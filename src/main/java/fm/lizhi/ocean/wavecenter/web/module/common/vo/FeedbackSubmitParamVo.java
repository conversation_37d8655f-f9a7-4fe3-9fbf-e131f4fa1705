package fm.lizhi.ocean.wavecenter.web.module.common.vo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/14 19:57
 */
@Data
public class FeedbackSubmitParamVo {

    @Range(min = 1, max = 2, message = "反馈类型错误")
    @NotNull
    private Integer typeId;

    @NotBlank(message = "反馈内容不能为空")
    @Length(max = 500, message = "反馈内容超字数")
    private String content;

    @Range(min = 1, max = 5, message = "满意度错误")
    @NotNull
    private Integer star;

    @NotBlank(message = "联系方式不能为空")
    private String contact;

    /**
     * 反馈的文件附件
     */
    private List<String> fileList;

}
