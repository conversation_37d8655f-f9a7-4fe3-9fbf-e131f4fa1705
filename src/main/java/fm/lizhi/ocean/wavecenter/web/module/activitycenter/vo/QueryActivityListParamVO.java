package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class QueryActivityListParamVO {

    /**
     * 分页页码
     */
    @NotNull(message = "分页参数不能空")
    @Min(1)
    private Integer pageNo;

    @NotNull(message = "每页数据最大不能超过500")
    @Max(500)
    private Integer pageSize;

    /**
     * 活动状态
     */
    private Integer activityStatus;

    /**
     * 最大活动开始时间
     */
    private Long maxStartTime;

    /**
     * 分类ID
     */
    private Long classId;

    /**
     * 活动结束时间
     */
    private Long minStartTime;

    /**
     * 活动名
     */
    private String name;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 活动审核状态
     */
    @Size(max = 5, message = "审核状态最多5个")
    private List<Integer> auditStatus;

    /**
     * 活动申请开始时间
     */
    private Long applyStartTime;

    /**
     * 活动申请结束时间
     */
    private Long applyEndTime;

    /**
     * 等级ID
     */
    private Long levelId;

    /**
     * 活动ID
     */
    private Long activityId;

}
