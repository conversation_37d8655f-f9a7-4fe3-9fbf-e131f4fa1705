package fm.lizhi.ocean.wavecenter.web.module.live.model.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import lombok.Data;

/**
 * 获取麦序福利厅详情参数
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetCheckInRoomDetailParam {

    /**
     * 厅主id
     */
    private Long njId;

    /**
     * 统计的时间类型
     */
    private CheckInDateTypeEnum dateType = CheckInDateTypeEnum.HOUR;

    /**
     * 开始时间毫秒时间戳, 包含
     */
    private Long startDate;

    /**
     * 结束时间毫秒时间戳, 包含
     */
    private Long endDate;
}
