package fm.lizhi.ocean.wavecenter.web.module.sign.convert;

import fm.lizhi.ocean.wavecenter.api.sign.bean.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseSignContractUrl;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.*;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16 15:18
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface SignConvert {

    SignConvert I = Mappers.getMapper(SignConvert.class);

    TodoSignVo todoSignBean2Vo(TodoSignBean bean);

    List<TodoSignVo> todoSignBeans2Vos(List<TodoSignBean> beans);

    TodoSignPlayerVo todoSignPlayerBean2Vo(TodoSignPlayerBean bean);

    List<TodoSignPlayerVo> todoSignPlayerBeans2Vos(List<TodoSignPlayerBean> beans);

    RoomSignRecordVo roomSignRecordBean2Vo(RoomSignRecordBean bean);

    List<RoomSignRecordVo> roomSignRecordBeans2Vos(List<RoomSignRecordBean> beans);

    CancelPlayerRecordVo cancelPlayerRecordBean2Vo(CancelPlayerRecordBean bean);

    List<CancelPlayerRecordVo> cancelPlayerRecordBeans2Vos(List<CancelPlayerRecordBean> beans);

    AdminSignPlayerRecordVo adminSignPlayerRecordBean2Vo(AdminSignPlayerRecordBean bean);

    List<AdminSignPlayerRecordVo> adminSignPlayerRecordBeans2Vos(List<AdminSignPlayerRecordBean> beans);

    CurrentActivityFlowVo actFlowBean2Vo(ActFlowBean bean);

    FlowInfoVO flowInfoBean2Vo(FlowInfoBean bean);

    MyFamilyVo myFamilyBean2Vo(MyFamilyBean bean);

    MyRoomVo myRoomBean2Vo(MyRoomBean bean);

    PlayerSignHistoryRecordVo playerSignHistoryRecordBean2Vo(PlayerSignHistoryRecordBean bean);

    List<PlayerSignHistoryRecordVo> playerSignHistoryRecordBeans2Vos(List<PlayerSignHistoryRecordBean> beans);

    SignContractUrlVO toSignContractUrlVO(ResponseSignContractUrl target);
}
