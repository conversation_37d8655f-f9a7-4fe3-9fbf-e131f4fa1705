package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;


import lombok.Data;

import java.util.Date;

@Data
public class RoomHourDetailVo implements IDetailEle{

    private Date time;

    private String income;

    private long charm;

    /**
     * 主持档
     */
    private boolean isHost;

    /**
     * 是否打卡
     */
    private boolean isCheck;

    @Override
    public Date foundTime() {
        return this.time;
    }

    @Override
    public String foundIncome() {
        return this.income;
    }

    @Override
    public Integer foundCharm() {
        return Math.toIntExact(this.charm);
    }

    @Override
    public Integer foundSeatOrder() {
        return null;
    }

    @Override
    public Integer foundHostCnt() {
        return isHost ? 1 : 0;
    }

    @Override
    public Integer foundCheckPlayerNumber() {
        return isCheck ? 1 : 0;
    }
}
