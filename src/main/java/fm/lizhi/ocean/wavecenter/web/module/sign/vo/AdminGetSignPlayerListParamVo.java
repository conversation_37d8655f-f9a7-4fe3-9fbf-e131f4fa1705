package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:11
 */
@Data
public class AdminGetSignPlayerListParamVo {

    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNo = 1;

    @Min(value = 1, message = "每页最大条数不能小于1")
    @Max(value = 100, message = "每页最大条数不能超过100")
    private Integer pageSize = 10;

    /**
     * 类型
     * SIGN=签约, CANCEL=解约
     */
    @NotBlank(message = "类型不能为空")
    private String type;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空")
    private String status;

    /**
     * 搜索用户波段号
     */
    private String userBand;

}
