package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BatchGetDecorateParamVO {


    /**
     * 类型 1：头像框，2：背景
     * @see DecorateEnum
     */
    private Integer type;

    /**
     * id 列表
     */
    private List<Long> ids;


}
