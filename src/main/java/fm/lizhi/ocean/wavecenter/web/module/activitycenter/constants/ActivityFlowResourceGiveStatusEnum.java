package fm.lizhi.ocean.wavecenter.web.module.activitycenter.constants;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceGiveStatusEnum;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * 资源发放状态
 * <AUTHOR>
 */
@Getter
public enum ActivityFlowResourceGiveStatusEnum {


    /**
     * 等待发放
     */
    WAIT_GIVE(0, Collections.singletonList(ActivityResourceGiveStatusEnum.WAIT_GIVE.getStatus())),

    /**
     * 发放失败
     */
    GIVE_FAIL(1, CollUtil.newArrayList(ActivityResourceGiveStatusEnum.GIVE_FAIL.getStatus())),

    /**
     * 发放成功
     */
    SUCCESS(2, Collections.singletonList(ActivityResourceGiveStatusEnum.SUCCESS.getStatus()))

    ;

    private final int status;

    private final List<Integer> bizStatus;


    ActivityFlowResourceGiveStatusEnum(int status, List<Integer> bizStatus) {
        this.status = status;
        this.bizStatus = bizStatus;
    }

    public static int getStatusByBizStatus(int bizStatus) {
        for (ActivityFlowResourceGiveStatusEnum status : ActivityFlowResourceGiveStatusEnum.values()) {
            if (status.getBizStatus().contains(bizStatus)){
                return status.getStatus();
            }
        }
        return WAIT_GIVE.getStatus();
    }
}
