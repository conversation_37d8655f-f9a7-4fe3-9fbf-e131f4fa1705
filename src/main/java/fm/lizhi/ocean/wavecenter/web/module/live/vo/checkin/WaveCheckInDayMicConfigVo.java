package fm.lizhi.ocean.wavecenter.web.module.live.vo.checkin;

import lombok.Data;

import java.util.List;

/**
 * 日麦序配置
 * <AUTHOR>
 */
@Data
public class WaveCheckInDayMicConfigVo {


    /**
     * 是否开启日麦序奖励规则
     */
    private Boolean enabled;

    /**
     * 计算类型.
     * 1=固定金额计算
     * 2=麦序计算
     */
    private Integer calcType;

    /**
     * 最高麦序奖励个数
     */
    private Integer maxCount;

    /**
     * 奖励阶梯列表
     */
    private List<WaveCheckInDayMicConfigLadderVo> ladders;

}