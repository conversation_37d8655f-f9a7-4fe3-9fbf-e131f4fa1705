package fm.lizhi.ocean.wavecenter.web.exception;


import fm.lizhi.ocean.wavecenter.web.common.IRCodes;

/**
 * 业务异常
 * <AUTHOR> generator
 */
public class AssertException extends RuntimeException {

    private final int code;
    private final String message;

    public AssertException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public AssertException(IRCodes irCodes) {
        this(irCodes.getCode(), irCodes.getMsg());
    }

    @Override
    public String getMessage() {
        return message;
    }

    public int getCode() {
        return code;
    }

}