package fm.lizhi.ocean.wavecenter.web.module.live.vo;

import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.RoomVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.*;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GuildAuditRecordStatsVo {

    /**
     * 签约厅的信息
     */
    private RoomVo roomBean;


    /**
     * 违规人数
     */
    private Integer pushPeopleNumber;

    /**
     * 违规人次
     */
    private Integer pushNumber;

}
