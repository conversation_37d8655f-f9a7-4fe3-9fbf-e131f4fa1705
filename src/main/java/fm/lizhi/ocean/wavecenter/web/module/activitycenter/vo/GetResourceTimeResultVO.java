package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResourceTimeBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GetResourceTimeResultVO {

    /**
     * 官频位最大数量
     */
    private int maxOfficialSeatHallCount;

    /**
     * 资源时间表
     */
    private List<ResourceTimeVO> timeInfoList;


}
