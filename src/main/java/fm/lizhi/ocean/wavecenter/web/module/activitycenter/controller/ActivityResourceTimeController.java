package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestGetResourceTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseGetResourceTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityResourceTimeService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityApplyConvert;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.GetResourceTimeParamVO;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.GetResourceTimeResultVO;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.ResourceTimeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/activity")
public class ActivityResourceTimeController {

    @Autowired
    private ActivityResourceTimeService activityResourceTimeService;

    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER})
    @GetMapping("/resource/timeList")
    public ResultVO<GetResourceTimeResultVO> getResourceTimeList(@Validated GetResourceTimeParamVO param) {
        RequestGetResourceTimeBean request = new RequestGetResourceTimeBean()
                .setSeat(param.getSeat())
                .setTemplateId(param.getTemplateId())
                .setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        if (param.getStartDate() != null) {
            request.setStartDate(new Date(param.getStartDate()));
        }
        if (param.getEndDate() != null) {
            request.setEndDate(new Date(param.getEndDate()));
        }
        Result<ResponseGetResourceTimeBean> result = activityResourceTimeService.getResourceTimeList(request);
        if (RpcResult.isFail(result)) {
            log.error("getResourceTimeList fail, result={}", result);
            return ResultVO.failure("查询时间表失败");
        }

        List<ResourceTimeVO> lists = ActivityApplyConvert.I.convertTimeInfoListBeans2VOList(result.target().getTimeInfoList());
        GetResourceTimeResultVO timeResultVO = new GetResourceTimeResultVO();
        timeResultVO.setTimeInfoList(lists);
        timeResultVO.setMaxOfficialSeatHallCount(result.target().getMaxOfficialSeatHallCount());
        return ResultVO.success(timeResultVO);
    }

}
