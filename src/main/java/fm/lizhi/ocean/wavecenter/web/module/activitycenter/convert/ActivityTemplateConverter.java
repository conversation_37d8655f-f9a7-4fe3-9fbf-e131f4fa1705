package fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateHotPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageHotActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.PageGeneralActivityTemplateParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.PageHotActivityTemplateParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.GetGeneralActivityTemplateResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.PageGeneralActivityTemplateResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.PageHotActivityTemplateResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.ActivityTemplateFlowResourceVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 活动模板转换器, 主要是将WEB请求参数转换为DC请求参数, 处理一些通过header传递的参数
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityTemplateConverter {

    /**
     * 热门活动模板分页查询WEB请求参数转换为热门活动模板分页查询DC请求参数
     *
     * @param param  热门活动模板分页查询WEB请求参数
     * @param appId  应用id
     * @param userId
     * @return 通用活动模板分页查询DC请求参数
     */
    @Mapping(target = "categoryValue", ignore = true)
    RequestPageHotActivityTemplate toRequestPageHotActivityTemplate(PageHotActivityTemplateParam param, Integer appId, long userId);

    /**
     * 分页查询热门活动模板结果转换为分页查询热门活动模板结果VO
     *
     * @param pageBean 分页查询热门活动模板结果
     * @return 分页查询热门活动模板结果VO
     */
    PageVO<PageHotActivityTemplateResult> toPageVOPageHotActivityTemplateResult(PageBean<ActivityTemplateHotPageBean> pageBean);

    PageHotActivityTemplateResult toPageHotActivityTemplateResult(ActivityTemplateHotPageBean bean);

    /**
     * 通用活动模板分页查询WEB请求参数转换为通用活动模板分页查询DC请求参数
     *
     * @param param  通用活动模板分页查询WEB请求参数
     * @param appId  应用id
     * @return 通用活动模板分页查询DC请求参数
     */
    @Mapping(target = "njIds", ignore = true)
    RequestPageGeneralActivityTemplate toRequestPageGeneralActivityTemplate(PageGeneralActivityTemplateParam param, Integer appId, long requestUserId);

    /**
     * 分页查询通用活动模板结果转换为分页查询通用活动模板结果VO
     *
     * @param pageBean 分页查询通用活动模板结果
     * @return 分页查询通用活动模板结果VO
     */
    PageVO<PageGeneralActivityTemplateResult> toPageVOPageGeneralActivityTemplateResult(PageBean<ActivityTemplateGeneralPageBean> pageBean);

    ActivityTemplateFlowResourceVO toActivityTemplateFlowResourceVO(ActivityTemplateFlowResourceBean bean);

    List<ActivityTemplateFlowResourceVO> toActivityTemplateFlowResourceVOs(List<ActivityTemplateFlowResourceBean> beans);

    PageGeneralActivityTemplateResult toPageGeneralActivityTemplateResult(ActivityTemplateGeneralPageBean bean);

    /**
     * 获取通用活动模板结果转换为获取通用活动模板结果VO
     *
     * @param bean 获取通用活动模板结果
     * @return 获取通用活动模板结果VO
     */
    GetGeneralActivityTemplateResult toGetGeneralActivityTemplateResult(ResponseGetGeneralActivityTemplate bean);
}
