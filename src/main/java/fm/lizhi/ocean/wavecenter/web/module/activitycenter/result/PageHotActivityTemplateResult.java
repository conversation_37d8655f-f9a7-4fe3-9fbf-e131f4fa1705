package fm.lizhi.ocean.wavecenter.web.module.activitycenter.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.ActivityTemplateFlowResourceVO;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.HighlightVO;
import lombok.Data;

import java.util.List;

/**
 * 分页查询热门活动模板结果
 */
@Data
public class PageHotActivityTemplateResult {

    /**
     * 活动模板id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 热门推荐权重
     */
    private Integer hotWeight;

    /**
     * 使用次数
     */
    private String usageCount;

    /**
     * 流量资源
     */
    private List<ActivityTemplateFlowResourceVO> flowResources;

    /**
     * 活动模板亮点标签列表
     */
    private List<HighlightVO> highlights;
}
