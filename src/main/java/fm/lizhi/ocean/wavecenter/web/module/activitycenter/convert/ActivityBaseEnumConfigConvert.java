package fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityBaseConfigResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityConfigResult;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityBaseEnumConfigConvert {

    ActivityBaseEnumConfigConvert I = Mappers.getMapper(ActivityBaseEnumConfigConvert.class);


    ActivityBaseConfigResult toActivityBaseConfigResult(ResponseActivityConfigBean target);

    ActivityConfigResult configResp2Result(ResponseActivityConfig resp);
}
