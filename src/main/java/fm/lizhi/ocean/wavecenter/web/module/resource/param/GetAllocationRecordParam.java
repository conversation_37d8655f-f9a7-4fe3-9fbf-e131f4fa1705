package fm.lizhi.ocean.wavecenter.web.module.resource.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/14 20:07
 */
@Data
public class GetAllocationRecordParam {

    private Long njId;

    private Integer pageNo = 1;

    private Integer pageSize = 20;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date day;

}
