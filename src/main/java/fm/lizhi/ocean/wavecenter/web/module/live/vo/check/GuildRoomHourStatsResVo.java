package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;


import fm.lizhi.ocean.wavecenter.web.module.user.vo.RoomVo;
import lombok.Data;

import java.util.List;

@Data
public class GuildRoomHourStatsResVo implements IDetailList<GuildRoomHourDetailVo>{


    private RoomVo room;

    /**
     * 合计
     */
    private GuildRoomHourStatsVo stats;

    /**
     * 明细
     */
    private List<GuildRoomHourDetailVo> detail;

    @Override
    public List<GuildRoomHourDetailVo> foundDetail() {
        return this.detail;
    }
}
