package fm.lizhi.ocean.wavecenter.web.module.datacenter.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.RankDataService;
import fm.lizhi.ocean.wavecenter.api.live.bean.PlayerSmsStatBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.convert.DataCenterConvert;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.RankGetRoomParamVo;
import fm.lizhi.ocean.wavecenter.web.module.live.convert.LiveSmsConvert;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.LiveSmsPlayerExportVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/22 20:24
 */
@Slf4j
@RestController
@RequestMapping("rank")
public class RankController {

    @Autowired
    private RankDataService rankDataService;
    @Autowired
    private DataScopeHandler dataScopeHandler;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private FileExportHandler fileExportHandler;

    /**
     * 主播排行榜
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("sign/player")
    public ResultVO<PageVO<PlayerRankVo>> signPlayer(@Validated GetSignPlayerParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long roomId = dataScopeHandler.getRoomForRoomOrDefault(paramVo.getRoomId());
        long userId = ContextUtils.getContext().getUserId();
        if (familyId == null) {
            return ResultVO.failure("当前厅未签约");
        }

        GetSignPlayerParamBean.GetSignPlayerParamBeanBuilder builder = GetSignPlayerParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(userId)
                .familyId(familyId)
                .roomId(roomId)
                .roomIds(ContextUtils.getContext().getRoomResource())
                .orderMetrics(paramVo.getOrderMetrics())
                .orderType(OrderType.fromValue(paramVo.getOrderType()))
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .startDate(DateUtil.parse(paramVo.getStartDate(), DatePattern.NORM_DATE_PATTERN))
                .pageNo(paramVo.getPageNo())
                .filterZero(paramVo.getFilterZero().equals(1))
                .pageSize(paramVo.getPageSize());
        if (StringUtils.isNotBlank(paramVo.getEndDate())) {
            builder.endDate(DateUtil.parse(paramVo.getEndDate(), DatePattern.NORM_DATE_PATTERN));
        }
        GetSignPlayerParamBean request = builder.build();
        Result<PageBean<PlayerRankBean>> result = rankDataService.signPlayer(request);
        if (RpcResult.isFail(result)) {
            log.error("signPlayer fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return ResultVO.failure();
        }

        PageBean<PlayerRankBean> target = result.target();
        List<PlayerRankVo> list = DataCenterConvert.I.playerRankBeans2Vos(target.getList());
        return ResultVO.success(PageVO.of(target.getTotal(), list));
    }

    /**
     * 签约厅排行榜
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/sign/room")
    public ResultVO<PageVO<RoomRankVo>> signRoom(@Validated GetSignRoomParamVo paramVo) {
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long roomId = dataScopeHandler.getRoomForRoomOrDefault(null);
        long userId = ContextUtils.getContext().getUserId();
        if (familyId == null) {
            return ResultVO.failure("当前厅未签约");
        }

        GetSignRoomParamBean.GetSignRoomParamBeanBuilder builder = GetSignRoomParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(userId)
                .familyId(familyId)
                .roomId(roomId)
                .roomIds(ContextUtils.getContext().getRoomResource())
                .orderMetrics(paramVo.getOrderMetrics())
                .orderType(OrderType.fromValue(paramVo.getOrderType()))
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .startDate(DateUtil.parse(paramVo.getStartDate(), DatePattern.NORM_DATE_PATTERN))
                .pageNo(paramVo.getPageNo())
                .filterZero(paramVo.getFilterZero().equals(1))
                .pageSize(paramVo.getPageSize());
        if (StringUtils.isNotBlank(paramVo.getEndDate())) {
            builder.endDate(DateUtil.parse(paramVo.getEndDate(), DatePattern.NORM_DATE_PATTERN));
        }
        GetSignRoomParamBean request = builder.build();
        Result<PageBean<RoomRankBean>> result = rankDataService.signRoom(request);
        if (RpcResult.isFail(result)) {
            log.error("signRoom fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return ResultVO.failure();
        }

        PageBean<RoomRankBean> target = result.target();
        List<RoomRankVo> list = DataCenterConvert.I.roomRankBeans2Vos(target.getList());
        return ResultVO.success(PageVO.of(target.getTotal(), list));
    }

    /**
     * 工会数据-主播业绩龙虎榜
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("data/guild/player")
    public ResultVO<GuildPlayerRankResVo> dataGuildPlayer(@Validated RankGetGuildPlayerParamVo paramVo) {
        if (!checkDayRange(paramVo.getDate())) {
            return ResultVO.failure("仅可查看30天以内的数据");
        }

        RankGetGuildPlayerParamBean request = RankGetGuildPlayerParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(ContextUtils.getContext().getSubjectId())
                .roomIds(ContextUtils.getContext().getRoomResource())
                .date(paramVo.getDate())
                .rankType(OrderType.fromValue(paramVo.getRankType()))
                .build();
        Result<GuildPlayerRankResBean> result = rankDataService.guildPlayer(request);
        if (RpcResult.isFail(result)) {
            log.error("guildPlayer fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return ResultVO.failure();
        }

        GuildPlayerRankResVo guildPlayerRankResVo = new GuildPlayerRankResVo();
        guildPlayerRankResVo.setDate(result.target().getDate());
        guildPlayerRankResVo.setRanks(DataCenterConvert.I.rankBeans2Vos(result.target().getRanks()));
        return ResultVO.success(guildPlayerRankResVo);
    }


    /**
     * 厅数据-主播业绩龙虎榜
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.PLAYER, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("data/room/player")
    public ResultVO<RoomPlayerRankResVo> dataRoomPlayer(@Validated RankGetRoomPlayerParamVo paramVo) {
        if (!checkDayRange(paramVo.getDate())) {
            return ResultVO.failure("仅可查看30天以内的数据");
        }
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }
        if (!dataScopeHandler.checkParamPlayerId(paramVo.getPlayerId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Long familyId = dataScopeHandler.getFamilyForBase();
        if (familyId == null) {
            return ResultVO.failure("未签约家族");
        }

        Long queryRoomId = dataScopeHandler.getRoomForBase(paramVo.getRoomId(), paramVo.getPlayerId());
        if (queryRoomId == null) {
            return ResultVO.failure("请选择厅");
        }

        RankGetRoomPlayerParamBean request = RankGetRoomPlayerParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(familyId)
                .roomId(queryRoomId)
                .rankType(OrderType.fromValue(paramVo.getRankType()))
                .date(paramVo.getDate())
                .build();
        Result<RoomPlayerRankResBean> result = rankDataService.roomPlayer(request);
        if (RpcResult.isFail(result)) {
            log.error("roomPlayer fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return ResultVO.failure();
        }

        RoomPlayerRankResVo roomPlayerRankResVo = new RoomPlayerRankResVo();
        roomPlayerRankResVo.setDate(result.target().getDate());
        roomPlayerRankResVo.setRanks(DataCenterConvert.I.rankBeans2Vos(result.target().getRanks()));
        return ResultVO.success(roomPlayerRankResVo);
    }


    /**
     * 公会数据-厅-业绩 龙虎版
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("data/room")
    public ResultVO<RankRoomResVo> dataRoom(@Validated RankGetRoomParamVo paramVo) {
        if (!checkDayRange(paramVo.getDate())) {
            return ResultVO.failure("仅可查看30天以内的数据");
        }
        RankRoomResVo vo = new RankRoomResVo();
        vo.setDate(DateUtil.parse(paramVo.getDate(), DatePattern.NORM_DATE_PATTERN));

        RankGetRoomParamBean request = RankGetRoomParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .rankType(OrderType.fromValue(paramVo.getRankType()))
                .date(paramVo.getDate())
                .familyId(ContextUtils.getContext().getSubjectId())
                .roomIds(ContextUtils.getContext().getRoomResource())
                .build();
        Result<List<RankRoomBean>> result = rankDataService.room(request);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("rankDataService.room fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            vo.setRanks(Collections.emptyList());
            return ResultVO.success(vo);
        }
        List<RankRoomVo> rankRoomVos = result.target().stream().map(e -> {
            RankRoomVo rankRoomVo = new RankRoomVo();
            rankRoomVo.setRoom(UserCommonConvert.I.userBean2Vo(e.getRoomInfo()));
            rankRoomVo.setIncome(CalculateUtil.formatDecimal(e.getIncome()));
            return rankRoomVo;
        }).collect(Collectors.toList());
        vo.setRanks(rankRoomVos);
        return ResultVO.success(vo);
    }

    /**
     * 校验日期范围
     *
     * @param dateStr 日期字符串
     * @return 结果
     */
    private boolean checkDayRange(String dateStr) {
        if (StringUtils.isNotBlank(dateStr)) {
            Date date = DateUtil.parse(dateStr, DatePattern.NORM_DATE_PATTERN);
            long betweenDay = DateUtil.betweenDay(date, new Date(), true);
            return betweenDay <= appConfig.getMaxRankQueryDayRange();
        }
        return true;
    }


    /**
     * 签约厅排行榜-导出
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/sign/room/export")
    public ResultVO<Void> signRoomExport(@Validated GetSignRoomExportParamVo paramVo) {
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long roomId = dataScopeHandler.getRoomForRoomOrDefault(null);
        long userId = ContextUtils.getContext().getUserId();
        if (familyId == null) {
            return ResultVO.failure("当前厅未签约");
        }

        GetSignRoomParamBean.GetSignRoomParamBeanBuilder builder = GetSignRoomParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(userId)
                .familyId(familyId)
                .roomId(roomId)
                .roomIds(ContextUtils.getContext().getRoomResource())
                .orderMetrics(paramVo.getOrderMetrics())
                .orderType(OrderType.fromValue(paramVo.getOrderType()))
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .startDate(DateUtil.parse(paramVo.getStartDate(), DatePattern.NORM_DATE_PATTERN))
                .filterZero(paramVo.getFilterZero().equals(1));
        if (StringUtils.isNotBlank(paramVo.getEndDate())) {
            builder.endDate(DateUtil.parse(paramVo.getEndDate(), DatePattern.NORM_DATE_PATTERN));
        }

        HashSet<String> includeFields = null;
        if (CollUtil.isNotEmpty(paramVo.getMetrics())) {
            includeFields = new HashSet<>(paramVo.getMetrics());
            includeFields.add(RoomRankExportVo.Fields.roomName);
            includeFields.add(RoomRankExportVo.Fields.njId);
            includeFields.add(RoomRankExportVo.Fields.familyId);
        }

        String fileName = "签约厅排行榜_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, RoomRankExportVo.class,
                (pageNo, pageSize) -> {
                    builder.pageNo(pageNo);
                    builder.pageSize(pageSize);
                    GetSignRoomParamBean request = builder.build();
                    Result<PageBean<RoomRankBean>> result = rankDataService.signRoom(request);
                    if (RpcResult.isFail(result)) {
                        log.error("signRoom fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
                        return PageVO.empty();
                    }
                    PageBean<RoomRankBean> target = result.target();
                    List<RoomRankExportVo> exportVos = DataCenterConvert.I.convertRoomRankExportVos(target.getList());
                    return PageVO.of(target.getTotal(), exportVos);
                }, includeFields);
    }

    /**
     * 主播排行榜-导出
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("sign/player/export")
    public ResultVO<Void> signPlayerExport(@Validated GetSignPlayerExportParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long roomId = dataScopeHandler.getRoomForRoomOrDefault(paramVo.getRoomId());
        long userId = ContextUtils.getContext().getUserId();
        if (familyId == null) {
            return ResultVO.failure("当前厅未签约");
        }

        GetSignPlayerParamBean.GetSignPlayerParamBeanBuilder builder = GetSignPlayerParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(userId)
                .familyId(familyId)
                .roomId(roomId)
                .roomIds(ContextUtils.getContext().getRoomResource())
                .orderMetrics(paramVo.getOrderMetrics())
                .orderType(OrderType.fromValue(paramVo.getOrderType()))
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .startDate(DateUtil.parse(paramVo.getStartDate(), DatePattern.NORM_DATE_PATTERN))
                .filterZero(paramVo.getFilterZero().equals(1));
        if (StringUtils.isNotBlank(paramVo.getEndDate())) {
            builder.endDate(DateUtil.parse(paramVo.getEndDate(), DatePattern.NORM_DATE_PATTERN));
        }

        HashSet<String> includeFields = null;
        if (CollUtil.isNotEmpty(paramVo.getMetrics())) {
            includeFields = new HashSet<>(paramVo.getMetrics());
            includeFields.add(PlayerRankExportVo.Fields.roomName);
            includeFields.add(PlayerRankExportVo.Fields.njId);
            includeFields.add(PlayerRankExportVo.Fields.playerName);
            includeFields.add(PlayerRankExportVo.Fields.playerBand);
        }

        String fileName = "主播排行榜_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, PlayerRankExportVo.class,
                (pageNo, pageSize) -> {
                    builder.pageNo(pageNo);
                    builder.pageSize(pageSize);
                    GetSignPlayerParamBean request = builder.build();
                    Result<PageBean<PlayerRankBean>> result = rankDataService.signPlayer(request);
                    if (RpcResult.isFail(result)) {
                        log.error("signPlayer fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
                        return PageVO.empty();
                    }
                    PageBean<PlayerRankBean> target = result.target();
                    List<PlayerRankExportVo> exportVos = DataCenterConvert.I.convertPlayerRankExportVos(target.getList());
                    return PageVO.of(target.getTotal(), exportVos);
                }, includeFields);

    }

}
