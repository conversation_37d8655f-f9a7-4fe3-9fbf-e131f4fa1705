package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import fm.lizhi.ocean.wavecenter.web.common.BigDecimalDownSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/25 11:06
 */
@Data
@Accessors(chain = true)
public class TimeStatsVo {

    private Date time;

    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal income;

    private Integer charm;

    private Integer seatOrder;

    private Integer hostCnt;

    private Integer checkPlayerNumber;


}
