package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 14:19
 */
@Data
public class RoomGetKeyIndicatorsParamVo {

    @NotBlank(message = "时间类型不可为空")
    private String dateType;

    @NotBlank(message = "开始时间不可为空")
    private String startDate;

    private String endDate;

    private List<String> valueMetrics;

    private List<String> ratioMetrics;

    private Long roomId;

}
