package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserInfoVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/21 19:19
 */
@Data
public class GuildSignRoomVo {

    /**
     * 厅信息
     */
    private UserInfoVo roomInfo;

    private Integer signStatus;

    /**
     * 签约时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signDate;

    /**
     * 解约时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date expireDate;

    /**
     * 解约时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date stopDate;

    /**
     * 签约主播
     */
    private Integer signPlayerCnt;

    /**
     * 合同ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

}
