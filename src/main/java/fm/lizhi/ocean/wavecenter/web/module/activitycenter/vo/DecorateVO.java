package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import lombok.Data;

/**
 * 装扮
 * <AUTHOR>
 */
@Data
public class DecorateVO {

    private String id;

    /**
     * 类型 1：头像框，2：背景
     * @see DecorateEnum
     */
    private Integer type;

    /**
     * 物品名称
     */
    private String name;

    /**
     * 预览 URL
     */
    private String previewUrl;

    /**
     * 业务侧的装扮扩展信息，直接透传。
     */
    private String extInfo;



}
