package fm.lizhi.ocean.wavecenter.web.module.common.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.AddEvaluateRecordReq;
import fm.lizhi.ocean.wavecenter.api.common.bean.GetEvaluateRecordCountReq;
import fm.lizhi.ocean.wavecenter.api.common.service.FeedbackService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.lark.FeedbackLarkDto;
import fm.lizhi.ocean.wavecenter.web.common.lark.LarkClient;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.common.vo.EvaluateParamVo;
import fm.lizhi.ocean.wavecenter.web.module.common.vo.FeedbackSubmitParamVo;
import fm.lizhi.ocean.wavecenter.web.module.common.vo.OperationContactVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/14 19:51
 */
@Slf4j
@RestController
@RequestMapping("/common/feedback")
public class FeedbackController {

    @Autowired
    private LarkClient larkClient;
    @Autowired
    private UserCommonService userCommonService;
    @Autowired
    private FeedbackService feedbackService;
    @Autowired
    private AppConfig appConfig;

    /**
     * 是否展示意见反馈
     * @return
     */
    @VerifyUserToken
    @GetMapping("showEvaluate")
    public ResultVO<Boolean> showEvaluate(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();

        Result<Long> result = feedbackService.getEvaluateRecordCount(GetEvaluateRecordCountReq.builder()
                .appId(appId)
                .userId(userId)
                .build());

        if (RpcResult.isFail(result)) {
            log.warn("getEvaluateRecordCount fail. appId={}, userId={}, rCode={}", appId, userId, result.rCode());
            return ResultVO.failure();
        }

        Long count = result.target();
        if (count == null) {
            return ResultVO.success(true);
        }

        return ResultVO.success(count <= 0);
    }

    /**
     * 推荐评分-提交
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @PostMapping("evaluate")
    public ResultVO<Void> evaluate(@Validated @RequestBody EvaluateParamVo paramVo){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();

        Result<UserBean> userInfoResult = userCommonService.getUserById(appId, userId);
        if (RpcResult.isFail(userInfoResult)) {
            return ResultVO.failure("获取用户信息失败");
        }
        UserBean userBean = userInfoResult.target();
        FeedbackLarkDto feedbackLarkDto = new FeedbackLarkDto();
        BeanUtils.copyProperties(paramVo, feedbackLarkDto);
        feedbackLarkDto.setUserId(userId)
                .setAppName(ContextUtils.getBusinessEvnEnum().getName())
                .setBand(userBean.getBand())
                .setNickName(userBean.getName())
                .setSource("推荐评分")
        ;
        larkClient.submitFeedback(feedbackLarkDto);

        //保存记录
        feedbackService.addEvaluateRecord(AddEvaluateRecordReq.builder()
                .appId(appId)
                .userId(userId)
                .build());

        return ResultVO.success();
    }

    /**
     * 意见反馈-提交
     * @return
     */
    @VerifyUserToken
    @PostMapping("submit")
    public ResultVO<Void> submit(@Validated @RequestBody FeedbackSubmitParamVo paramVo){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();

        Result<UserBean> userInfoResult = userCommonService.getUserById(appId, userId);
        if (RpcResult.isFail(userInfoResult)) {
            return ResultVO.failure("获取用户信息失败");
        }

        UserBean userBean = userInfoResult.target();
        FeedbackLarkDto feedbackLarkDto = new FeedbackLarkDto();
        BeanUtils.copyProperties(paramVo, feedbackLarkDto);
        feedbackLarkDto.setUserId(userId)
                .setAppName(ContextUtils.getBusinessEvnEnum().getName())
                .setBand(userBean.getBand())
                .setNickName(userBean.getName())
                .setFileListStr(String.join(",", paramVo.getFileList()))
                .setSource("web意见反馈")
        ;
        larkClient.submitFeedback(feedbackLarkDto);
        return ResultVO.success();
    }


    /**
     * 获取内容活动中心的运营联系方式
     */
    @VerifyUserToken
    @GetMapping("/getOperationContacts")
    public ResultVO<List<OperationContactVO>> getOperationContacts(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String operationContacts = appConfig.getOperationContacts();

        Map<Integer, List<OperationContactVO>> operationContactsMap = JSON.parseObject(operationContacts, new TypeReference<Map<Integer, List<OperationContactVO>>>(){});
        return ResultVO.success(operationContactsMap.get(appId));
    }


}
