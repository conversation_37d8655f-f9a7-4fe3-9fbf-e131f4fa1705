package fm.lizhi.ocean.wavecenter.web.module.home.result;


import com.fasterxml.jackson.annotation.JsonFormat;
import fm.lizhi.ocean.wavecenter.web.module.home.vo.TrendChartVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 公会关键数据-趋势图
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GuildKeyDataTrendChartResult {


    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;


    /**
     * 总收入
     */
    private TrendChartVO sumIncome;

    /**
     * 上麦主播数
     */
    private TrendChartVO signUpGuestPlayerCnt;

    /**
     * 厅均收入
     */
    private TrendChartVO roomAvgIncome;

    /**
     * 人均收入
     */
    private TrendChartVO playerAvgIncome;

    /**
     * 上麦主播数
     */
    private TrendChartVO incomePlayerCnt;
}
