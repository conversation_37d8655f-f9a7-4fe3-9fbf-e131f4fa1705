package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 当前激活流程信息
 * <AUTHOR>
 * @date 2024/10/5 09:56
 */
@Data
@Accessors(chain = true)
public class CurrentActivityFlowVo {

    /**
     * 流程信息
     */
    private FlowInfoVO flowInfo;

    /**
     * 家族信息 仅和家族签署才有
     */
    private MyFamilyVo familyInfo;

    /**
     * 当前签署信息
     */
    private MyFamilyVo signedFamilyInfo;

    /**
     * 厅信息 仅和厅签署才有
     */
    private MyRoomVo roomInfo;

    /**
     * 当前签署信息
     */
    private MyRoomVo signedRoomInfo;

}
