package fm.lizhi.ocean.wavecenter.web.common.context;

import lombok.Data;
import org.codehaus.plexus.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/16
 */
@Data
public class Header {

    private String ip;

    private Integer appId;

    private String deviceId;

    private String deviceType = "web";

    private String clientVersion = "1";

    private String accessToken;

    private String roleCode;

    private Long subjectId;

    /**
     * 登录方式
     * 1=本人登录，2=授权登录
     */
    private Integer loginType;

    /**
     * 业务用户token
     * 用于手机扫码等场景
     */
    private String lzToken;


    public static Header build(HttpServletRequest request) {
        Header header = new Header();
        header.setIp(getIPAddress(request));

        String appId = request.getHeader("appId");
        if (StringUtils.isNotBlank(appId)) {
            header.setAppId(Integer.parseInt(appId));
        } else {
            header.setAppId(0);
        }
        header.setAccessToken(request.getHeader("accessToken"));
        header.setLzToken(request.getHeader("lz-token"));
        header.setDeviceId(request.getHeader("deviceId"));
        header.setDeviceType(request.getHeader("deviceType"));
        header.setClientVersion(request.getHeader("clientVersion"));
        header.setRoleCode(request.getHeader("roleCode"));
        String subjectIdStr = request.getHeader("subjectId");
        if (StringUtils.isNotBlank(subjectIdStr)) {
            header.setSubjectId(Long.valueOf(subjectIdStr));
        }
        String loginType = request.getHeader("loginType");
        if (StringUtils.isNotBlank(loginType)) {
            header.setLoginType(Integer.valueOf(loginType));
        }
        return header;
    }

    public static String getIPAddress(HttpServletRequest request) {
        String ip = null;

        //X-Forwarded-For：Squid 服务代理
        String ipAddresses = request.getHeader("X-Forwarded-For");

        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //Proxy-Client-IP：apache 服务代理
            ipAddresses = request.getHeader("Proxy-Client-IP");
        }

        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //WL-Proxy-Client-IP：weblogic 服务代理
            ipAddresses = request.getHeader("WL-Proxy-Client-IP");
        }

        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //HTTP_CLIENT_IP：有些代理服务器
            ipAddresses = request.getHeader("HTTP_CLIENT_IP");
        }

        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //X-Real-IP：nginx服务代理
            ipAddresses = request.getHeader("X-Real-IP");
        }

        //有些网络通过多层代理，那么获取到的ip就会有多个，一般都是通过逗号（,）分割开来，并且第一个ip为客户端的真实IP
        if (ipAddresses != null && ipAddresses.length() != 0) {
            ip = ipAddresses.split(",")[0];
        }

        //还是不能获取到，最后再通过request.getRemoteAddr();获取
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

}
