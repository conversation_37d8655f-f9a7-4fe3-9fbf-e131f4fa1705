package fm.lizhi.ocean.wavecenter.web.module.live.handler;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class RedisHandler {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;


    public RedisClient getRedisClient() {
        return redisClient;
    }
}
