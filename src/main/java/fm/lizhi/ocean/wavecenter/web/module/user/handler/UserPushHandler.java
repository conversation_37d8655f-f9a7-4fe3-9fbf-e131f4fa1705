package fm.lizhi.ocean.wavecenter.web.module.user.handler;

import fm.lizhi.ocean.wavecenter.web.common.push.AbsRomePush;
import fm.lizhi.ocean.wavecenter.web.common.push.PushTopic;
import fm.lizhi.ocean.wavecenter.web.common.push.PushVo;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/4/23 17:37
 */
@Component
public class UserPushHandler extends AbsRomePush {

    /**
     * 用户设备通知
     * @param appId
     * @param userId
     * @param deviceId
     */
    public void alterDevicePush(int appId, long userId, String deviceId, PushVo<?> pushVo){
        String topic = PushTopic.USER_DEVICE_PUSH.getKey(appId, userId, deviceId);
        pushMessage(topic, pushVo);
    }


}
