package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 个人收入
 * <AUTHOR>
 * @date 2024/4/24 14:47
 */
@Data
public class GetPersonalIncomeDetailParamVo extends GetDetailBaseParamVo {



    @Min(value = 1)
    private Integer pageNo;

    @Min(value = 1)
    @Max(value = 50)
    private Integer pageSize;


    private Long flushTime = System.currentTimeMillis();

}
