package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import fm.lizhi.ocean.wavecenter.web.module.user.vo.PlayerSignVo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/17 16:48
 */
@Data
@Accessors(chain = true)
public class PlayerAssessmentInfoVo extends AssessmentTimeVo{

    private PlayerSignVo playerInfo;

    /**
     * 刷新时间
     */
    private Long flushTime;

    /**
     * 总收入
     */
    private IncomeVo sumIncome;

    /**
     * 总魅力值
     */
    private CharmRatioVo sumCharm;

    /**
     * 签约厅收礼收入
     */
    private IncomeVo roomIncome;

    /**
     * 厅收礼魅力值
     */
    private CharmRatioVo roomCharm;

    /**
     * 个播收入
     */
    private IncomeVo individualIncome;

    /**
     * 个播魅力值
     */
    private CharmRatioVo individualCharm;

    /**
     * 官方厅收礼收入
     */
    private IncomeVo officialIncome;

    /**
     * 官方厅收礼魅力值
     */
    private CharmRatioVo officialCharm;

}
