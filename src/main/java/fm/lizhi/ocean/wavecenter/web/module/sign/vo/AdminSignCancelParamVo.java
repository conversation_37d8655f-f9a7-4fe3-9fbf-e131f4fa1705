package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/10/5 16:08
 */
@Data
public class AdminSignCancelParamVo {

    /**
     * 签约记录ID
     */
    @NotNull(message = "签约记录ID不可为空")
    private Long contractId;

    /**
     * 签署意见
     * AGREE REJECT
     */
    @NotBlank(message = "签署意见不可为空")
    private String operateType;

}
