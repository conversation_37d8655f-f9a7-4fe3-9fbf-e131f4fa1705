package fm.lizhi.ocean.wavecenter.web.module.resource.param;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/14 19:46
 */
@Data
@Accessors(chain = true)
public class AllocationRecommendCardParam {

    /**
     * 任务列表
     */
    @NotEmpty(message = "任务列表不可为空")
    private List<Task> tasks;

    @Data
    public static class Task {
        /**
         * 推荐卡数量
         */
        @NotNull(message = "推荐卡数不可为空")
        private Integer num;

        /**
         * 目标用户ID
         */
        @NotBlank(message = "目标用户ID不可为空")
        private Long targetUserId;
    }


}
