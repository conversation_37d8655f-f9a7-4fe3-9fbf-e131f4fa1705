package fm.lizhi.ocean.wavecenter.web.module.award.family.convert;

import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.web.module.award.family.param.GetFamilyWeekAwardV1Param;
import fm.lizhi.ocean.wavecenter.web.module.award.family.param.GetFamilyWeekAwardV2Param;
import fm.lizhi.ocean.wavecenter.web.module.award.family.vo.GetFamilyWeekAwardV1VO;
import fm.lizhi.ocean.wavecenter.web.module.award.family.vo.GetFamilyWeekAwardV2VO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface FamilyAwardConvert {

    FamilyAwardConvert I = Mappers.getMapper(FamilyAwardConvert.class);

    RequestGetFamilyWeekAwardV1 toRequestGetFamilyWeekAwardV1(GetFamilyWeekAwardV1Param param, Integer appId, Long familyId);

    GetFamilyWeekAwardV1VO toGetFamilyWeekAwardV1VO(ResponseGetFamilyWeekAwardV1 response);

    RequestGetFamilyWeekAwardV2 toRequestGetFamilyWeekAwardV2(GetFamilyWeekAwardV2Param param, Integer appId, Long familyId);

    GetFamilyWeekAwardV2VO toGetFamilyWeekAwardV2VO(ResponseGetFamilyWeekAwardV2 response);
}
