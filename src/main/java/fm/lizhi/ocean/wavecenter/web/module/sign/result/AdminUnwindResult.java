package fm.lizhi.ocean.wavecenter.web.module.sign.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/26 14:09
 */
@Data
@Accessors(chain = true)
public class AdminUnwindResult {

    private String contractUrl;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

}
