package fm.lizhi.ocean.wavecenter.web.common;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * 小数数值序列化
 * <AUTHOR>
 * @date 2024/4/27 10:37
 */
public class BigDecimalDownSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        String valueStr = "";
        if (value == null) {
            valueStr = CalculateUtil.formatDecimal("0");
        } else {
            valueStr = CalculateUtil.formatDecimal(value);
        }
        gen.writeString(valueStr);
    }
}
