package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.*;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.*;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionDetailResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionSupportInfoResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorRoomSummary;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param.*;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.web.util.UrlUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import scala.annotation.meta.param;

import java.util.Date;
import java.util.List;

/**
 * 离线区域数据监控转换器
 * <AUTHOR>
 * @date 2025-08-12
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {CommonConvert.class}
)
public abstract class OfflineZoneConvert {

    @Autowired
    private AppConfig appConfig;

    /**
     * 将MapDataBean转换为MapDataVo
     */
    abstract MapDataVo mapDataBean2Vo(MapDataBean bean);

    /**
     * 将MapDataBean列表转换为MapDataVo列表
     */
    public abstract List<MapDataVo> mapDataBeans2Vos(List<MapDataBean> beans);

    /**
     * 将MapDataCityBean转换为MapDataCityVo
     */
    abstract MapDataCityVo mapDataCityBean2Vo(MapDataCityBean bean);

    /**
     * 将MapDataRoomBean转换为MapDataRoomVo
     */
    abstract MapDataRoomVo mapDataRoomBean2Vo(MapDataRoomBean bean);

    /**
     * 将OfflineZoneMetricsDataBean转换为OfflineZoneMetricsDataVo
     */
    abstract OfflineZoneMetricsDataVo metricsDataBean2Vo(OfflineZoneMetricsDataBean bean);

    /**
     * 将ResponseDataMonitorFamilySummary转换为OfflineZoneFamilySummaryVo
     */
    public abstract OfflineZoneFamilySummaryVo familySummaryBean2Vo(ResponseDataMonitorFamilySummary bean);

    /**
     * 将ResponseDataMonitorRoomSummary转换为OfflineZoneSummaryVo
     */
    public abstract OfflineZoneSummaryVo roomSummaryBean2Vo(ResponseDataMonitorRoomSummary bean);

    /**
     * 将OfflineZoneRoomDataBean转换为OfflineZoneRoomDataVo
     */
    @Mapping(target = "njInfo", expression = "java(convertUserBean2Vo(bean.getNjInfo()))")
    abstract OfflineZoneRoomDataVo roomDataBean2Vo(OfflineZoneRoomDataBean bean);

    /**
     * 将OfflineZonePlayerDataBean转换为OfflineZonePlayerDataVo
     */
    @Mapping(target = "playerInfo", expression = "java(convertUserBean2Vo(bean.getPlayerInfo()))")
    @Mapping(target = "njInfo", expression = "java(convertUserBean2Vo(bean.getNjInfo()))")
    abstract OfflineZonePlayerDataVo playerDataBean2Vo(OfflineZonePlayerDataBean bean);

    /**
     * 将PageBean<OfflineZoneRoomDataBean>转换为OfflineZoneRoomDataListVo
     */
    public PageVO<OfflineZoneRoomDataVo> roomDataPageBean2Vo(PageBean<OfflineZoneRoomDataBean> pageBean) {
        if (pageBean == null) {
            return null;
        }

        return PageVO.of(pageBean.getTotal(), roomDataBeans2Vos(pageBean.getList()));
    }
    /**
     * 将PageBean<OfflineZonePlayerDataBean>转换为OfflineZonePlayerDataListVo
     */
    public PageVO<OfflineZonePlayerDataVo> playerDataPageBean2Vo(PageBean<OfflineZonePlayerDataBean> pageBean) {
        if (pageBean == null) {
            return null;
        }
        return PageVO.of(pageBean.getTotal(), playerDataBeans2Vos(pageBean.getList()));
    }

    /**
     * 将OfflineZoneRoomDataBean列表转换为OfflineZoneRoomDataVo列表
     */
    abstract List<OfflineZoneRoomDataVo> roomDataBeans2Vos(List<OfflineZoneRoomDataBean> beans);

    /**
     * 将OfflineZonePlayerDataBean列表转换为OfflineZonePlayerDataVo列表
     */
    abstract List<OfflineZonePlayerDataVo> playerDataBeans2Vos(List<OfflineZonePlayerDataBean> beans);

    /**
     * UserBean转换为UserVo的辅助方法
     */
    fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo convertUserBean2Vo(UserBean userBean) {
        if (userBean == null) {
            return null;
        }
        return UserCommonConvert.I.userBean2Vo(userBean);
    }

    // ==================== Request构建方法 ====================

    /**
     * 构建RequestDataMonitorMapData
     */
    public abstract RequestDataMonitorMapData buildMapDataRequest(Integer appId, Long familyId);

    /**
     * 构建RequestDataMonitorRoomSummary
     */
    public RequestDataMonitorRoomSummary buildRoomSummaryRequest(Integer appId, Long familyId, Long queryRoomId) {
        RequestDataMonitorRoomSummary request = new RequestDataMonitorRoomSummary();
        request.setAppId(appId);
        request.setFamilyId(familyId);
        request.setRoomId(queryRoomId);
        return request;
    }

    /**
     * 构建RequestDataMonitorFamilySummary
     */
    public abstract RequestDataMonitorFamilySummary buildFamilySummaryRequest(Integer appId, Long familyId);

    /**
     * 构建RequestRegion
     */
    public RequestRegion buildRegionRequest(Integer appId) {
        RequestRegion request = new RequestRegion();
        request.setAppId(appId);
        return request;
    }

    /**
     * 构建RequestGetRoomDataList
     */
    public abstract RequestGetRoomDataList buildRoomDataListRequest(Integer appId, OfflineZoneRoomDataListParam param, Long familyId);

    /**
     * 构建RequestGetPlayerDataList
     */
    @Mapping(source = "familyId", target = "familyId")
    @Mapping(source = "queryRoomId", target = "njId")
    public abstract RequestGetPlayerDataList buildPlayerDataListRequest(Integer appId, OfflineZonePlayerDataListParam param, Long familyId, Long queryRoomId);

    // ==================== 跳槽保护相关转换方法 ====================

    /**
     * 构建RequestGetProtectionSupportInfo
     */
    public RequestGetProtectionSupportInfo buildProtectionSupportInfoRequest(Integer appId, OfflineZoneProtectionSupportInfoParam
            param) {
        RequestGetProtectionSupportInfo request = new RequestGetProtectionSupportInfo();
        request.setAppId(appId);
        request.setFamilyId(param.getFamilyId());
        request.setNjId(param.getNjId());
        request.setPlayerId(param.getPlayerId());
        return request;
    }

    /**
     * 构建RequestPlayerHandleAgreement
     */
    public RequestPlayerHandleAgreement buildPlayerHandleAgreementRequest(Integer appId, Long playerId, OfflineZoneProtectionPlayerConfirmParam param) {
        RequestPlayerHandleAgreement request = new RequestPlayerHandleAgreement();
        request.setAppId(appId);
        request.setPlayerId(playerId);
        request.setProtectionId(param.getProtectionId());
        request.setAgreeStatus(param.getStatus());
        return request;
    }

    /**
     * 构建RequestSubmitAgreement
     */
    public RequestSubmitAgreement buildSubmitAgreementRequest(Integer appId, Long uploadUserId, OfflineZoneProtectionSubmitParam param) {
        RequestSubmitAgreement request = new RequestSubmitAgreement();
        request.setId(param.getId());
        request.setAppId(appId);
        request.setFamilyId(param.getFamilyId());
        request.setNjId(param.getNjId());
        request.setPlayerId(param.getPlayerId());
        request.setUploadUserId(uploadUserId);

        // 时间戳转Date
        if (param.getAgreementStartTime() != null) {
            request.setAgreementStartTime(new Date(param.getAgreementStartTime()));
        }
        if (param.getAgreementEndTime() != null) {
            request.setAgreementEndTime(new Date(param.getAgreementEndTime()));
        }

        request.setStampSign(param.getStampSign());

        // 转换文件列表为JSON字符串
        if (param.getAgreementFile() != null && !param.getAgreementFile().isEmpty()) {
            request.setAgreementFileJson(convertFileListToJson(param.getAgreementFile()));
        }

        return request;
    }

    /**
     * 将ProtectionSupportInfoResponse转换为OfflineZoneProtectionSupportInfoVo
     */
    @Mapping(target = "playerInfo", expression = "java(convertPlayerInfoBean2Vo(response.getPlayerInfo()))")
    @Mapping(target = "familyInfo", expression = "java(convertFamilyInfoBean2Vo(response.getFamilyInfo()))")
    public abstract OfflineZoneProtectionSupportInfoVo protectionSupportInfoResponse2Vo(ProtectionSupportInfoResponse response);

    /**
     * 将ProtectionDetailResponse转换为OfflineZoneProtectionDetailVo
     */
    @Mapping(target = "agreementFile", expression = "java(convertAgreementFileBeans2Vos(response.getAgreementFile()))")
    public abstract OfflineZoneProtectionDetailVo protectionDetailResponse2Vo(ProtectionDetailResponse response);

    /**
     * 将AgreementFileBean转换为AgreementFileVo
     */
    @Mapping(target = "url", source = "url", qualifiedByName = "addWaveCdnHost")
    abstract AgreementFileVo agreementFileBean2Vo(AgreementFileBean bean);

    /**
     * 将AgreementFileBean列表转换为AgreementFileVo列表
     */
    abstract List<AgreementFileVo> convertAgreementFileBeans2Vos(List<AgreementFileBean> beans);

    /**
     * PlayerInfoBean转换为PlayerInfoVo的辅助方法
     */
    abstract PlayerInfoVo convertPlayerInfoBean2Vo(PlayerInfoBean bean);

    /**
     * FamilyInfoBean转换为FamilyInfoVo的辅助方法
     */
    abstract FamilyInfoVo convertFamilyInfoBean2Vo(FamilyInfoBean bean);

    /**
     * 将RegionBean列表转换为RegionVo列表
     */
    public abstract List<RegionVo> regionBeans2Vos(List<RegionBean> beans);

    /**
     * 将RegionBean转换为RegionVo
     */
    abstract RegionVo regionBean2Vo(RegionBean bean);
    /**
     * 将文件列表转换为JSON字符串
     */
    String convertFileListToJson(List<OfflineZoneProtectionSubmitParam.AgreementFileParam> fileList) {
        fileList.forEach(file -> file.setUrl(UrlUtils.removeHostOrEmpty(file.getUrl())));
        return JsonUtil.dumps(fileList);
    }

    @Named("addWaveCdnHost")
    protected String addWaveCdnHost(String path) {
        return UrlUtils.addHostOrEmpty(path, appConfig.getRomeFsDownloadCdn());
    }
}
