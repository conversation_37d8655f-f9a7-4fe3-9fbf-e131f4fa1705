package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/11 11:56
 */
@Data
public class PlayerDayStatsExportVo {

    @ExcelProperty(value = "日期")
    @DateTimeFormat(DatePattern.NORM_DATE_PATTERN)
    private Date date;

    @ExcelProperty(value = "收入")
    private String income;

    @ExcelProperty(value = "魅力值")
    private String charm;

    @ExcelProperty(value = "打卡麦序")
    private String seatOrder;

    @ExcelProperty(value = "上麦时长")
    private String upGuestDur;

    @ExcelProperty(value = "主持档")
    private String host;

}
