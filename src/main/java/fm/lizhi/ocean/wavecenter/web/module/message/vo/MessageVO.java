package fm.lizhi.ocean.wavecenter.web.module.message.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2024-10-30 04:25:18
 */
@Data
public class MessageVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型
     */
    private Integer type;

    /**
     * 目标用户 ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targetUserId;

    /**
     * 发送用户 ID, 1=系统发送
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sendUserId;

    /**
     * 可见角色
     */
    private String visibleRoleCode;

    /**
     * 是否已读
     */
    private Boolean read;

    /**
     * 跳转链接
     */
    private String targetLink;

    /**
     * 跳转类型
     */
    private String linkType;

    /**
     * 业务 ID
     */
    private Integer appId;

    /**
     * 关联场景业务ID，比如合同id,签约记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bizId;

    /**
     * 消息动作
     */
    private String action;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 生效时间
     */
    private Long effectTime;
}