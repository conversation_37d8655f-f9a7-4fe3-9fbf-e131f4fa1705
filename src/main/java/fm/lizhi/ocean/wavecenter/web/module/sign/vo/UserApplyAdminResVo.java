package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/24 14:48
 */
@Data
@Accessors(chain = true)
public class UserApplyAdminResVo {

    private String signId;

    private String contractUrl;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

}
