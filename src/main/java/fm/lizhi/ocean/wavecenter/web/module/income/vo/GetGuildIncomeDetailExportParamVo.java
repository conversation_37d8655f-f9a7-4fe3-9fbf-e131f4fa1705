package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 14:47
 */
@Data
public class GetGuildIncomeDetailExportParamVo  extends GetDetailBaseParamVo {


    @NotNull(message = "请选择厅")
    private Long roomId;

    /**
     * 收入类型
     */
    private List<Integer> incomeType;

}
