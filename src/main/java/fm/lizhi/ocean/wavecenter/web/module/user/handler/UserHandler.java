package fm.lizhi.ocean.wavecenter.web.module.user.handler;


import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class UserHandler {


    @Autowired
    private UserCommonService userCommonService;


    /**
     * 获取用户信息通过 波段号
     *
     * @param appId
     * @param band
     * @return
     */
    public Long getUserIdByBand(int appId,String band){

        if(StringUtils.isEmpty(band)){
            return null;
        }

        Result<UserBean> result = userCommonService.getUserByBand(appId, band);
        if(result.rCode()!= GeneralRCode.GENERAL_RCODE_SUCCESS || result.target()==null){
            return null;
        }
        return result.target().getId();
    }

    /**
     * 根据用户ID获取用户信息
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 用户信息
     */
    public UserBean getUserById(int appId, Long userId) {
        if (userId == null) {
            return null;
        }
        Result<UserBean> result = userCommonService.getUserById(appId, userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getUserById fail, userId={}, rCode={}", userId, result.rCode());
            return null;
        }
        return result.target();
    }

    /**
     * 批量查询用户
     * @param userIds
     * @return
     */
    public List<UserBean> getUserByIds(List<Long> userIds){
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        Result<List<UserBean>> result = userCommonService.getUserByIds(ContextUtils.getBusinessEvnEnum().getAppId(), userIds);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getUserByIds fail, userIds={}, rCode={}", JsonUtil.dumps(userIds), result.rCode());
            return Collections.emptyList();
        }

        return result.target();
    }

}
