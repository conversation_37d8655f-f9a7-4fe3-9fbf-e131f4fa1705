package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetActivityNotice;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityNoticeService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.ActivityNoticeVO;

@Slf4j
@RestController
@RequestMapping("/activity")
public class ActivityNoticeController {

    @Autowired
    private ActivityNoticeService activityNoticeService;

    /**
     * 获取活动公告
     * 
     * @return 活动公告
     */
    @GetMapping("/getNotice")
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER})
    public ResultVO<ActivityNoticeVO> getActivityNotice() {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        Result<ResponseGetActivityNotice> result = activityNoticeService.getNoticeConfig(appId, userId);
        if (RpcResult.isFail(result)) {
            return ResultVO.failure("查询活动公告失败");
        }

        if (RpcResult.noTarget(result)){
            log.info("未查询到活动公告. userId={}, appId={}", userId, appId);
            return ResultVO.success();
        }

        ActivityNoticeVO activityNoticeVO = new ActivityNoticeVO();
        activityNoticeVO.setId(result.target().getId());
        activityNoticeVO.setAppId(result.target().getAppId());
        activityNoticeVO.setOperator(result.target().getOperator());
        activityNoticeVO.setContent(result.target().getContent());
        return ResultVO.success(activityNoticeVO);
    }
}
