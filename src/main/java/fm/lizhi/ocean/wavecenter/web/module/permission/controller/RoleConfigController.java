package fm.lizhi.ocean.wavecenter.web.module.permission.controller;

import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.AddRoleAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.ModifyAuthConfigUserVo;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.permissions.request.RequestGetRoleRoomDataScope;
import fm.lizhi.ocean.wavecenter.api.permissions.service.RoleService;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.push.PushConstants;
import fm.lizhi.ocean.wavecenter.web.common.push.PushVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.convert.RoleConvert;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.AddRoleAuthConfigVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.ModifyRoleAuthStatusVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.RoleAuthRefVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.RoleStopMsgVo;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserPushHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.RoomVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserRoleInfoVo;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 角色授权配置
 * <AUTHOR>
 * @date 2024/4/11 14:36
 */
@RestController
@RequestMapping("/roleConfig")
public class RoleConfigController {

    private static final Logger log = LoggerFactory.getLogger(RoleConfigController.class);
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserPushHandler userPushHandler;
    @Autowired
    private DataScopeHandler dataScopeHandler;

    /**
     * 查询用户已授权的高级管理厅列表
     * @return
     */
    @PermissionCheck(passRole = RoleEnum.FAMILY, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("familyAdmin/detail")
    public ResultVO<List<RoomVo>> familyAdminDetail(@RequestParam("userId")Long userId){

        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        Result<List<RoomBean>> result = roleService.getRoleRoomDataScope(new RequestGetRoleRoomDataScope()
                .setRole(RoleEnum.FAMILY_ADMIN)
                .setFamilyId(familyId)
                .setAppId(appId)
                .setUserId(userId)
        );
        if (RpcResult.isFail(result)) {
            log.error("getRoleRoomDataScope fail. rCode={},userId={},familyId={},appId={}"
                    , result.rCode(), userId, familyId, appId);
            return ResultVO.success(Collections.emptyList());
        }

        List<RoomBean> beanList = result.target();
        if (CollectionUtils.isEmpty(beanList)) {
            return ResultVO.success(Collections.emptyList());
        }

        return ResultVO.success(UserCommonConvert.I.roomBeans2Vos(beanList));
    }

    /**
     * 查询所有授权列表
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = RoleEnum.FAMILY, onlySelfLogin = true)
    @GetMapping("allConfig")
    public ResultVO<PageVO<RoleAuthRefVo>> getAllConfig(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo
            , @RequestParam(value = "pageSize", defaultValue = "20") int pageSize
            , @RequestParam(value = "userId", required = false) Long userId
            , @RequestParam(value = "roleCode", required = false) String roleCode
    ){
        if (!dataScopeHandler.isLoginFamily()){
            return ResultVO.failure("仅家族长本人可操作");
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long createUserId = ContextUtils.getContext().getUserId();
        Result<PageBean<RoleAuthRefBean>> result = roleService.getAllAuthConfig(appId, createUserId, userId, roleCode, PageParamBean.builder()
                .pageNo(pageNo)
                .pageSize(pageSize)
                .build());
        if (RpcResult.isFail(result)) {
            return ResultVO.failure();
        }

        List<RoleAuthRefVo> voList = RoleConvert.I.authRefBeans2Vos(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList));
    }

    /**
     * 添加授权配置
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = RoleEnum.FAMILY, onlySelfLogin = true)
    @PostMapping("addConfig")
    public ResultVO<Void> addConfig(@Validated @RequestBody AddRoleAuthConfigVo paramVo){
        if (!dataScopeHandler.isLoginFamily()){
            return ResultVO.failure("仅家族长本人可操作");
        }

        //如果是高级管理，授权厅列表的数据必须大于等于2
        if (RoleEnum.FAMILY_ADMIN.getRoleCode().equals(paramVo.getRoleCode())
            && (paramVo.getAuthRoomIds() == null || paramVo.getAuthRoomIds().size()<=1)) {
            return ResultVO.failure(MsgCodes.CONFIG_PARAM_ERROR.getCode(), "至少选择两个授权厅");
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();

        if (paramVo.getUserId().equals(userId)) {
            return ResultVO.failure("不可为自己授权");
        }

        //如果是家族长，填充授权账号ID
        if (RoleEnum.FAMILY.getRoleCode().equals(paramVo.getRoleCode())
                || RoleEnum.FAMILY_ADMIN.getRoleCode().equals(paramVo.getRoleCode())) {
            paramVo.setSubjectUserId(userId);
        }

        //如果是厅主，授权账号不可为空
        if (RoleEnum.ROOM.getRoleCode().equals(paramVo.getRoleCode()) && paramVo.getSubjectUserId() == null) {
            return ResultVO.failure("授权账号不可为空");
        }

        Result<Void> result = roleService.addAuthConfig(appId, AddRoleAuthRefBean.builder()
                .userId(paramVo.getUserId())
                .roleCode(paramVo.getRoleCode())
                .subjectUserId(paramVo.getSubjectUserId())
                .createUserId(userId)
                .familyId(ContextUtils.getContext().getSubjectId())
                .authRoomIds(paramVo.getAuthRoomIds())
                .build());
        int rCode = result.rCode();
        if (rCode == RoleService.ADD_AUTH_CONFIG_EXIST) {
            return ResultVO.failure("角色授权配置已存在");
        }
        if (rCode == RoleService.ADD_AUTH_CONFIG_SUBJECT_ERROR) {
            return ResultVO.failure("无法确认授权账号");
        }
        if (rCode == RoleService.ADD_AUTH_CONFIG_ROLE_ERROR) {
            return ResultVO.failure("授权账号角色不符合");
        }

        return RpcResult.isFail(result) ? ResultVO.failure() : ResultVO.success();
    }

    /**
     * 修改授权配置状态
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = RoleEnum.FAMILY, onlySelfLogin = true)
    @PostMapping("modifyStatus")
    public ResultVO<Void> modifyStatus(@Validated @RequestBody ModifyRoleAuthStatusVo paramVo){
        if (!dataScopeHandler.isLoginFamily()){
            return ResultVO.failure("仅家族长本人可操作");
        }

        Result<List<ModifyAuthConfigUserVo>> result = roleService.modifyAuthConfigStatus(paramVo.getId(), paramVo.getStatus());
        if (RpcResult.isFail(result)) {
            return ResultVO.failure("修改失败");
        }

        List<ModifyAuthConfigUserVo> target = result.target();
        if (CollectionUtils.isEmpty(target)) {
            log.info("login user is empty");
            return ResultVO.success();
        }

        //查询授权角色信息
        Result<RoleAuthRefBean> authConfig = roleService.getAuthConfig(paramVo.getId());
        if (RpcResult.isFail(authConfig)) {
            //不影响主流程
            log.info("getAuthConfig fail. rCode={},id={}", authConfig.rCode(), paramVo.getId());
            return ResultVO.success();
        }

        RoleAuthRefBean authRefBean = authConfig.target();
        UserRoleInfoVo userRoleInfoVo = new UserRoleInfoVo();
        userRoleInfoVo.setRoleCode(authRefBean.getRoleCode());
        userRoleInfoVo.setBand(authRefBean.getSubject().getBand());
        userRoleInfoVo.setName(authRefBean.getSubject().getName());

        for (ModifyAuthConfigUserVo userVo : target) {
            PushVo<RoleStopMsgVo> pushVo = new PushVo<>();
            pushVo.setBiz(PushConstants.Biz.ROLE_STOP);
            pushVo.setData(new RoleStopMsgVo()
                    .setDate(DateUtil.formatDateTime(new Date()))
                    .setRoleInfo(userRoleInfoVo)
            );
            userPushHandler.alterDevicePush(userVo.getAppId(), userVo.getUserId(), userVo.getDeviceId(), pushVo);
        }

        return ResultVO.success();
    }

}
