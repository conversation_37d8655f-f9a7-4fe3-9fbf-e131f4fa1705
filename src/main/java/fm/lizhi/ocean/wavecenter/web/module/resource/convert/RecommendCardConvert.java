package fm.lizhi.ocean.wavecenter.web.module.resource.convert;


import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.AllocationItemBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendAllocationRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardRewardBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardUseRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.RequestRewardRecommendCard;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.web.module.resource.param.AllocationRecommendCardParam;
import fm.lizhi.ocean.wavecenter.web.module.resource.param.AllocationRecommendCardParam.Task;
import fm.lizhi.ocean.wavecenter.web.module.resource.vo.AllocationItemVO;
import fm.lizhi.ocean.wavecenter.web.module.resource.vo.RecommendAllocationRecordVO;
import fm.lizhi.ocean.wavecenter.web.module.resource.vo.RecommendCardUseRecordVO;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 推荐卡转换器
 * <AUTHOR>
 * @date 2025/3/21 17:00
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RecommendCardConvert {

    RecommendCardConvert I = Mappers.getMapper(RecommendCardConvert.class);

    /**
     * 转换推荐卡
     * @param task
     * @return
     */
    RecommendCardRewardBean convertRewardBean(Task task);

    /**
     * 转换推荐卡
     * @param param
     * @param appId
     * @param userId
     * @return
     */
    @Mappings({
        @Mapping(target = "operatorUserId", source = "userId"),
        @Mapping(target = "rewardBeans", source = "param.tasks"),
    })
    RequestRewardRecommendCard convertRewardBean(AllocationRecommendCardParam param, int appId, long userId);

    /**
     * 推荐卡分配记录转换
     * @param bean
     * @return
     */
    RecommendAllocationRecordVO allocationRecordBean2VO(RecommendAllocationRecordBean bean);

    List<RecommendAllocationRecordVO> allocationRecordBeans2VOs(List<RecommendAllocationRecordBean> beans);

    UserVo userBean2Vo(UserBean bean);

    /**
     * 推荐卡分配候选列表转换
     * @param bean
     * @return
     */
    AllocationItemVO allocationItemBean2VO(AllocationItemBean bean);

    List<AllocationItemVO> allocationItemBeans2VOs(List<AllocationItemBean> beans);

    @Mappings({
            @Mapping(target = "room.id", source = "njId"),
            @Mapping(target = "room.name", source = "njName"),
            @Mapping(target = "room.band", source = "njBand"),
    })
    RecommendCardUseRecordVO useRecordBean2VO(RecommendCardUseRecordBean bean);

    List<RecommendCardUseRecordVO> useRecordBeans2VOs(List<RecommendCardUseRecordBean> beans);
}
