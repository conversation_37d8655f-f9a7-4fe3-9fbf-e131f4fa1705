package fm.lizhi.ocean.wavecenter.web.module.award.family.vo;

import lombok.Data;

/**
 * 获取公会周奖励V2的响应
 */
@Data
public class GetFamilyWeekAwardV2VO {

    /**
     * 推荐卡总数
     */
    private Integer totalRecommendCardNumber = 0;

    /**
     * 等级奖励推荐卡数量
     */
    private Integer levelRecommendCardNumber = 0;

    /**
     * 流水增长推荐卡数量
     */
    private Integer flowGrowthRecommendCardNumber = 0;

    /**
     * 新厅留存推荐卡数量
     */
    private Integer newRoomRetainRecommendCardNumber = 0;

    /**
     * 0流失厅推荐卡数量
     */
    private Integer zeroLostRoomRecommendCardNumber = 0;

    /**
     * 特殊推荐卡数量
     */
    private Integer specialRecommendCardNumber = 0;

    /**
     * 新厅名额总数
     */
    private Integer totalNewRoomNumber = 0;

    /**
     * 等级新厅名额数量
     */
    private Integer levelNewRoomNumber = 0;

    /**
     * 流水涨幅新厅名额数量
     */
    private Integer flowGrowthNewRoomNumber = 0;

    /**
     * 流失厅新厅名额数量
     */
    private Integer lostRoomNewRoomNumber = 0;

    /**
     * 新厅留存新厅名额数量
     */
    private Integer newRoomRetainNewRoomNumber = 0;
}
