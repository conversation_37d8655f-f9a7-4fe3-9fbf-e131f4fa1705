package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 活动环节信息
 */
@Data
@Accessors(chain = true)
public class ActivityProcessVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 活动环节名
     */
    @NotBlank(message = "活动环节名不能为空")
    private String name;

    /**
     * 活动环节说明
     */
    private String explanation;

    /**
     * 时长
     */
    private String duration;
}
