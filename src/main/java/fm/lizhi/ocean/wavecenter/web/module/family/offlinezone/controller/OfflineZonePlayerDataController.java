package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZonePlayerDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetPlayerDataList;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZonePlayerDataService;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert.OfflineZoneConvert;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param.OfflineZonePlayerDataListParam;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.OfflineZonePlayerDataListVo;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.OfflineZonePlayerDataVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 线下专区-主播数据Controller
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@RestController
@RequestMapping("offline/playerData")
public class OfflineZonePlayerDataController {

    @Autowired
    private OfflineZonePlayerDataService offlineZonePlayerDataService;
    @Autowired
    private DataScopeHandler dataScopeHandler;
    @Autowired
    private OfflineZoneConvert offlineZoneConvert;

    /**
     * 主播数据列表
     * @param paramVo 请求参数
     * @return 主播数据列表
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/list")
    public ResultVO<PageVO<OfflineZonePlayerDataVo>> list(@Validated OfflineZonePlayerDataListParam paramVo) {

        // 获取当前角色对应的家族ID
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long queryRoomId = dataScopeHandler.getRoomForRoomOrDefault(paramVo.getNjId());

        //校验
        if (familyId == null) {
            return ResultVO.failure("当前厅没有签约家族");
        }

        // 构建请求参数
        RequestGetPlayerDataList request = offlineZoneConvert.buildPlayerDataListRequest(
                ContextUtils.getBusinessEvnEnum().getAppId(), paramVo, familyId, queryRoomId);

        // 调用Service获取数据
        Result<PageBean<OfflineZonePlayerDataBean>> result = offlineZonePlayerDataService.getPlayerDataList(request);
        
        if (RpcResult.isFail(result)) {
            log.error("getPlayerDataList,error,request={},rCode={}", request, result.rCode());
            return ResultVO.failure();
        }

        // 转换并返回结果
        return ResultVO.success(offlineZoneConvert.playerDataPageBean2Vo(result.target()));
    }
}
