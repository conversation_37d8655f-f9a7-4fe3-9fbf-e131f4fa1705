package fm.lizhi.ocean.wavecenter.web.common;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * double --> long 数值序列化，四舍五入
 * <AUTHOR>
 */
public class DoubleToLongSerializer extends JsonSerializer<Double> {
    @Override
    public void serialize(Double value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            long roundedValue = Math.round(value);
            gen.writeNumber(roundedValue);
        }
    }
}