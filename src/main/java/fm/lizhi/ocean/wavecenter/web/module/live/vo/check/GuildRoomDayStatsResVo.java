package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;


import fm.lizhi.ocean.wavecenter.web.module.user.vo.RoomVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class GuildRoomDayStatsResVo implements IDetailList<GuildRoomDayDetailVo>{


    private RoomVo room;

    /**
     * 合计
     */
    private GuildRoomDayStatsVo stats;

    /**
     * 明细
     */
    private List<GuildRoomDayDetailVo> detail;

    @Override
    public List<GuildRoomDayDetailVo> foundDetail() {
        return this.detail;
    }
}
