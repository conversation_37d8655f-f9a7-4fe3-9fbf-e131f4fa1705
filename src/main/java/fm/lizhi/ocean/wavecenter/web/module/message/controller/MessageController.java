package fm.lizhi.ocean.wavecenter.web.module.message.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestBatchReadMessage;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestGetMessageList;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestUnReadMessageCount;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseGetMessageList;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseQueryRecentMessages;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseUnReadMessageCount;
import fm.lizhi.ocean.wavecenter.api.message.service.WaveCenterMessageService;
import fm.lizhi.ocean.wavecenter.api.message.service.WcNoticeConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.message.convert.MessageConvert;
import fm.lizhi.ocean.wavecenter.web.module.message.param.BatchReadMessageParam;
import fm.lizhi.ocean.wavecenter.web.module.message.param.GetMessageListParam;
import fm.lizhi.ocean.wavecenter.web.module.message.result.GetMessageListResult;
import fm.lizhi.ocean.wavecenter.web.module.message.result.QueryRecentMessagesResult;
import fm.lizhi.ocean.wavecenter.web.module.message.result.UnReadMessageCount;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息中心
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/message")
@Slf4j
public class MessageController {

    @Autowired
    private WaveCenterMessageService waveCenterMessageService;

    @Autowired
    private WcNoticeConfigService wcNoticeConfigService;


    /**
     * 获取消息列表
     */
    @GetMapping("/list")
    @VerifyUserToken
    @PermissionCheck
    public ResultVO<GetMessageListResult> list(GetMessageListParam param) {

        RequestGetMessageList request = new RequestGetMessageList();
        request.setPerformanceId(param.getPerformanceId());
        request.setSize(param.getSize());
        request.setType(param.getType());
        request.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        request.setUserId(ContextUtils.getContext().getUserId());
        request.setRoleCode(ContextUtils.getContext().getRoleCode());

        Result<ResponseGetMessageList> result = waveCenterMessageService.getMessageList(request);
        if (RpcResult.isFail(result)) {
            log.error("get message list fail. userId: {}, appId:{}, performanceId:{}", request.getUserId(), request.getAppId(), request.getPerformanceId());
            return ResultVO.failure();
        }

        return ResultVO.success(MessageConvert.I.toGetMessageListResult(result.target()));


    }


    /**
     * 批量已读
     */
    @PostMapping("/batchRead")
    @VerifyUserToken
    @PermissionCheck
    public ResultVO<Void> batchRead(@RequestBody BatchReadMessageParam param) {

        RequestBatchReadMessage request = new RequestBatchReadMessage();
        request.setIds(param.getIds());
        request.setType(param.getType());
        request.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        request.setUserId(ContextUtils.getContext().getUserId());
        request.setRoleCode(ContextUtils.getContext().getRoleCode());
        request.setAllRead(param.getAllRead());
        Result<Void> result = waveCenterMessageService.batchRead(request);
        if (RpcResult.isFail(result)) {
            log.error("batch read message fail. userId: {}, appId:{}, ids:{}, isAllRead:{}", request.getUserId(), request.getAppId(), request.getIds(), param.getAllRead());
            return ResultVO.failure();
        }

        return ResultVO.success();
    }

    @GetMapping("/getUnReadNoticeCount")
    @VerifyUserToken
    @PermissionCheck
    public ResultVO<List<UnReadMessageCount>> getUnreadMessageCount(@RequestParam(value = "type", required = false) Integer type){
        RequestUnReadMessageCount request = new RequestUnReadMessageCount();
        request.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        request.setTargetUserId(ContextUtils.getContext().getUserId());
        request.setType(type);
        request.setRoleCode(ContextUtils.getContext().getRoleCode());
        Result<ResponseUnReadMessageCount> result = wcNoticeConfigService.getUnReadMessageCount(request);
        if (RpcResult.isFail(result)) {
            log.warn("get unread notice count fail. userId: {}, appId:{}", ContextUtils.getContext().getUserId(), request.getAppId());
            return ResultVO.failure();
        }

        return ResultVO.success(MessageConvert.I.toUnReadMessageCountBeanList(result.target().getUnReadMessageCountList()));
    }

    @GetMapping("/getHomeMsg")
    @VerifyUserToken
    @PermissionCheck
    public ResultVO<QueryRecentMessagesResult> queryRecentMessages(@RequestParam("size") Integer size) {
        size = size == null ? 4 : size;
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        String roleCode = ContextUtils.getContext().getRoleCode();
        Result<ResponseQueryRecentMessages> result = waveCenterMessageService.queryRecentMessages(appId, userId, size, roleCode);
        if (RpcResult.isFail(result)) {
            log.error("queryRecentMessages fail. userId: {}, appId:{}", userId, appId);
            return ResultVO.failure();
        }

        ResponseQueryRecentMessages target = result.target();
        QueryRecentMessagesResult resp = MessageConvert.I.toResponseQueryRecentMessages(target);
        return ResultVO.success(resp);
    }


}
