package fm.lizhi.ocean.wavecenter.web.config;

import fm.lizhi.ocean.wavecenter.web.util.ThreadUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2024/4/23 19:47
 */
@EnableAsync
@Configuration
public class ExecutorConfig {

    @Bean
    public Executor fileExport() {
        return ThreadUtils.getTtlExecutors("file-export-");
    }

}
