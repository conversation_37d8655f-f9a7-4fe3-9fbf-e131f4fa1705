package fm.lizhi.ocean.wavecenter.web.module.message.convert;

import fm.lizhi.ocean.wavecenter.api.message.bean.UnReadMessageCountBean;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseGetMessageList;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseQueryRecentMessages;
import fm.lizhi.ocean.wavecenter.web.module.message.result.GetMessageListResult;
import fm.lizhi.ocean.wavecenter.web.module.message.result.QueryRecentMessagesResult;
import fm.lizhi.ocean.wavecenter.web.module.message.result.UnReadMessageCount;

import java.util.List;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface MessageConvert {

    MessageConvert I = Mappers.getMapper(MessageConvert.class);


    GetMessageListResult toGetMessageListResult(ResponseGetMessageList target);


    List<UnReadMessageCount> toUnReadMessageCountBeanList(List<UnReadMessageCountBean> unReadMessageCountList);

    QueryRecentMessagesResult toResponseQueryRecentMessages(ResponseQueryRecentMessages recentMessage);
}
