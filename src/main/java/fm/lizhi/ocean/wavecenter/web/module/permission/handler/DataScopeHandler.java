package fm.lizhi.ocean.wavecenter.web.module.permission.handler;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestHasSignRecordWithRooms;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignPlayerService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.util.WcAssert;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserFamilyHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/12 09:00
 */
@Component
public class DataScopeHandler {

    private static final Logger log = LoggerFactory.getLogger(DataScopeHandler.class);
    @Autowired
    private UserFamilyHandler userFamilyHandler;
    @Autowired
    private UserFamilyService userFamilyService;
    @Autowired
    private SignPlayerService signPlayerService;
    @Autowired
    private UserCommonService userCommonService;

    /**
     * 获取厅主或者登录用户的ID
     * @return
     */
    public Long getRoomOrPlayer(){
        if (ContextUtils.getContext().isRoom()) {
            return ContextUtils.getContext().getSubjectId();
        }
        return ContextUtils.getContext().getUserId();
    }

    /**
     * 根据主播波段号，检查主播是否在数据权限范围中
     * @param band
     * @return
     */
    public boolean checkParamPlayerBand(String band) {
        if (!ContextUtils.getContext().isFamilyAdmin()) {
           return true;
        }
        if (StringUtils.isBlank(band)) {
            return true;
        }

        Result<UserBean> result = userCommonService.getUserByBand(ContextUtils.getBusinessEvnEnum().getAppId(), band);
        if (RpcResult.isFail(result)) {
            log.error("getUserByBand fail. rCode={},band={}", result.rCode(), band);
            return false;
        }
        return checkParamPlayerId(result.target().getId());
    }

    /**
     * 检查主播是否在权限范围内
     * @param playerId
     * @return
     */
    public boolean checkParamPlayerId(Long playerId){
        if (!ContextUtils.getContext().isFamilyAdmin()) {
            return true;
        }
        if (playerId == null) {
            return true;
        }

        List<Long> roomResource = ContextUtils.getContext().getRoomResource();
        RequestHasSignRecordWithRooms request = new RequestHasSignRecordWithRooms()
                .setAppId(ContextUtils.getBusinessEvnEnum().getAppId())
                .setNjIds(roomResource)
                .setPlayerId(playerId);
        Result<Boolean> result = signPlayerService.hasSignRecordWithRooms(request);
        if (RpcResult.isFail(result)) {
            log.error("hasSignRecordWithRooms fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return false;
        }
        return result.target();
    }

    /**
     * 检查请求用户是否拥有参数房间的访问权限
     * @param roomId 待检查校验的厅
     * @return 是否拥有权限
     */
    public boolean checkParamRoom(Long roomId){
        if (!ContextUtils.getContext().isFamilyAdmin()) {
            return true;
        }
        if (roomId == null) {
            //空则忽略
            return true;
        }

        return checkParamRoom(Lists.newArrayList(roomId));
    }

    /**
     * 检查请求用户是否拥有参数房间的访问权限
     * @param roomIds 待检查校验的厅列表
     * @return 是否拥有权限
     */
    public boolean checkParamRoom(List<Long> roomIds){
        if (!ContextUtils.getContext().isFamilyAdmin()) {
            return true;
        }
        if (CollectionUtils.isEmpty(roomIds)) {
            return true;
        }

        List<Long> roomResource = ContextUtils.getContext().getRoomResource();
        for (Long roomId : roomIds) {
            if (!roomResource.contains(roomId)) {
                //存在权限以外的厅，不允许范围
                return false;
            }
        }
        return true;
    }

    /**
     * 登录用户是否为家族长
     * @return
     */
    public boolean isLoginFamily(){
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<UserInFamilyBean> result = userFamilyService.getUserInFamily(appId, userId);
        if (RpcResult.isFail(result)) {
            log.warn("isLoginFamily getUserInFamily fail. userId={},appId={},rCode={}", userId, appId, result.rCode());
            return false;
        }
        return result.target().isFamily();
    }

    /**
     * 获取当前登录家族长或厅主可以查看的家族ID
     * 1. 家族长本人登录：获取subjectId
     * 2. 家族长授权登录：获取subjectId
     * 3. 厅主本人登录: 获取subjectId签约的familyId
     * 4. 厅主授权登录：获取subjectId签约的familyId
     * @return
     */
    public Long getFamilyForFamilyOrRoom(){
        if (ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin()){
            return ContextUtils.getContext().getSubjectId();
        }
        if (ContextUtils.getContext().isRoom()){
            //获取当前厅主签约的家族
            return userFamilyHandler.getUserSignFamilyId(ContextUtils.getContext().getSubjectId());
        }
        return null;
    }

    /**
     * 获取当前登录家族长的家族ID
     * @return
     */
    public Long getFamilyForFamily(){
        if (ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin()){
            return ContextUtils.getContext().getSubjectId();
        }
        return null;
    }

    /**
     * 获取当前登录角色的家族ID
     * @return
     */
    public Long getFamilyForBase(){
        if (ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin()){
            return ContextUtils.getContext().getSubjectId();
        }
        if (ContextUtils.getContext().isRoom()){
            //查询当前厅签约的家族
            Long roomId = ContextUtils.getContext().getSubjectId();
            Long familyId = userFamilyHandler.getUserSignFamilyId(roomId);
            WcAssert.notNull(familyId, "当前厅无签约信息");
            return familyId;
        }
        if (ContextUtils.getContext().isPlayer()) {
            //查询当前陪玩签约的厅的家族
            long userId = ContextUtils.getContext().getUserId();
            Long familyId = userFamilyHandler.getUserSignFamilyId(userId);
            WcAssert.notNull(familyId, "当前陪玩无签约信息");
            return familyId;
        }
        return null;
    }

    /**
     * 获取当前厅主或者陪玩签约的厅ID
     * @return
     */
    public Long getRoomForRoomOrPlayer(){
        if (ContextUtils.getContext().isRoom()){
            return ContextUtils.getContext().getSubjectId();
        }
        if (ContextUtils.getContext().isPlayer()){
            //获取当前陪玩签约的厅
            long userId = ContextUtils.getContext().getUserId();
            Long roomId = userFamilyHandler.getPlayerSignRoom(userId);
            WcAssert.notNull(roomId, "用户未签约");
            return roomId;
        }
        return null;
    }

    /**
     * 获取当前陪玩的ID
     * 如果是家族长或厅主：返回默认
     * 陪玩，返回当前陪玩ID
     * @param playerId
     * @return
     */
    public Long getPlayerForBaseOrDefault(Long playerId){
        if (ContextUtils.getContext().isPlayer()) {
            return ContextUtils.getContext().getUserId();
        }
        return playerId;
    }

    /**
     * 获取当前登录厅主或家族长可以访问的厅主ID
     * 1. 家族长：返回默认值
     * 2. 厅主：返回subjectId
     * @param roomId
     * @return
     */
    public Long getRoomForRoomOrDefault(Long roomId){
        if (ContextUtils.getContext().isRoom()){
            return ContextUtils.getContext().getSubjectId();
        }
        return roomId;
    }

    public Long getRoomForBase(Long defaultRoomId){
        if (ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin()) {
            return defaultRoomId;
        }
        if (ContextUtils.getContext().isRoom()) {
            return ContextUtils.getContext().getSubjectId();
        }
        if (ContextUtils.getContext().isPlayer()) {
            long userId = ContextUtils.getContext().getUserId();
            Long roomId = userFamilyHandler.getPlayerSignRoom(userId);
            WcAssert.notNull(roomId, "未签约");
            return roomId;
        }
        return null;
    }

    /**
     * 获取当前角色厅，可指定公会下的陪玩的厅
     * @param defaultRoomId
     * @param roomRefPlayerId
     * @return
     */
    public Long getRoomForBase(Long defaultRoomId, Long roomRefPlayerId){
        if (ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin()) {
            if (roomRefPlayerId != null) {
                //查询roomRefPlayerId在该公会下最近签约的厅
                Long familyId = ContextUtils.getContext().getSubjectId();
                Long roomId = userFamilyHandler.getPlayerLastRoom(familyId, roomRefPlayerId);
                WcAssert.notNull(roomId, "陪玩未签约厅");
                return roomId;
            }
            WcAssert.notNull(defaultRoomId, "请选择厅");
            return defaultRoomId;
        }
        if (ContextUtils.getContext().isRoom()) {
            return ContextUtils.getContext().getSubjectId();
        }
        if (ContextUtils.getContext().isPlayer()) {
            //陪玩当前签约的厅
            long userId = ContextUtils.getContext().getUserId();
            Long roomId = userFamilyHandler.getPlayerSignRoom(userId);
            WcAssert.notNull(roomId, "陪玩未签约厅");
            return roomId;
        }
        return null;
    }

    /**
     * 获取当前角色厅，可指定公会下的陪玩的厅
     * @param defaultRoomId
     * @param roomRefPlayerId
     * @return
     */
    public Long getRoomForFamilyOrRoom(Long defaultRoomId, Long roomRefPlayerId){
        if (ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin()) {
            if (roomRefPlayerId != null) {
                //查询roomRefPlayerId在该公会下最近签约的厅
                Long familyId = ContextUtils.getContext().getSubjectId();
                Long roomId = userFamilyHandler.getPlayerLastRoom(familyId, roomRefPlayerId);
                WcAssert.notNull(roomId, "陪玩未签约厅");
                return roomId;
            }
            WcAssert.notNull(defaultRoomId, "请选择厅");
            return defaultRoomId;
        }
        if (ContextUtils.getContext().isRoom()) {
            return ContextUtils.getContext().getSubjectId();
        }
        return null;
    }

    /**
     * 获取厅主登录时，可以查询的家族ID
     * @return
     */
    public Long getQueryFamilyForRoom(){
        if (!ContextUtils.getContext().isRoom()) {
            return null;
        }
        return null;
    }

}
