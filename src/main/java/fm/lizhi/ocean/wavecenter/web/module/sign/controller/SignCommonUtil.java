package fm.lizhi.ocean.wavecenter.web.module.sign.controller;

import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseSignResult;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignErrorCode;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/10/14 19:12
 */
public class SignCommonUtil {

    /**
     * 签约通用异常码检查
     * @param result
     * @return
     */
    public static ResultVO<Void> signCommonCodeCheck(ResponseSignResult result){
        int code = result.getResultCode();

        //通用特殊异常码
        switch (code) {
            case SignErrorCode.REQ_USER_NO_PLATFORM_VERIFY:
                return ResultVO.failure(MsgCodes.PLATFORM_VERIFY_NO_PASS);

            case SignErrorCode.REQ_USER_NO_SIGN_VERIFY:
                return ResultVO.failure(MsgCodes.SIGN_VERIFY_NO_PASS);

            case SignErrorCode.REQ_USER_GENDER_NO_SETTING:
            case SignErrorCode.REQ_USER_MEDIA_INFO_NO_EXIST:
                return ResultVO.failure(MsgCodes.MEDIA_INFO_NOT_EXIST);

            case SignErrorCode.TAR_USER_NO_PLATFORM_VERIFY:
                return ResultVO.failure(MsgCodes.PLATFORM_VERIFY_NO_PASS_ERROR);

            case SignErrorCode.TAR_USER_GENDER_NO_SETTING:
                return ResultVO.failure(MsgCodes.MEDIA_INFO_NOT_EXIST_ERROR);

            case SignErrorCode.PLAYER_SIGNED:
                return ResultVO.failure(MsgCodes.PLAYER_SIGNED);

            case SignErrorCode.PLAYER_CENTER_AUTH_NULL:
                return ResultVO.failure(MsgCodes.PLAYER_CENTER_AUTH);
        }

        if (StringUtils.isNotBlank(result.getResultMsg()) && code != 0) {
            return ResultVO.failure(result.getResultMsg());
        }

        return code == 0 ? ResultVO.success() : ResultVO.failure(MsgCodes.FAIL);
    }

}
