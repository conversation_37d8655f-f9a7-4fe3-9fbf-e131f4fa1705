package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/18 17:53
 */
@Data
@Accessors(chain = true)
public class RoomPlayerPerformanceResVo {

    private Integer total;

    private List<PlayerPerformanceVo> players;

    /**
     * 考核周期开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date startDate;

    /**
     * 考核周期结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date endDate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date flushTime;

}
