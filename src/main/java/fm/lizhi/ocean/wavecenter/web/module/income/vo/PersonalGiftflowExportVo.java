package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 20:03
 */
@Data
public class PersonalGiftflowExportVo {

    @ExcelProperty(value = "记录时间")
    @DateTimeFormat(DatePattern.NORM_DATETIME_PATTERN)
    private Date date;

    @ExcelProperty(value = "收礼厅")
    private String recRoomName;

    @ExcelProperty(value = "收礼厅ID")
    private String recRoomBand;

    @ExcelProperty(value = "礼物")
    private String giftName;

    @ExcelProperty(value = "魅力值")
    private String charm;

    @ExcelProperty(value = "内容")
    private String content;

    @ExcelProperty(value = "送礼人")
    private String sendUserName;

    @ExcelProperty(value = "送礼人ID")
    private String sendUserBand;

}
