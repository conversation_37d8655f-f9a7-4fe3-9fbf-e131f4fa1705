package fm.lizhi.ocean.wavecenter.web.module.grow.ability.param;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import lombok.Data;

/**
 * 查询厅主播排名请求参数
 */
@Data
public class GetRoomPlayerRankParam {

    /**
     * 考核周期开始日期, 格式为yyyy-MM-dd
     */
    private String startWeekDate;

    /**
     * 排序能力项code, 如果为空则按综合分排序
     */
    private String orderByCapabilityCode;

    /**
     * 排序方向
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private String orderByDirection = OrderType.ASC.getValue();

    /**
     * 是否只看新主播
     */
    @JsonSetter(nulls = Nulls.SKIP)
    private Boolean onlyNewPlayer = false;
}
