package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class RoomIncomeDetailVo {

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date date;

    private Integer incomeType;

    private Integer income;

    private String content;

}
