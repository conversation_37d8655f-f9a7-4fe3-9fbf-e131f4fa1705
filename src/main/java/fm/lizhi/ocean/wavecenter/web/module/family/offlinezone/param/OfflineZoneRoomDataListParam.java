package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 离线区域厅数据列表请求参数
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Accessors(chain = true)
public class OfflineZoneRoomDataListParam {

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 厅分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 开始日期 YYYY-MM-DD
     */
    private String startDate;

    /**
     * 结束日期 YYYY-MM-DD
     */
    private String endDate;

    /**
     * 排序字段
     */
    private String orderMetrics;

    /**
     * 排序类型：ASC/DESC
     */
    private String orderType;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNo = 1;

    /**
     * 页大小
     */
    @Min(value = 1, message = "页大小不能小于1")
    @Max(value = 100, message = "页大小不能大于100")
    private Integer pageSize = 20;
}
