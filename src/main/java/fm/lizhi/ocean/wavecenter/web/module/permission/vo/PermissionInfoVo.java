package fm.lizhi.ocean.wavecenter.web.module.permission.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/12 14:36
 */
@Data
@Accessors(chain = true)
public class PermissionInfoVo {
    /**
     * 拥有的菜单权限
     */
    private List<String> menu;

    /**
     * 可读的组件
     */
    private List<String> readComponents;

    /**
     * 可操作的组件
     */
    private List<String> writeComponents;
}
