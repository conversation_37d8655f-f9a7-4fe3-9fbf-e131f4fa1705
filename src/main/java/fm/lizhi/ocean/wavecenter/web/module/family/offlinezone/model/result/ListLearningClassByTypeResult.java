package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result;

import com.ctrip.framework.apollo.core.enums.Env;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassStatusEnum;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassTypeEnum;
import lombok.Data;

/**
 * 根据类型查询学习课堂列表结果
 */
@Data
public class ListLearningClassByTypeResult {

    /**
     * 资料ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 部署环境
     *
     * @see Env#name()
     */
    private String deployEnv;

    /**
     * 标题
     */
    private String title;

    /**
     * 类型
     *
     * @see OfflineZoneLearningClassTypeEnum
     */
    private Integer type;

    /**
     * 文件链接
     */
    private String fileUrl;

    /**
     * 文件封面
     */
    private String fileCover;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 状态
     *
     * @see OfflineZoneLearningClassStatusEnum
     */
    private Integer status;

    /**
     * 标签
     */
    private String label;

    /**
     * 标签颜色
     */
    private String labelColor;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 创建时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * 修改时间, 毫秒时间戳
     */
    private Long modifyTime;

    /**
     * 该资料是否属于白名单, 即专属课程
     */
    private boolean inWhiteList;
}
