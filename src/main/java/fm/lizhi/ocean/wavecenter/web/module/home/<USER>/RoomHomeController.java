package fm.lizhi.ocean.wavecenter.web.module.home.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomMsgAnalysisPerformance;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomMsgAnalysisPerformance;
import fm.lizhi.ocean.wavecenter.api.home.service.RoomHomeService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.home.convert.RoomHomeConvert;
import fm.lizhi.ocean.wavecenter.web.module.home.result.RoomKeyDataSummaryResult;
import fm.lizhi.ocean.wavecenter.web.module.home.result.RoomKeyDataTrendChartResult;
import fm.lizhi.ocean.wavecenter.web.module.home.result.RoomMsgAnalysisPerformanceResult;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * 厅管理首页
 * <AUTHOR>
 */
@RestController
@RequestMapping("/home/<USER>")
@Slf4j
public class RoomHomeController {

    @Autowired
    private RoomHomeService roomHomeService;

    @Autowired
    private DataScopeHandler dataScopeHandler;


    /**
     * 获取关键数据汇总
     */
    @GetMapping("/keyData/summary")
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    public ResultVO<RoomKeyDataSummaryResult> getRoomKeyDataSummary(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {

        Long familyId = dataScopeHandler.getFamilyForBase();
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId = dataScopeHandler.getRoomForRoomOrDefault(null);
        log.info("getRoomKeyDataSummary appId={}, familyId={}, roomId={}", appId, familyId, roomId);

        RequestGetRoomKeyDataSummary request = new RequestGetRoomKeyDataSummary()
                .setFamilyId(familyId)
                .setRoomId(roomId)
                .setUserId(userId)
                .setAppId(appId)
                .setStartDate(startTime)
                .setEndDate(endTime);
        Result<ResponseRoomKeyDataSummary> result = roomHomeService.getRoomKeyDataSummary(request);

        if (RpcResult.isFail(result)) {
            log.error("getRoomKeyDataSummary fail, appId={}, familyId={}, roomId={}, rCode={}", appId, familyId, roomId, result.rCode());
            return ResultVO.failure();
        }

        return ResultVO.success(RoomHomeConvert.I.toRoomKeyDataSummaryResult(result.target()));
    }


    /**
     * 获取关键数据趋势图
     * @return
     */
    @GetMapping("/keyData/trendChart")
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    public ResultVO<List<RoomKeyDataTrendChartResult>> getKeyDataTrendChart(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {

        Long familyId = dataScopeHandler.getFamilyForBase();
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId = dataScopeHandler.getRoomForRoomOrDefault(null);
        log.info("getKeyDataTrendChart appId={}, familyId={}, roomId={}", appId, familyId, roomId);

        RequestGetRoomKeyDataTrendChart request = new RequestGetRoomKeyDataTrendChart()
              .setFamilyId(familyId)
              .setRoomId(roomId)
              .setUserId(userId)
              .setAppId(appId)
              .setStartDate(startTime)
              .setEndDate(endTime);

        Result<List<ResponseRoomKeyDataTrendChart>> result = roomHomeService.getRoomKeyDataTrendChart(request);
        if (RpcResult.isFail(result)) {
            log.error("getKeyDataTrendChart fail, appId={}, familyId={}, roomId={}, rCode={}", appId, familyId, roomId, result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(RoomHomeConvert.I.toResponseRoomKeyDataTrendCharts(result.target()));
    }


    /**
     * 私信拓客表现
     * @return
     */
    @GetMapping("/msgAnalysis/performance")
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    public ResultVO<RoomMsgAnalysisPerformanceResult> getAnalysisPerformance(@RequestParam("startTime") String startTime,
                                                                              @RequestParam("endTime") String endTime,
                                                                              @RequestParam("dateType") String dateType
    ) {

        Long familyId = dataScopeHandler.getFamilyForBase();
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId = dataScopeHandler.getRoomForRoomOrDefault(null);
        log.info("getAnalysisPerformance appId={}, familyId={}, roomId={}", appId, familyId, roomId);

        RequestGetRoomMsgAnalysisPerformance request = new RequestGetRoomMsgAnalysisPerformance()
                .setAppId(appId)
                .setFamilyId(familyId)
                .setUserId(userId)
                .setStartDate(startTime)
                .setEndDate(endTime)
                .setRoomId(roomId)
                .setDateType(dateType)
                ;
        Result<ResponseRoomMsgAnalysisPerformance> result = roomHomeService.getRoomMsgAnalysisPerformance(request);
        if (RpcResult.isFail(result)) {
            log.error("getAnalysisPerformance fail, appId={}, familyId={}, roomId={}, rCode={}", appId, familyId, roomId, result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(RoomHomeConvert.I.toRoomMsgAnalysisPerformanceResult(result.target()));
    }

}
