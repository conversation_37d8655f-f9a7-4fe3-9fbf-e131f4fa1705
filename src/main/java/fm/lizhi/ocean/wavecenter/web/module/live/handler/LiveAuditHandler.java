package fm.lizhi.ocean.wavecenter.web.module.live.handler;


import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.AddAuditRecordFullParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveInfoBean;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveAuditService;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.AuditRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Component
public class LiveAuditHandler {


    @Autowired
    private AppConfig appConfig;

    @Autowired
    private LiveAuditService liveAuditService;

    @Autowired
    private UserFamilyService userFamilyService;

    @Autowired
    private LiveService liveService;


    /**
     * 新增审核记录
     *
     * @param auditDto
     */
    public void addLiveAuditRecord(AuditRecordDto auditDto) {
        // 参数检测
        boolean paramCheck = paramCheck(auditDto);
        if (!paramCheck) {
            return;
        }
        int appId = getAppId(auditDto.getAppName());
        if (appId <= 0) {
            log.info("audit consumer app not support. appName={}", auditDto.getAppName());
            return;
        }

        AddAuditRecordFullParamBean.AddAuditRecordFullParamBeanBuilder addParamBuilder = AddAuditRecordFullParamBean.builder()
                .auditId(auditDto.getParentId())
                .recordId(auditDto.getRecordId())
                .bizId(auditDto.getBelongId())
                .userId(auditDto.getFromUserId())
                .toUserId(auditDto.getToUserId())
                .op(auditDto.getOp())
                .reason(auditDto.getReason())
                .auditEndTime(new Date(auditDto.getInsertTime()))
                .auditStartTime(new Date(auditDto.getCreateTime()))
                .sceneType(auditDto.getType())
                .sceneName(auditDto.getTypeStr())
                .pushTime(auditDto.getPunishTime())
                .sourceContentUrl(auditDto.getContent() == null ? "" : auditDto.getContent())
                .appId(appId);

        fillSignInfo(appId, addParamBuilder, auditDto);
        fillLiveSignInfo(appId, addParamBuilder, auditDto);

        Result<Void> addAuditRecord = liveAuditService.addAuditRecordFullInfo(addParamBuilder.build());
        if (RpcResult.isFail(addAuditRecord)) {
            log.error("addLiveAuditRecord addAuditRecord error  auditDto={} rCode={}", JSONObject.toJSONString(auditDto), addAuditRecord.rCode());
        }
    }

    /**
     * 填充违规用户签约信息
     *
     * @param addParamBuilder
     * @param auditDto
     */
    private void fillSignInfo(int appId, AddAuditRecordFullParamBean.AddAuditRecordFullParamBeanBuilder addParamBuilder, AuditRecordDto auditDto) {
        Long fromUserId = auditDto.getFromUserId();
        Result<UserInFamilyBean> result = userFamilyService.getUserInFamily(appId, fromUserId);
        if (RpcResult.isFail(result)) {
            log.warn("fillSignInfo getUserInFamily fail. rCode={},appId={},userId={}", result.rCode(), appId, fromUserId);
            return;
        }

        if (result.target().isPlayer()) {
            log.info("fillSignInfo isPlayer. userId={}", fromUserId);
            addParamBuilder.signNjId(result.target().getNjId())
                    .signFamilyId(result.target().getFamilyId());
        } else if (result.target().isRoom()) {
            log.info("fillSignInfo isRoom. userId={}", fromUserId);
            addParamBuilder.signNjId(fromUserId)
                    .signFamilyId(result.target().getFamilyId());
        } else if (result.target().isFamily()) {
            log.info("fillSignInfo isFamily. userId={}", fromUserId);
            addParamBuilder.signFamilyId(result.target().getFamilyId());
        } else {
            log.info("fillSignInfo empty role. userId={}", fromUserId);
        }
    }

    /**
     * 填充直播签约信息
     *
     * @param addParamBuilder
     * @param auditDto
     */
    private void fillLiveSignInfo(int appId, AddAuditRecordFullParamBean.AddAuditRecordFullParamBeanBuilder addParamBuilder, AuditRecordDto auditDto) {
        Long liveId = auditDto.getBelongId();
        if (liveId == null) {
            log.info("liveId is null. recordId={}", auditDto.getRecordId());
            return;
        }

        // belongId在非直播间场景不是liveId, 所以可能查不到
        Result<LiveInfoBean> result = liveService.getLiveInfo(appId, liveId);
        if (RpcResult.isFail(result)) {
            log.warn("fillLiveSignInfo getLiveInfo fail. rCode={},appId={},liveId={}", result.rCode(), appId, liveId);
            return;
        }
        Long userId = result.target().getUserId();
        log.info("fillLiveSignInfo getLiveInfo. liveId={},userId={}", liveId, userId);

        //签约信息
        Result<UserInFamilyBean> familyRes = userFamilyService.getUserInFamily(appId, userId);
        if (RpcResult.isFail(familyRes)) {
            log.warn("fillLiveSignInfo getUserInFamily fail. rCode={},appId={},njId={}", familyRes.rCode(), appId, userId);
            return;
        }

        Long fromUserId = auditDto.getFromUserId();
        //自己开播的情况
        if (Objects.equals(userId, fromUserId)) {
            log.info("fillLiveSignInfo selflive. liveId={},userId={}", liveId, userId);
            addParamBuilder.bizNjId(userId)
                    .bizFamilyId(familyRes.target().getFamilyId());
        } else {
            if (familyRes.target().isPlayer()) {
                //签约陪玩自己开直播，记录为陪玩自己即可
                log.info("fillLiveSignInfo isPlayer. liveId={},userId={}", liveId, userId);
                addParamBuilder.bizNjId(userId);
            } else if (familyRes.target().isRoom()) {
                log.info("fillLiveSignInfo isRoom. liveId={},userId={}", liveId, userId);
                addParamBuilder.bizNjId(userId)
                        .bizFamilyId(familyRes.target().getFamilyId());
            } else if (familyRes.target().isFamily()) {
                log.info("fillLiveSignInfo isFamily. liveId={},userId={}", liveId, userId);
                addParamBuilder.bizFamilyId(familyRes.target().getFamilyId());
            } else {
                //普通用户主播, 只保存用户id，自己就是厅主
                addParamBuilder.bizNjId(userId);
                log.info("fillLiveSignInfo empty role. liveId={},userId={}", liveId, userId);
            }
        }

    }

    private int getAppId(String appName) {
        String auditRecordSaveApp = appConfig.getAuditRecordSaveApp();
        if (!auditRecordSaveApp.contains(appName)) {
            return -1;
        }
        if (BusinessEvnEnum.PP.getName().equals(appName)) {
            return BusinessEvnEnum.PP.getAppId();
        } else if (BusinessEvnEnum.HEI_YE.getName().equals(appName)) {
            return BusinessEvnEnum.HEI_YE.getAppId();
        } else if (BusinessEvnEnum.XIMI.getName().equals(appName)) {
            return BusinessEvnEnum.XIMI.getAppId();
        }
        return -1;
    }

    /**
     * 参数检测
     *
     * @param auditRecordDto
     * @return
     */
    private boolean paramCheck(AuditRecordDto auditRecordDto) {
        String auditOp = appConfig.getAuditOp();
        String[] ops = auditOp.split(",");
        Set<Integer> opSet = Stream.of(ops).map(Integer::parseInt).collect(Collectors.toSet());

        // op检测
        if (!opSet.contains(auditRecordDto.getOp())) {
            log.info("audit consumer op not support. op={},parentId={}", auditRecordDto.getOp(), auditRecordDto.getParentId());
            return false;
        }

        if (auditRecordDto.getFromUserId() == null) {
            log.info("audit consumer fromUserId is null. parentId={}", auditRecordDto.getParentId());
            return false;
        }

        if (StringUtils.isEmpty(auditRecordDto.getAppName())) {
            log.info("audit consumer appName is empty. parentId={}", auditRecordDto.getParentId());
            return false;
        }
        return true;
    }
}
