package fm.lizhi.ocean.wavecenter.web.common.aspect;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.context.RpcContext;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import lombok.Data;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 在rpc调用上下文中填充业务信息. 该类的作用于web-ocean-wave项目的FillRpcContextSlot类相同, 因为目前只有lz-ocean-wave-api有该需求,
 * 因此先直接使用包目录进行匹配, 后续再替换为SPI实现形式.
 */
@Aspect
@Component
@Order(1000)
public class FillRpcContextAspect {

    private static final String WAVE_WEB_INFO = "WAVE_WEB_INFO";

    @Around("execution(public fm.lizhi.commons.service.client.pojo.Result fm.lizhi.ocean.wave.platform.api..*(..))")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        BusinessEvnEnum businessEvnEnum = ContextUtils.getBusinessEvnEnum();
        if (businessEvnEnum == null) {
            return joinPoint.proceed();
        }
        String oldValue = RpcContext.getContext().getAttachment(WAVE_WEB_INFO);
        try {
            WaveWebInfo waveWebInfo = new WaveWebInfo();
            waveWebInfo.setBusinessEvnName(businessEvnEnum.getBusinessEnv());
            String newValue = JsonUtil.dumps(waveWebInfo);
            RpcContext.getContext().setAttachment(WAVE_WEB_INFO, newValue);
            return joinPoint.proceed();
        } finally {
            RpcContext.getContext().setAttachment(WAVE_WEB_INFO, oldValue);
        }
    }

    /**
     * 参照web-ocean-wave项目的WaveWebInfo
     */
    @Data
    private static class WaveWebInfo {

        /**
         * 部署环境信息中的businessEnv
         */
        private String businessEvnName;
    }
}
