package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * 个播收入
 * <AUTHOR>
 * @date 2024/4/24 14:47
 */
@Data
public class GetPlayerIncomeDetailParamVo extends GetDetailBaseParamVo {


    @Min(value = 1)
    private Integer pageNo;

    @Min(value = 1)
    @Max(value = 100)
    private Integer pageSize;

    /**
     * 收入类型
     */
    private List<Integer> incomeType;

}
