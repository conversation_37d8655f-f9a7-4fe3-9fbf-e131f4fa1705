package fm.lizhi.ocean.wavecenter.web.module.common.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;


@Data
public class VersionNotificationConfigVo {
    
    private List<Map<String, Object>> guideConfigs;



    @Data
    public static class VersionGuideConfig {

        /**
         * 前端导航key
         */
        private String key;
        /**
         * 停止曝光的代码版本
         */
        private String stopExposeVersion;

        /**
         * 上新发布的版本
         */
        private String featureVersion;

        /**
         * 显示的内容
         */
        private String type;
    }
}
