package fm.lizhi.ocean.wavecenter.web.module.common.controller;

import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/server")
public class ServerConfigController {

    @Autowired
    private AppConfig appConfig;

    /**
     * 查询服务端时间
     * @return
     */
    @GetMapping("/time")
    @VerifyUserToken(required = false)
    public ResultVO<Long> getServerTime() {
        String testServerTimeMillis = appConfig.getTestServerTimeMillis();
        if (StringUtils.isNotBlank(testServerTimeMillis)) {
            return ResultVO.success(Long.valueOf(testServerTimeMillis));
        }
        return ResultVO.success(System.currentTimeMillis());
    }

}
