package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityLevelInfoBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityLevelService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityLevelConvert;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityLevelResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityLevelRecCardConfigResult;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityRecommendCardConfigService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/activity/level")
public class ActivityLevelController {

    @Autowired
    private ActivityLevelService activityLevelService;

    @Autowired
    private ActivityRecommendCardConfigService activityRecommendCardConfigService;

    @GetMapping("/list")
    @VerifyUserToken
    public ResultVO<List<ActivityLevelResult>> list() {
        Result<List<ActivityLevelInfoBean>> result = activityLevelService.listByAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        if (RpcResult.isFail(result)) {
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success(ActivityLevelConvert.I.convertResponseActivityLevel2ActivityLevelVO(result.target()));
    }

    /**
     * 查询等级对应推荐卡配置
     */
    @GetMapping("/recCardConfig")
    @VerifyUserToken
    public ResultVO<List<ActivityLevelRecCardConfigResult>> recCardConfig() {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        // 获取等级信息
        Result<List<ActivityLevelInfoBean>> levelResult = activityLevelService.listByAppId(appId);
        if (RpcResult.isFail(levelResult)) {
            log.warn("get activity level fail, appId: {}, rCode: {}", appId, levelResult.rCode());
            return ResultVO.failure(levelResult.getMessage());
        }

        // 获取推荐卡配置信息
        Result<List<ActivityRecommendCardConfigBean>> cardConfigResult = activityRecommendCardConfigService.listByAppId(appId);
        if (RpcResult.isFail(cardConfigResult)) {
            log.warn("get activity recommend card config fail, appId: {}, rCode: {}", appId, cardConfigResult.rCode());
            return ResultVO.failure(cardConfigResult.getMessage());
        }

        // 将推荐卡配置按等级ID分组，便于后续查找
        Map<Long, ActivityRecommendCardConfigBean> cardConfigMap = cardConfigResult.target().stream()
                .collect(Collectors.toMap(ActivityRecommendCardConfigBean::getLevelId, config -> config, (existing, replacement) -> existing));

        List<ActivityLevelRecCardConfigResult> results = new ArrayList<>();

        buildRecConfigResult(levelResult, cardConfigMap, results);

        return ResultVO.success(results);
    }

    private static void buildRecConfigResult(Result<List<ActivityLevelInfoBean>> levelResult, Map<Long, ActivityRecommendCardConfigBean> cardConfigMap, List<ActivityLevelRecCardConfigResult> results) {
        // 遍历等级信息，合并推荐卡配置数据
        for (ActivityLevelInfoBean levelBean : levelResult.target()) {
            ActivityLevelRecCardConfigResult result = new ActivityLevelRecCardConfigResult();
            Long levelId = levelBean.getId() != null ? levelBean.getId() : 0L;
            result.setLevelId(levelId);
            result.setLevelName(levelBean.getLevel());

            // 创建推荐卡配置信息
            ActivityLevelRecCardConfigResult.RecCardInfo recCardInfo = new ActivityLevelRecCardConfigResult.RecCardInfo();

            // 从推荐卡配置中获取对应等级的配置
            ActivityRecommendCardConfigBean configBean = cardConfigMap.get(levelId);
            if (configBean != null) {
                // 使用配置服务返回的数据
                recCardInfo.setValidDay(configBean.getValidDay());
                recCardInfo.setCount(configBean.getCount());
            }

            result.setRecCardInfo(recCardInfo);
            results.add(result);
        }
    }

}
