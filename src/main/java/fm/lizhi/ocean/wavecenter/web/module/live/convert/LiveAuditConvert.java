package fm.lizhi.ocean.wavecenter.web.module.live.convert;


import fm.lizhi.content.review.constant.OperationMarkEnum;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface LiveAuditConvert {

    LiveAuditConvert I = Mappers.getMapper(LiveAuditConvert.class);

    GuildAuditRecordStatsVo beanTo2GuildAuditRecordVo(GuildAuditRecordStatsBean bean);

    RoomAuditRecordStatsVo beanTo2RoomAuditRecordStatsBean(RoomAuditRecordStatsBean bean);


    @Mappings({
            @Mapping(target = "op", source = "bean",qualifiedByName = "guildOpConvert"),
    })
    GuildAuditRecordVo beanTo2GuildAuditRecordVo(GuildAuditRecordBean bean);


    @Mappings({
            @Mapping(target = "op", source = "bean",qualifiedByName = "roomOpConvert"),
    })
    RoomAuditRecordVo beanTo2RoomAuditRecordBean(RoomAuditRecordBean bean);


    @Mappings({
            @Mapping(target = "op", source = "bean",qualifiedByName = "playerOpConvert"),
    })
    PlayerAuditRecordVo beanTo2PlayerAuditRecordVo(PlayerAuditRecordBean bean);


    @Named("guildOpConvert")
    default String guildOpConvert(GuildAuditRecordBean bean){
        String opName = OperationMarkEnum.getDescByOp(bean.getOp());
        String pushTimeStr = "";
        if (StringUtils.isNotBlank(bean.getPushTime())) {
            pushTimeStr = bean.getPushTime();
        }

        return opName + pushTimeStr;
    }

    @Named("roomOpConvert")
    default String roomOpConvert(RoomAuditRecordBean bean){
        String opName = OperationMarkEnum.getDescByOp(bean.getOp());
        String pushTimeStr = "";
        if (StringUtils.isNotBlank(bean.getPushTime())) {
            pushTimeStr = bean.getPushTime();
        }

        return opName + pushTimeStr;
    }

    @Named("playerOpConvert")
    default String playerOpConvert(PlayerAuditRecordBean bean){
        String opName = OperationMarkEnum.getDescByOp(bean.getOp());
        String pushTimeStr = "";
        if (StringUtils.isNotBlank(bean.getPushTime())) {
            pushTimeStr = bean.getPushTime();
        }

        return opName + pushTimeStr;
    }


    @Mapping(target = "playerNickname", source = "vo.player.name")
    @Mapping(target = "playerBand", source = "vo.player.band")
    PlayerAuditRecordExportVo convertPlayerAuditRecordExportVo(PlayerAuditRecordVo vo);

    @Mapping(target = "roomName", source = "vo.roomBean.name")
    @Mapping(target = "playerNickname", source = "vo.player.name")
    @Mapping(target = "playerBand", source = "vo.player.band")
    @Mapping(target = "njId", source = "vo.roomBean.id")
    GuildAuditRecordExportVo convertGuildAuditRecordExportVo(GuildAuditRecordVo vo);

    @Mapping(target = "roomName", source = "vo.roomBean.name")
    @Mapping(target = "playerNickname", source = "vo.player.name")
    @Mapping(target = "playerBand", source = "vo.player.band")
    @Mapping(target = "njId", source = "vo.roomBean.id")
    RoomAuditRecordExportVo convertRoomAuditRecordExportVo(RoomAuditRecordVo vo);

}
