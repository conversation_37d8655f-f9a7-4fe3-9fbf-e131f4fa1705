package fm.lizhi.ocean.wavecenter.web.module.income.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestGuildIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestGuildIncomeSummary;
import fm.lizhi.ocean.wavecenter.api.income.response.ResponseGuildIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomeGuildService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.util.WcAssert;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.convert.DataCenterConvert;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.GuildIncomeSummaryVo;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.income.convert.IncomeConvert;
import fm.lizhi.ocean.wavecenter.web.module.income.processor.IIncomeProcessor;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 公会收益
 *
 * <AUTHOR>
 * @date 2024/4/23 14:44
 */
@RestController
@RequestMapping("income/guild/")
public class IncomeGuildController {

    private static final Logger log = LoggerFactory.getLogger(IncomeGuildController.class);

    @Autowired
    private IncomeGuildService incomeService;

    @Autowired
    private FileExportHandler fileExportHandler;

    @Autowired
    private ProcessorFactory processorFactory;

    @Autowired
    private DataScopeHandler dataScopeHandler;

    /**
     * tab按钮汇总
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/sum")
    public ResultVO<GuildIncomeSummaryVo> guildIncomeSummary() {
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        List<Long> roomResource = ContextUtils.getContext().getRoomResource();
        RequestGuildIncomeSummary request = new RequestGuildIncomeSummary()
                .setFamilyId(familyId)
                .setRoomIds(roomResource)
                .setAppId(appId);
        Result<GuildIncomeSummaryBean> result = incomeService.guildIncomeSummaryV2(request);
        if (RpcResult.isFail(result)) {
            log.error("guildIncomeSummaryV2 fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return ResultVO.failure();
        }
        return ResultVO.success(DataCenterConvert.I.guildIncomeSummaryBean2Vo(result.target()));
    }

    /**
     * 签约厅收入明细
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("roomDetail")
    public ResultVO<PageVO<GuildRoomIncomeDetailVo>> roomIncomeDetail(@Validated GuildRoomIncomeDetailParamVo paramVo) {
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();

        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        Long flushTime = endDate.getTime() > paramVo.getFlushTime() ? paramVo.getFlushTime() : endDate.getTime();
        endDate = new DateTime(flushTime);
        // 以上处理快照时间版本

        //全部返回
        SignRoomIncomeDetailParamBean signRoomIncomeDetailParam = new SignRoomIncomeDetailParamBean()
                .setAppId(appId).setFamilyId(familyId).setNjId(paramVo.getRoomId())
                .setStartDate(startDate)
                .setEndDate(endDate)
                .setRoomIds(ContextUtils.getContext().getRoomResource())
                .setPage(1).setPageSize(500);
        Result<PageBean<RoomIncomeDetailBean>> result = incomeService.signRoomIncomeDetail(signRoomIncomeDetailParam);
        if (RpcResult.isFail(result)) {
            log.error("roomIncomeDetail rpc fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(signRoomIncomeDetailParam));
            return ResultVO.failure();
        }
        List<RoomIncomeDetailBean> list = result.target().getList();
        List<GuildRoomIncomeDetailVo> voList = IncomeConvert.I.guildRoomIncomeDetailBeans2Vos(list);
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList, flushTime));
    }

    /**
     * 签约厅收入明细-导出
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("roomDetail/export")
    public ResultVO<Void> roomIncomeDetailExport(@Validated GuildRoomIncomeDetailParamVo paramVo) {
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();

        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        Long flushTime = endDate.getTime() > paramVo.getFlushTime() ? paramVo.getFlushTime() : endDate.getTime();
        endDate = new DateTime(flushTime);

        IIncomeProcessor processor = processorFactory.getProcessor(IIncomeProcessor.class);
        DateTime finalEndDate1 = endDate;
        List<Long> roomResource = ContextUtils.getContext().getRoomResource();
        return processor.roomIncomeDetailExport((pageNo, pageSize) -> {
            SignRoomIncomeDetailParamBean signRoomIncomeDetailParam = new SignRoomIncomeDetailParamBean()
                    .setAppId(appId).setFamilyId(familyId).setNjId(paramVo.getRoomId())
                    .setStartDate(startDate)
                    .setEndDate(finalEndDate1)
                    .setRoomIds(roomResource)
                    .setPage(pageNo).setPageSize(pageSize);
            Result<PageBean<RoomIncomeDetailBean>> result = incomeService.signRoomIncomeDetail(signRoomIncomeDetailParam);
            if (RpcResult.isFail(result)) {
                log.error("roomIncomeDetailExport fail rCode={}", result.rCode());
                return PageBean.empty();
            }
            return result.target();
        });
    }

    /**
     * 公会收益-收入账户明细记录-导出
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/incomeDetail/export")
    public ResultVO<Void> guildIncomeDetailExport(@Validated GetGuildIncomeDetailExportParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        long flushTime = System.currentTimeMillis();
        String fileName = fileExportHandler.genFileName("公会收入账户明细");
        return fileExportHandler.exportFile(fileName, GuildIncomeDetailExportVo.class, (pageNo, pageSize) -> {
            GetGuildIncomeDetailParamBean.GetGuildIncomeDetailParamBeanBuilder builder = GetGuildIncomeDetailParamBean.builder()
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                    .familyId(ContextUtils.getContext().getSubjectId())
                    .flushTime(flushTime)
                    .roomId(paramVo.getRoomId())
                    .endDate(endDate)
                    .startDate(startDate);

            if (CollUtil.isNotEmpty(paramVo.getIncomeType())) {
                builder.incomeType(paramVo.getIncomeType().stream().map(IncomeType::valueOf).collect(Collectors.toList()));
            }

            Result<PageBean<GuildIncomeDetailBean>> result = incomeService.getGuildIncomeDetailOut(builder.build());
            if (RpcResult.isFail(result)) {
                log.error("guildIncomeDetailExport fail pageNo={}, pageSize={}, rCode={}, paramVo={}", pageNo
                        , pageSize, result.rCode(), JsonUtil.dumps(paramVo));
                return PageVO.empty();
            }
            int total = result.target().getTotal();
            List<GuildIncomeDetailBean> list = result.target().getList();
            return PageVO.of(total, IncomeConvert.I.guildIncomeDetailBeans2ExportVos(list));
        });

    }

    /**
     * 公会收益-收入账户明细记录
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/incomeDetail")
    public ResultVO<PageVO<GuildIncomeDetailVo>> guildIncomeDetail(@Validated GetGuildIncomeDetailParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);
        Long flushTime = endDate.getTime() > paramVo.getFlushTime() ? paramVo.getFlushTime() : endDate.getTime();

        GetGuildIncomeDetailParamBean.GetGuildIncomeDetailParamBeanBuilder builder = GetGuildIncomeDetailParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(ContextUtils.getContext().getSubjectId())
                .roomId(paramVo.getRoomId())
                .endDate(endDate)
                .startDate(startDate)
                .flushTime(flushTime)
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize());

        if (CollUtil.isNotEmpty(paramVo.getIncomeType())) {
            builder.incomeType(paramVo.getIncomeType().stream().map(IncomeType::valueOf).collect(Collectors.toList()));
        }

        Result<PageBean<GuildIncomeDetailBean>> result = incomeService.getGuildIncomeDetail(builder.build());
        if (RpcResult.isFail(result)) {
            log.error("guildIncomeDetail,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        PageBean<GuildIncomeDetailBean> target = result.target();
        List<GuildIncomeDetailVo> voList = IncomeConvert.I.guildIncomeDetailBeans2Vos(target.getList());
        return ResultVO.success(PageVO.of(target.getTotal(), voList, target.getFlushTime()));
    }


    /**
     * 公会收益-收入账户明细记录-合计
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/incomeDetail/sum")
    public ResultVO<GuildIncomeDetailSumVo> guildIncomeDetailSum(@Validated GetGuildIncomeDetailSumParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        GetGuildIncomeDetailSumParamBean.GetGuildIncomeDetailSumParamBeanBuilder builder = GetGuildIncomeDetailSumParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(ContextUtils.getContext().getSubjectId())
                .roomId(paramVo.getRoomId())
                .endDate(endDate)
                .startDate(startDate);

        if (CollUtil.isNotEmpty(paramVo.getIncomeType())) {
            builder.incomeType(paramVo.getIncomeType().stream().map(IncomeType::valueOf).collect(Collectors.toList()));
        }

        Result<GuildIncomeDetailSumBean> result = incomeService.getGuildIncomeDetailSum(builder.build());
        if (RpcResult.isFail(result)) {
            log.error("guildIncomeDetailSum,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }

        return ResultVO.success(IncomeConvert.I.guildIncomeDetailSumBean2Vo(result.target()));
    }

    /**
     * 获取厅关键统计数据
     *
     * @param statPeriod  统计周期
     * @param lastMinTime 上一次最小时间
     * @return 结果
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/offlineSum/list")
    public ResultVO<GuildIncomeSumResVO> guildOfflineSumList(Integer pageSize, String statPeriod, Long lastMinTime) {
        try {
            Long familyId = ContextUtils.getContext().getSubjectId();
            int appId = ContextUtils.getBusinessEvnEnum().getAppId();
            pageSize = pageSize == null ? 10 : pageSize;

            // 构建请求参数
            RequestGuildIncomeStats request = new RequestGuildIncomeStats();
            request.setFamilyId(familyId);
            request.setAppId(appId);
            request.setStatPeriod(statPeriod);
            request.setLastMinTime(lastMinTime);
            request.setPageSize(pageSize);

            // 调用服务方法
            Result<ResponseGuildIncomeStats> result = incomeService.queryGuildIncomeStats(request);

            if (RpcResult.isFail(result)) {
                log.error("queryGuildIncomeStats fail. rCode={}, request={}", result.rCode(), JsonUtil.dumps(request));
                return ResultVO.failure("查询失败");
            }

            GuildIncomeSumResVO guildIncomeSumResVO = IncomeConvert.I.guildIncomeStatsBeans2Vos(result.target());
            return ResultVO.success(guildIncomeSumResVO);

        } catch (Exception e) {
            log.error("guildOfflineSumList error", e);
            return ResultVO.failure("查询失败");
        }
    }

}
