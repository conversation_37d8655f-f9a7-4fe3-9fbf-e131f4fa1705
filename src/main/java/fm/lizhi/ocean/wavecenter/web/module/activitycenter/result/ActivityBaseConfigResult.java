package fm.lizhi.ocean.wavecenter.web.module.activitycenter.result;


import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ActivityBaseConfigResult {

    /**
     * 应用信息列表
     */
    private List<ActivityBaseConfigVO> appInfoList;

    /**
     * 自动配置资源列表
     */
    private List<ActivityAutoConfigResourceVO> autoConfigResourceList;

    /**
     * 提报限制列表
     */
    private List<ActivityBaseConfigVO> applyRuleList;

    /**
     * 玩法工具列表
     */
    private List<ActivityBaseConfigVO> activityToolList;

    /**
     * 素材分类
     */
    private List<ActivityBaseConfigVO> fodderClassificationList;

    /**
     * 装扮类型
     */
    private List<ActivityBaseConfigVO> decorateTypeList;


    /**
     * 亮点标签
     */
    private List<ActivityTemplateHighlightConfigVO> heightLightList;

    /**
     * 大分类类型列表
     */
    private List<ActivityBigClassTypeConfigVO> bigClassTypeList;

    /**
     * 房间品类列表
     */
    private List<RoomCategoryVO> roomCategoryList;

    /**
     * 奖励标签列表
     */
    private List<ActivityResourceEnumVO> rewardTagList;

}
