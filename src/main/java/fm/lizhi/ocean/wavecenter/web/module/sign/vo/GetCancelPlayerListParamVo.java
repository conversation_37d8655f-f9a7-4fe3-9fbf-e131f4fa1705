package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/10/8 11:18
 */
@Data
public class GetCancelPlayerListParamVo {

    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNo = 1;

    @Max(value = 100, message = "每页最大条数不能超过100")
    private Integer pageSize = 10;

    /**
     * 状态
     */
    @NotBlank(message = "状态不可为空")
    private String status;

    /**
     * 搜索用户波段号
     */
    private String userBand;

}
