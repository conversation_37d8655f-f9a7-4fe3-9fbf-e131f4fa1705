package fm.lizhi.ocean.wavecenter.web.module.message.result;

import fm.lizhi.ocean.wavecenter.web.module.message.vo.MessageVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-10-30 04:25:18
 */
@Data
@Accessors(chain = true)
public class QueryRecentMessagesResult {

    /**
     * 消息列表
     */
    private List<MessageVO> messageList;

    /**
     * 未读数
     */
    private Long unRead;
}