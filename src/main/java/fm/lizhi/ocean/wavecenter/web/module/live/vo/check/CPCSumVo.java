package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import fm.lizhi.ocean.wavecenter.web.common.BigDecimalDownSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/12 20:27
 */
@Data
public class CPCSumVo {

    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal income;

    private String charm;

    private String seatOrder;

    private String hostCnt;

}
