package fm.lizhi.ocean.wavecenter.web.module.user.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/21 14:11
 */
@Data
public class SearchFamilyVo {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    private String familyName;

    /**
     * 家族简介
     */
    private String familyIntro;

    /**
     * 家族头像
     */
    private String familyIconUrl;

    /**
     * 家族创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

}
