package fm.lizhi.ocean.wavecenter.web.module.income.convert;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.response.ResponseGuildIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.response.ResponseRoomIncomeStats;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.export.hy.HyGuildRoomIncomeDetailExportVo;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.export.hy.HyRoomSignPlayerIncomeExportVo;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.export.pp.PpGuildRoomIncomeDetailExportVo;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.export.pp.PpRoomSignPlayerIncomeExportVo;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.export.xm.XmGuildRoomIncomeDetailExportVo;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.export.xm.XmRoomSignPlayerIncomeExportVo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 15:48
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface IncomeConvert {

    IncomeConvert I = Mappers.getMapper(IncomeConvert.class);

    GuildIncomeDetailVo guildIncomeDetailBean2Vo(GuildIncomeDetailBean bean);

    List<GuildIncomeDetailVo> guildIncomeDetailBeans2Vos(List<GuildIncomeDetailBean> beans);

    GuildIncomeDetailSumVo guildIncomeDetailSumBean2Vo(GuildIncomeDetailSumBean bean);

    @Mappings({
            @Mapping(source = "roomInfo.name", target = "name"),
            @Mapping(source = "roomInfo.band", target = "band"),
            @Mapping(source = "incomeTypeName", target = "incomeName"),
    })
    GuildIncomeDetailExportVo guildIncomeDetailBean2ExportVo(GuildIncomeDetailBean bean);

    List<GuildIncomeDetailExportVo> guildIncomeDetailBeans2ExportVos(List<GuildIncomeDetailBean> beans);

    RoomIncomeDetailExportVo RoomIncomeDetailExportVo2ExportVo(fm.lizhi.ocean.wavecenter.api.income.bean.RoomIncomeDetailBean bean);

    List<RoomIncomeDetailExportVo> roomIncomeDetailBeans2ExportVos(List<fm.lizhi.ocean.wavecenter.api.income.bean.RoomIncomeDetailBean> beans);

    PersonalIncomeDetailVo personalIncomeDetailBean2Vo(PersonalIncomeDetailBean bean);

    List<PersonalIncomeDetailVo> personalIncomeDetailBeans2Vos(List<PersonalIncomeDetailBean> beans);

    PersonalIncomeDetailExportVo personalIncomeDetailBean2ExportVo(PersonalIncomeDetailBean bean);

    List<PersonalIncomeDetailExportVo> personalIncomeDetailBeans2ExportVos(List<PersonalIncomeDetailBean> beans);

    List<PersonalRevenueIncomeDetailExportVo> personalRevenueIncomeDetailBeans2ExportVos(List<PersonalIncomeDetailBean> beans);

    PlayerIncomeDetailVo playerIncomeDetailBean2Vo(PlayerIncomeDetailBean bean);

    List<PlayerIncomeDetailVo> playerIncomeDetailBeans2Vos(List<PlayerIncomeDetailBean> beans);

    PlayerIncomeDetailExportVo playerIncomeDetailBean2ExportVo(PlayerIncomeDetailBean bean);

    List<PlayerIncomeDetailExportVo> playerIncomeDetailBeans2ExportVos(List<PlayerIncomeDetailBean> beans);

    PersonalIncomeDetailSumVo personalIncomeDetailSumBean2Vo(PersonalIncomeDetailSumBean bean);

    PlayerIncomeDetailSumVo playerIncomeDetailSumBean2Vo(PlayerIncomeDetailSumBean bean);
    RoomSignRoomVo roomSignRoomBean2Vo(RoomSignRoomBean bean);

    List<RoomSignRoomVo> roomSignRoomBeans2Vos(List<RoomSignRoomBean> beans);

    @Mappings({
            @Mapping(target = "recRoomName", source = "bean.recRoomInfo.name"),
            @Mapping(target = "recRoomBand", source = "bean.recRoomInfo.band"),
            @Mapping(target = "sendUserName", source = "bean.sendUserInfo.name"),
            @Mapping(target = "sendUserBand", source = "bean.sendUserInfo.band"),
            @Mapping(target = "recUserName", source = "bean.recUserInfo.name"),
            @Mapping(target = "recUserBand", source = "bean.recUserInfo.band"),
    })
    RoomSignRoomExportVo roomSignRoomBean2ExportVo(RoomSignRoomBean bean);

    List<RoomSignRoomExportVo> roomSignRoomBeans2ExportVos(List<RoomSignRoomBean> beans);

    @Mappings({
            @Mapping(target = "officialIncome", source = "officialHallIncome"),
            @Mapping(target = "sumIncome", source = "checkIncome"),
    })
    RoomSignPlayerIncomeVo roomSignPlayerIncomeBean2Vo(RoomSignPlayerIncomeBean bean);

    List<RoomSignPlayerIncomeVo> roomSignPlayerIncomeBeans2Vos(List<RoomSignPlayerIncomeBean> beans);

    @Mappings({
            @Mapping(target = "playerName", source = "bean.playerInfo.name"),
            @Mapping(target = "playerBand", source = "bean.playerInfo.band"),
    })
    PpRoomSignPlayerIncomeExportVo ppRoomSignPlayerIncomeBeans2ExportVo(RoomSignPlayerIncomeBean bean);

    List<PpRoomSignPlayerIncomeExportVo> ppRoomSignPlayerIncomeBeans2ExportVos(List<RoomSignPlayerIncomeBean> beans);

    @Mappings({
            @Mapping(target = "playerName", source = "bean.playerInfo.name"),
            @Mapping(target = "playerBand", source = "bean.playerInfo.band"),
    })
    HyRoomSignPlayerIncomeExportVo hyRoomSignPlayerIncomeBeans2ExportVo(RoomSignPlayerIncomeBean bean);

    List<HyRoomSignPlayerIncomeExportVo> hyRoomSignPlayerIncomeBeans2ExportVos(List<RoomSignPlayerIncomeBean> beans);

    @Mappings({
            @Mapping(target = "playerName", source = "bean.playerInfo.name"),
            @Mapping(target = "playerBand", source = "bean.playerInfo.band"),
    })
    XmRoomSignPlayerIncomeExportVo xmRoomSignPlayerIncomeBeans2ExportVo(RoomSignPlayerIncomeBean bean);

    List<XmRoomSignPlayerIncomeExportVo> xmRoomSignPlayerIncomeBeans2ExportVos(List<RoomSignPlayerIncomeBean> beans);


    PlayerSumResVo playerSumResBean2Vo(PlayerSumResBean bean);

    PlayerSumTimeVo playerSumTimeBean2Vo(PlayerSumTimeBean bean);

    PlayerSumDataVo playerSumDataBean2Vo(PlayerSumDataBean bean);

    PersonalGiftflowVo personalGiftflowBean2Vo(PersonalGiftflowBean bean);

    List<PersonalGiftflowVo> personalGiftflowBeans2Vos(List<PersonalGiftflowBean> beans);

    @Mappings({
            @Mapping(target = "recRoomBand", source = "recRoomInfo.band"),
            @Mapping(target = "recRoomName", source = "recRoomInfo.name"),
            @Mapping(target = "content", source = "content"),
            @Mapping(target = "sendUserName", source = "sendUserInfo.name"),
            @Mapping(target = "sendUserBand", source = "sendUserInfo.band"),
    })
    PersonalGiftflowExportVo personalGiftflowBean2ExportVo(PersonalGiftflowBean bean);

    List<PersonalGiftflowExportVo> personalGiftflowBeans2ExportVos(List<PersonalGiftflowBean> beans);

    PlayerRoomGiftflowVo playerRoomGiftflowBean2Vo(PlayerRoomGiftflowBean bean);

    List<PlayerRoomGiftflowVo> playerRoomGiftflowBeans2Vos(List<PlayerRoomGiftflowBean> beans);

    @Mappings({
            @Mapping(target = "recRoomName", source = "bean.recRoomInfo.name"),
            @Mapping(target = "recRoomBand", source = "bean.recRoomInfo.band"),
            @Mapping(target = "sendUserName", source = "bean.sendUserInfo.name"),
            @Mapping(target = "sendUserBand", source = "bean.sendUserInfo.band"),
            @Mapping(target = "recUserName", source = "bean.recUserInfo.name"),
            @Mapping(target = "recUserBand", source = "bean.recUserInfo.band"),
    })
    PlayerRoomGiftflowExportVo playerRoomGiftflowBean2ExportVo(PlayerRoomGiftflowBean bean);

    List<PlayerRoomGiftflowExportVo> playerRoomGiftflowBeans2ExportVos(List<PlayerRoomGiftflowBean> beans);

    GuildRoomIncomeDetailVo guildRoomIncomeDetailBean2Vo(RoomIncomeDetailBean bean);

    List<GuildRoomIncomeDetailVo> guildRoomIncomeDetailBeans2Vos(List<RoomIncomeDetailBean> beans);

    @Mappings({
            @Mapping(target = "roomName", source = "roomInfo.name"),
            @Mapping(target = "roomBand", source = "roomInfo.band"),
    })
    PpGuildRoomIncomeDetailExportVo ppGuildRoomIncomeDetailBeans2ExportVo(RoomIncomeDetailBean bean);

    List<PpGuildRoomIncomeDetailExportVo> ppGuildRoomIncomeDetailBeans2ExportVos(List<RoomIncomeDetailBean> beans);

    @Mappings({
            @Mapping(target = "roomName", source = "roomInfo.name"),
            @Mapping(target = "roomBand", source = "roomInfo.band"),
    })
    HyGuildRoomIncomeDetailExportVo hyGuildRoomIncomeDetailBeans2ExportVo(RoomIncomeDetailBean bean);

    List<HyGuildRoomIncomeDetailExportVo> hyGuildRoomIncomeDetailBeans2ExportVos(List<RoomIncomeDetailBean> beans);

    @Mappings({
            @Mapping(target = "roomName", source = "roomInfo.name"),
            @Mapping(target = "roomBand", source = "roomInfo.band"),
    })
    XmGuildRoomIncomeDetailExportVo xmGuildRoomIncomeDetailBeans2ExportVo(RoomIncomeDetailBean bean);

    List<XmGuildRoomIncomeDetailExportVo> xmGuildRoomIncomeDetailBeans2ExportVos(List<RoomIncomeDetailBean> beans);

    List<RoomIncomeDetailVo> roomIncomeDetailBeans2Vos(List<fm.lizhi.ocean.wavecenter.api.income.bean.RoomIncomeDetailBean> list);
    RoomIncomeDetailVo roomIncomeDetailBean2Vo(fm.lizhi.ocean.wavecenter.api.income.bean.RoomIncomeDetailBean bean);

    RoomIncomeDetailSumVo roomIncomeDetailSumBean2Vo(RoomIncomeDetailSumBean bean);

    RoomIncomeSumResVO roomIncomeStatsBeans2Vos(ResponseRoomIncomeStats beans);

    GuildIncomeSumResVO guildIncomeStatsBeans2Vos(ResponseGuildIncomeStats resp);

}
