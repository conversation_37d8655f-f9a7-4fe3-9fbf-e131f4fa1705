package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户资料完善状态
 * (UNFINISHED 未完成、FINISHED 已完成、PROCESSING 进行中/认证中、 AUDITING 审核中)
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SignUserStatusVo {

    /**
     * 用户信息状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.IdentifyStatusEnum
     */
    private String infoStatus;

    /**
     * 主播中心认证
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.IdentifyStatusEnum
     */
    private String playerCenterStatus;


}
