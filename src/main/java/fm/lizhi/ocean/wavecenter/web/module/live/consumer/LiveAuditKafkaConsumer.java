package fm.lizhi.ocean.wavecenter.web.module.live.consumer;


import com.alibaba.fastjson.JSON;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.ocean.wavecenter.web.module.live.handler.LiveAuditHandler;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.AuditRecordDto;
import fm.lizhi.ocean.wavecenter.web.util.KafkaMsgUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 同步审核结果
 * 用于违规记录查询
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "public-kafka250-bootstrap-server")
public class LiveAuditKafkaConsumer {

    @Autowired
    private LiveAuditHandler liveAuditHandler;

    @KafkaHandler(topic = "lz_audit_operation_result_to_wave", group = "lz_ocean_wavecenter_live_audit_group")
    public void handleLiveAuditMsg(String body) {
        log.info("handleLiveAuditMsg body={}",body);
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            AuditRecordDto auditDto = JSON.parseObject(msg, AuditRecordDto.class);
            liveAuditHandler.addLiveAuditRecord(auditDto);
        }catch (Exception e){
            log.error("handleLiveAuditMsg error body={}",body,e);
        }
    }

}
