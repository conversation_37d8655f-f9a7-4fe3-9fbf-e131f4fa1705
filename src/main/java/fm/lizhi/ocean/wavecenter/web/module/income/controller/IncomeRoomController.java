package fm.lizhi.ocean.wavecenter.web.module.income.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestRoomIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.response.ResponseRoomIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomeRoomService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.util.WcAssert;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.convert.DataCenterConvert;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.RoomIncomeSummaryVo;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.income.convert.IncomeConvert;
import fm.lizhi.ocean.wavecenter.web.module.income.processor.IIncomeProcessor;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserFamilyHandler;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 收益中心
 *
 * <AUTHOR>
 * @date 2024/4/23 14:44
 */
@RestController
@RequestMapping("income/room/")
public class IncomeRoomController {

    private static final Logger log = LoggerFactory.getLogger(IncomeRoomController.class);
    @Autowired
    private IncomeRoomService incomeService;
    @Autowired
    private FileExportHandler fileExportHandler;
    @Autowired
    private UserFamilyHandler userFamilyHandler;
    @Autowired
    private ProcessorFactory processorFactory;
    @Autowired
    private DataScopeHandler dataScopeHandler;

    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/sum")
    public ResultVO<RoomIncomeSummaryVo> roomIncomeSummary() {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        if (familyId == null) {
            return ResultVO.failure("未签约");
        }
        Long roomId = ContextUtils.getContext().getSubjectId();

        Result<RoomIncomeSummaryBean> result = incomeService.roomIncomeSummary(familyId, roomId, appId);
        if (RpcResult.isFail(result)) {
            log.error("roomIncomeSummary,rCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(DataCenterConvert.I.roomIncomeSummaryBean2Vo(result.target()));
    }

    /**
     * 厅收益-签约厅收礼流水记录
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("signRoom/sum")
    public ResultVO<RoomSignRoomSumResVo> roomSignRoomSum(@Validated GetRoomSignRoomParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        Long roomId = ContextUtils.getContext().getSubjectId();
        if ((ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin())
                && (roomId = paramVo.getRoomId()) == null) {
            return ResultVO.failure("必须选择其中一个签约厅");
        }

        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        GetRoomSignRoomParamBean.GetRoomSignRoomParamBeanBuilder builder = GetRoomSignRoomParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .roomId(roomId)
                .sendUserId(paramVo.getSendUserId())
                .recUserId(paramVo.getRecUserId())
                .startDate(startDate)
                .endDate(endDate);

        if (ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin()) {
            builder.familyId(ContextUtils.getContext().getSubjectId());
        }

        Result<RoomSignRoomSumResBean> result = incomeService.getRoomSignRoomSum(builder
                .build());
        if (RpcResult.isFail(result)) {
            log.error("roomSignRoomSum,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(new RoomSignRoomSumResVo()
                .setIncome(result.target().getIncome())
                .setCharm(result.target().getCharm())
        );
    }

    /**
     * 厅收益-签约厅收礼流水记录-导出
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("signRoom/export")
    public ResultVO<Void> roomSignRoomExport(@Validated GetRoomSignRoomExportParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());

        Long roomId = ContextUtils.getContext().getSubjectId();

        if ((ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin())
                && (roomId = paramVo.getRoomId()) == null) {
            return ResultVO.failure("必须选择其中一个签约厅");
        }
        long now = System.currentTimeMillis();
        Long flushTime = Math.min(endDate.getTime(), now);
        endDate = new DateTime(flushTime);

        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        String fileName = fileExportHandler.genFileName("签约厅收礼流水记录");
        Long finalRoomId = roomId;
        DateTime finalEndDate = endDate;
        return fileExportHandler.exportFile(fileName, RoomSignRoomExportVo.class, (pageNo, pageSize) -> {
            Result<PageBean<RoomSignRoomBean>> result = incomeService.getRoomSignRoom(GetRoomSignRoomParamBean.builder()
                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                    .roomId(finalRoomId)
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .sendUserId(paramVo.getSendUserId())
                    .recUserId(paramVo.getRecUserId())
                    .startDate(startDate)
                    .endDate(finalEndDate)
                    .build());
            if (RpcResult.isFail(result)) {
                log.error("roomSignRoomExport fail rCode={},pageNo={},pageSize={},startDate={},endDate={}"
                        , result.rCode()
                        , pageNo, pageSize, startDate, finalEndDate
                );
                return PageVO.empty();
            }
            PageBean<RoomSignRoomBean> pageBean = result.target();
            List<RoomSignRoomBean> list = pageBean.getList();
            return PageVO.of(pageBean.getTotal(), IncomeConvert.I.roomSignRoomBeans2ExportVos(list));
        });
    }

    /**
     * 厅收益-签约厅收礼流水记录
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("signRoom")
    public ResultVO<PageVO<RoomSignRoomVo>> roomSignRoom(@Validated GetRoomSignRoomParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        Long roomId = ContextUtils.getContext().getSubjectId();

        if ((ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin())
                && (roomId = paramVo.getRoomId()) == null) {
            log.info("need selected room");
            return ResultVO.failure("必须选择其中一个签约厅");
        }

        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            log.info("permissionError");
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        long flushTime = endDate.getTime() > paramVo.getFlushTime() ? paramVo.getFlushTime() : endDate.getTime();
        endDate = new DateTime(flushTime);

        GetRoomSignRoomParamBean.GetRoomSignRoomParamBeanBuilder builder = GetRoomSignRoomParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .roomId(roomId)
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .sendUserId(paramVo.getSendUserId())
                .recUserId(paramVo.getRecUserId())
                .startDate(startDate)
                .endDate(endDate);
        if (ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin()) {
            builder.familyId(ContextUtils.getContext().getSubjectId());
        }

        Result<PageBean<RoomSignRoomBean>> result = incomeService.getRoomSignRoom(builder.build());

        if (RpcResult.isFail(result)) {
            log.error("roomSignRoom,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }

        List<RoomSignRoomBean> list = result.target().getList();
        List<RoomSignRoomVo> voList = IncomeConvert.I.roomSignRoomBeans2Vos(list);
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList));
    }

    /**
     * 厅收益-签约主播收入-导出
     *
     * @return
     */
    @PermissionCheck(passRole = RoleEnum.ROOM)
    @VerifyUserToken
    @GetMapping("signPlayer/export")
    public ResultVO<Void> roomSignPlayerExport(@RequestParam("startDate") String startDate
            , @RequestParam("endDate") String endDate
            , @RequestParam(value = "playerId", required = false) Long playerId
    ) {
        WcAssert.hasText(startDate, "开始时间不可为空");
        WcAssert.hasText(endDate, "结束时间不可为空");

        DateTime startTime = DateUtil.parse(startDate + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endTime = DateUtil.parse(endDate + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        DateTime finalEndTime = getDateTime(endTime);
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long userId = ContextUtils.getContext().getSubjectId();

        IIncomeProcessor processor = processorFactory.getProcessor(IIncomeProcessor.class);
        return processor.roomSignPlayerExport((pageNo, pageSize) -> {
            GetRoomSignPlayerIncomeParamBean getRoomSignPlayerIncomeParamBean = GetRoomSignPlayerIncomeParamBean.builder().appId(appId).userId(userId)
                    .startDate(startTime).endDate(finalEndTime).pageNo(pageNo).pageSize(pageSize).build();
            Result<PageBean<RoomSignPlayerIncomeBean>> result = incomeService.getRoomSignPlayerIncome(getRoomSignPlayerIncomeParamBean);
            if (RpcResult.isFail(result)) {
                log.error("roomSignPlayerExport fail rCode={},startDate={},endDate={},playerId={}", result.rCode(), startDate, endDate, playerId);
                return PageBean.empty();
            }
            PageBean<RoomSignPlayerIncomeBean> pageBean = result.target();
            List<RoomSignPlayerIncomeBean> beanList = pageBean.getList();
            if (playerId != null) {
                pageBean.setList(beanList.stream().filter(v -> playerId.equals(v.getPlayerInfo().getId())).collect(Collectors.toList()));
            }
            return pageBean;
        });
    }

    @NotNull
    private static DateTime getDateTime(DateTime endTime) {
        long now = System.currentTimeMillis();
        long flushTime = Math.min(endTime.getTime(), now);
        endTime = new DateTime(flushTime);
        return endTime;
    }

    /**
     * 厅收益-签约主播收入-查询
     *
     * @return
     */
    @PermissionCheck(passRole = RoleEnum.ROOM)
    @VerifyUserToken
    @GetMapping("signPlayer")
    public ResultVO<PageVO<RoomSignPlayerIncomeVo>> roomSignPlayer(GetRoomSignPlayerParamVo paramVo) {

        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        Long flushTime = Math.min(endDate.getTime(), paramVo.getFlushTime());
        endDate = new DateTime(flushTime);

        Result<PageBean<RoomSignPlayerIncomeBean>> result = incomeService.getRoomSignPlayerIncome(GetRoomSignPlayerIncomeParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getSubjectId())
                .startDate(startDate)
                .endDate(endDate)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("roomSignPlayer,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        PageBean<RoomSignPlayerIncomeBean> pageBean = result.target();
        List<RoomSignPlayerIncomeVo> voList = IncomeConvert.I.roomSignPlayerIncomeBeans2Vos(pageBean.getList());
        return ResultVO.success(PageVO.of(pageBean.getTotal(), voList));
    }

    /**
     * 厅收益-收入账户明细记录
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/incomeDetail")
    public ResultVO<PageVO<RoomIncomeDetailVo>> roomIncomeDetail(@Validated GetRoomIncomeDetailParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);
        Long flushTime = endDate.getTime() > paramVo.getFlushTime() ? paramVo.getFlushTime() : endDate.getTime();

        Long familyId = userFamilyHandler.getUserFamilyId(ContextUtils.getContext().getSubjectId());
        GetRoomIncomeDetailParamBean.GetRoomIncomeDetailParamBeanBuilder builder = GetRoomIncomeDetailParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(familyId)
                .roomId(ContextUtils.getContext().getSubjectId())
                .flushTime(flushTime)
                .endDate(endDate)
                .startDate(startDate)
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize());

        if (CollUtil.isNotEmpty(paramVo.getIncomeType())) {
            builder.incomeType(paramVo.getIncomeType().stream().map(IncomeType::valueOf).collect(Collectors.toList()));
        }

        Result<PageBean<RoomIncomeDetailBean>> result = incomeService.getRoomIncomeDetail(builder.build());
        if (RpcResult.isFail(result)) {
            log.error("roomIncomeDetail,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        PageBean<RoomIncomeDetailBean> target = result.target();
        List<RoomIncomeDetailVo> voList = IncomeConvert.I.roomIncomeDetailBeans2Vos(target.getList());
        return ResultVO.success(PageVO.of(target.getTotal(), voList));
    }

    /**
     * 厅收益-收入账户明细记录-合计
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/incomeDetail/sum")
    public ResultVO<RoomIncomeDetailSumVo> roomIncomeDetailSum(@Validated GetRoomIncomeDetailSumParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Long familyId = userFamilyHandler.getUserFamilyId(ContextUtils.getContext().getSubjectId());
        GetRoomIncomeDetailSumParamBean.GetRoomIncomeDetailSumParamBeanBuilder builder = GetRoomIncomeDetailSumParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(familyId)
                .roomId(ContextUtils.getContext().getSubjectId())
                .endDate(endDate)
                .startDate(startDate);

        if (CollUtil.isNotEmpty(paramVo.getIncomeType())) {
            builder.incomeType(paramVo.getIncomeType().stream().map(IncomeType::valueOf).collect(Collectors.toList()));
        }

        Result<RoomIncomeDetailSumBean> result = incomeService.getRoomIncomeDetailSum(builder.build());
        if (RpcResult.isFail(result)) {
            log.error("roomIncomeDetailSum,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }

        return ResultVO.success(IncomeConvert.I.roomIncomeDetailSumBean2Vo(result.target()));
    }


    /**
     * 厅收益-收入账户明细记录-导出
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/incomeDetail/export")
    public ResultVO<Void> roomIncomeDetailExport(@Validated GetRoomIncomeDetailExportParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Long familyId = userFamilyHandler.getUserFamilyId(ContextUtils.getContext().getSubjectId());
        Long roomId = ContextUtils.getContext().getSubjectId();
        String fileName = fileExportHandler.genFileName("签约厅收入账户明细");
        return fileExportHandler.exportFile(fileName, RoomIncomeDetailExportVo.class, (pageNo, pageSize) -> {
            GetRoomIncomeDetailParamBean.GetRoomIncomeDetailParamBeanBuilder builder = GetRoomIncomeDetailParamBean.builder()
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                    .familyId(familyId)
                    .roomId(roomId)
                    .endDate(endDate)
                    .startDate(startDate);

            if (CollUtil.isNotEmpty(paramVo.getIncomeType())) {
                builder.incomeType(paramVo.getIncomeType().stream().map(IncomeType::valueOf).collect(Collectors.toList()));
            }

            Result<PageBean<RoomIncomeDetailBean>> result = incomeService.getRoomIncomeDetailOut(builder.build());
            if (RpcResult.isFail(result)) {
                log.error("guildIncomeDetailExport fail pageNo={}, pageSize={}, rCode={}, paramVo={}", pageNo
                        , pageSize, result.rCode(), JsonUtil.dumps(paramVo));
                return PageVO.empty();
            }
            int total = result.target().getTotal();
            List<RoomIncomeDetailBean> list = result.target().getList();
            return PageVO.of(total, IncomeConvert.I.roomIncomeDetailBeans2ExportVos(list));
        });

    }

    /**
     * 获取厅关键统计数据
     *
     * @param pageSize    每页条数
     * @param statPeriod  统计周期, day/week/month
     * @param lastMinTime 上一次最小时间
     * @return 结果
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/offlineSum/list")
    public ResultVO<RoomIncomeSumResVO> roomOfflineSumList(Integer pageSize, String statPeriod, Long lastMinTime) {
        try {
            int appId = ContextUtils.getBusinessEvnEnum().getAppId();
            Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
            if (familyId == null) {
                return ResultVO.failure("未签约");
            }
            Long roomId = ContextUtils.getContext().getSubjectId();
            // 构建参数Bean
            RequestRoomIncomeStats request = new RequestRoomIncomeStats();
            request.setNjId(roomId);
            request.setAppId(appId);
            request.setFamilyId(familyId);
            request.setStatPeriod(statPeriod);
            request.setPageSize(pageSize);
            request.setLastMinTime(lastMinTime);

            // 调用远程RPC接口
            Result<ResponseRoomIncomeStats> result = incomeService.queryRoomIncomeStats(request);

            if (RpcResult.isFail(result)) {
                ContextUtils.getContext().addResLog("queryRoomIncomeStats rpc fail. rCode={}", result.rCode());
                return ResultVO.failure("查询失败");
            }
            // 转换数据
            RoomIncomeSumResVO roomIncomeSumResVO = IncomeConvert.I.roomIncomeStatsBeans2Vos(result.target());
            return ResultVO.success(roomIncomeSumResVO);

        } catch (Exception e) {
            log.error("roomOfflineSumList error,", e);
            return ResultVO.failure("系统异常,查询失败");
        }
    }


}
