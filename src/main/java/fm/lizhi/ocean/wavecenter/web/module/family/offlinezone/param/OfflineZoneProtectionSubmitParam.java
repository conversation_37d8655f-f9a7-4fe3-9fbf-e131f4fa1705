package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 跳槽保护-提交协议请求参数
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class OfflineZoneProtectionSubmitParam {

    /**
     * 协议ID（更新时必传）
     */
    private Long id;

    /**
     * 家族ID
     */
    @NotNull(message = "家族ID不能为空")
    private Long familyId;

    /**
     * 厅主ID
     */
    @NotNull(message = "厅主ID不能为空")
    private Long njId;

    /**
     * 主播ID
     */
    @NotNull(message = "主播ID不能为空")
    private Long playerId;

    /**
     * 协议开始时间（时间戳）
     */
    private Long agreementStartTime;

    /**
     * 协议结束时间（时间戳）
     */
    private Long agreementEndTime;

    /**
     * 是否盖章签字
     */
    private Boolean stampSign;

    /**
     * 协议文件列表
     */
    private List<AgreementFileParam> agreementFile;

    /**
     * 协议文件参数
     */
    @Data
    public static class AgreementFileParam {
        /**
         * 文件URL
         */
        private String url;

        /**
         * 文件MD5
         */
        private String md5;

        /**
         * 文件名称
         */
        private String name;
    }
}
