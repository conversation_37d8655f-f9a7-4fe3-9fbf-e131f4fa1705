package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:30
 */
@Data
public class RoomSignRoomVo {

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date date;

    private UserVo recRoomInfo;

    private UserVo sendUserInfo;

    private UserVo recUserInfo;

    private String giftName;

    private String income;

    private Integer charm;

    private String content;

}
