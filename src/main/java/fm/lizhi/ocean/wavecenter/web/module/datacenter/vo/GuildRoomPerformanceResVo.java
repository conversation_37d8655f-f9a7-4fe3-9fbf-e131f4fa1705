package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 12:03
 */
@Data
public class GuildRoomPerformanceResVo {

    private List<RoomPerformanceVo> performances;

    /**
     * 考核周期开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date startDate;

    /**
     * 考核周期结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date endDate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date flushTime;
}
