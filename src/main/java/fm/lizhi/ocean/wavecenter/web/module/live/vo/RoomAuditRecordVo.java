package fm.lizhi.ocean.wavecenter.web.module.live.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.*;

import java.util.Date;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RoomAuditRecordVo {

    /**
     * 签约厅的信息
     *
     */
    private RoomBean roomBean;

    /**
     * 陪玩
     */
    private UserBean player;

    /**
     * 操作类型
     */
    private String op;

    /**
     * 操作理由
     */
    private String reason;

    /**
     * 处罚时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    /**
     * 违规场景
     */
    private String scene;

    /**
     * 公开录音文件 URL
     */
    private String publicContentUrl;
}
