package fm.lizhi.ocean.wavecenter.web.module.live.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/4/20 15:35
 */
@Data
public class LiveSmsPlayerParamVo {

    @Min(value = 1)
    private Integer pageNo = 1;

    @Min(value = 1)
    @Max(value = 50)
    private Integer pageSize = 20;

    /**
     * 排序字段
     */
    @NotBlank(message = "排序字段为空")
    private String orderMetrics;

    /**
     * 排序类型
     */
    @NotBlank(message = "排序类型为空")
    private String orderType;

    @NotBlank(message = "时间类型为空")
    private String dateType;

    /**
     * yyyy-MM-dd
     */
    @NotBlank(message = "开始时间为空")
    private String startDate;

    private String endDate;

    private Integer filterZero = 1;

    private Long roomId;

    /**
     * 主播ID
     */
    private Long playerId;

}
