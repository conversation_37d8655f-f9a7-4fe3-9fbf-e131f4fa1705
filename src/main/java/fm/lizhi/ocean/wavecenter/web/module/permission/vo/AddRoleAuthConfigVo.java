package fm.lizhi.ocean.wavecenter.web.module.permission.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11 17:45
 */
@Data
public class AddRoleAuthConfigVo {

    @NotNull(message = "用户不可为空")
    private Long userId;

    @NotBlank(message = "角色不可为空")
    private String roleCode;

    private Long subjectUserId;

    /**
     * 授权厅列表
     */
    private List<Long> authRoomIds;

}
