package fm.lizhi.ocean.wavecenter.web.module.grow.family.convert;

import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigAwardBean;
import fm.lizhi.ocean.wavecenter.web.module.grow.family.vo.FamilyLevelConfigVO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/20 16:56
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface FamilyLevelConvert {

    FamilyLevelConvert I = Mappers.getMapper(FamilyLevelConvert.class);

    @Mappings({
            @Mapping(source = "minExp", target = "minIncome"),
    })
    FamilyLevelConfigVO configAwardBean2VO(FamilyLevelConfigAwardBean bean);

    List<FamilyLevelConfigVO> configAwardBeans2VOs(List<FamilyLevelConfigAwardBean> beans);

}
