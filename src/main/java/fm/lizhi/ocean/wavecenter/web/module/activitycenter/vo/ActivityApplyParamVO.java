package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import lombok.Data;

import java.util.List;

/**
 * 活动申请bean
 */
@Data
public class ActivityApplyParamVO {

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 业务定义不超过10个字，稍微冗余
     */
    private String name;

    /**
     * 提报厅厅主ID
     */
    private Long njId;

    /**
     * 活动分类ID
     */
    private Long classId;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 申请者uid
     */
    private Long applicantUid;

    /**
     * 联系方式
     */
    private String contactNumber;

    /**
     * 主持人ID
     */
    private Long hostId;

    /**
     * 陪档主播ID列表
     */
    private List<Long> accompanyNjIds;

    /**
     * 活动目标，不超过100字
     */
    private String goal;

    /**
     * 活动介绍，不超过100字
     */
    private String introduction;

    /**
     * 活动海报图片地址
     */
    private String posterUrl;

    /**
     * 活动道具图片地址，多个逗号分隔
     */
    private List<String> auxiliaryPropUrl;

    /**
     * 玩法工具，1:魔法团战，2：跨房PK，3：投票，4：全麦PK
     */
    private List<Integer> activityTool;

    /**
     * 房间公告，不超过500字
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片，不超过3个
     */
    private List<String> roomAnnouncementImgUrl;

    /**
     * 房间背景ID列表
     */
    private List<Long> roomBackgroundIds;

    /**
     * 头像框ID列表
     */
    private List<Long> avatarWidgetIds;

    /**
     * 流量资源列表
     */
    private List<ActivityFlowResourceVO> flowResources;

    /**
     * 活动环节列表
     */
    private List<ActivityProcessVO> processList;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 活动类型
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyModelEnum
     */
    private Integer model;

    /**
     * 房间角标ID
     */
    private Long roomMarkId;

    /**
     * 房间角标URL
     */
    private String roomMarkUrl;

    /**
     * 礼物ID列表
     */
    private List<Long> giftIds;
}
