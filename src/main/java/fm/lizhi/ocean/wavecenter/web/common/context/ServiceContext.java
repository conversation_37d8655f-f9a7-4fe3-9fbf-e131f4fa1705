package fm.lizhi.ocean.wavecenter.web.common.context;

import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionResourceType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/16
 */
@Data
@Slf4j
public class ServiceContext {

    private static final Logger logger = LoggerFactory.getLogger("info");
    private static final Pattern logPattern = Pattern.compile("\\{\\}");

    private final StringBuffer infoLogReq = new StringBuffer();
    private final StringBuffer infoLogRes = new StringBuffer();

    private long start;
    private Header header;
    private String uri;
    private String url;
    private String args;
    private final String bodyString = "";

    private int rCode;
    private long userId;
    private String token;
    private BusinessEvnEnum businessEvnEnum;

    private String roleCode;
    /**
     * 如果是家族长请求，该字段为家族ID
     * 如果是家族管理请求，该字段为家族ID
     * 如果是厅主请求，该字段为厅主ID，厅主用户ID
     * 如果是陪玩，该字段为陪玩用户ID
     */
    private Long subjectId;
    /**
     * 登录方式 1=本人 2=授权
     */
    private Integer loginType;

    private Map<PermissionResourceType, Object> resourceMap;

    public ServiceContext(HttpServletRequest request, String args) {
        this.start = System.currentTimeMillis();
        this.header = Header.build(request);
        this.uri = request.getRequestURI();
        this.url = request.getRequestURL().toString();
        this.args = args;
        this.businessEvnEnum = BusinessEvnEnum.from(header.getAppId());
        this.resourceMap = new HashMap<>();
    }

    public ServiceContext(BusinessEvnEnum businessEvnEnum) {
        this.businessEvnEnum = businessEvnEnum;
        if (this.header == null || this.header.getAppId() == null) {
            Header h = new Header();
            h.setAppId(businessEvnEnum.getAppId());
            this.header = h;
        }
    }

    public void putRoomResource(List<Long> roomIds){
        this.resourceMap.put(PermissionResourceType.ROOM, roomIds);
    }

    public List<Long> getRoomResource(){
        return (List<Long>) resourceMap.get(PermissionResourceType.ROOM);
    }

    public BusinessEvnEnum getBusinessEvnEnum() {
        return businessEvnEnum;
    }

    public boolean isFamily(){
        return RoleEnum.FAMILY.getRoleCode().equals(roleCode);
    }
    public boolean isRoom(){
        return RoleEnum.ROOM.getRoleCode().equals(roleCode);
    }
    public boolean isPlayer(){
        return RoleEnum.PLAYER.getRoleCode().equals(roleCode);
    }

    public boolean isUser(){
        return RoleEnum.USER.getRoleCode().equals(roleCode);
    }

    public boolean isFamilyAdmin(){
        return RoleEnum.FAMILY_ADMIN.getRoleCode().equals(roleCode);
    }

    /**
     * 是否本人登录
     * @return
     */
    public boolean isSelfLogin(){
        return loginType == null || loginType == 1;
    }

    /**
     * 以params参数中的值替换s参数中的{}
     */
    protected static String parse(String s, Object... params) {
        if (params == null || params.length == 0) {
            return s;
        }
        Matcher matcher = logPattern.matcher(s);
        StringBuilder sb = new StringBuilder();
        int i = 0;
        int pos1 = 0;
        while (matcher.find()) {
            int pos2 = matcher.start();
            if (pos2 > pos1) {
                sb.append(s, pos1, pos2);
            }
            if (params.length > i) {
                sb.append(params[i++]);
            }
            pos1 = matcher.end();
        }
        return sb.toString();
    }

    /**
     * Add request log.
     */
    public void addReqLog(String msg, Object... params) {
        if (infoLogReq.length() > 0) {
            infoLogReq.append("`");
        }
        infoLogReq.append(parse(msg, params));
    }

    /**
     * Add response log.
     */
    public void addResLog(String msg, Object... params) {
        if (infoLogRes.length() > 0) {
            infoLogRes.append("`");
        }
        infoLogRes.append(parse(msg, params));
    }

    public void log() {
        logger.info("req`userId={}`url={}`uri={}`ip={}`appId={}`deviceId={}`deviceType={}`clientVersion={}`accessToken={}`args={}`{}",
                userId, url, uri, header.getIp(), header.getAppId(), header.getDeviceId(), header.getDeviceType(), header.getClientVersion(), header.getAccessToken(), args, infoLogReq);
        logger.info("res`userId={}`url={}`uri={}`ip={}`appId={}`deviceId={}`deviceType={}`clientVersion={}`roleCode={}`subjectId={}`loginType={}`accessToken={}`args={}`t={}`rCode={}`{}",
                userId, url, uri, header.getIp(), header.getAppId(), header.getDeviceId(), header.getDeviceType(), header.getClientVersion(), roleCode, subjectId, loginType, header.getAccessToken(), args,
                (System.currentTimeMillis() - start), rCode, infoLogRes);
    }



}
