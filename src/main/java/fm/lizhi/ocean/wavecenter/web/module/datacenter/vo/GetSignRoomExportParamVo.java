package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 15:36
 */
@Data
public class GetSignRoomExportParamVo extends GetSignRoomParamVo{

    /**
     *  指标列表
     */
    private List<String> metrics;

}
