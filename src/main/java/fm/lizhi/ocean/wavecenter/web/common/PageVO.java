package fm.lizhi.ocean.wavecenter.web.common;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10 11:41
 */
@Data
public class PageVO<T> {

    private int total = 0;

    private List<T> list;

    private Long flushTime = 0L;

    public PageVO() {
    }

    public PageVO(int total, List<T> list) {
        this.total = total;
        this.list = list;
    }

    public PageVO(int total, List<T> list, Long flushTime) {
        this.total = total;
        this.list = list;
        this.flushTime = flushTime;
    }

    public static <T> PageVO<T> of(int total, List<T> list) {
        return new PageVO<>(total, list);
    }

    public static <T> PageVO<T> of(int total, List<T> list, Long flushTime) {
        return new PageVO<>(total, list, flushTime);
    }

    public static <T> PageVO<T> empty() {
        return new PageVO<>(0, Collections.emptyList());
    }

}
