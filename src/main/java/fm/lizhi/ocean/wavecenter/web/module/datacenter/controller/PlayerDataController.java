package fm.lizhi.ocean.wavecenter.web.module.datacenter.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IndicatorBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IndicatorTrendResBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerGetKeyIndicatorsParamBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.PlayerDataService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.convert.DataCenterConvert;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.IndicatorTrendResVo;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.PlayerAssessmentInfoVo;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.PlayerGetKeyIndicatorsParamVo;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.PlayerGetKeyIndicatorsResVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/21 20:08
 */
@Slf4j
@RestController
@RequestMapping("player/data")
public class PlayerDataController {

    @Autowired
    private PlayerDataService playerDataService;
    @Autowired
    private UserCommonService userCommonService;
    @Autowired
    private DataScopeHandler dataScopeHandler;

    /**
     * 考核周期业绩
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.PLAYER, RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("assessmentInfo")
    public ResultVO<PlayerAssessmentInfoVo> assessmentInfo(@RequestParam(value = "playerId", required = false) Long playerId){
        if (!dataScopeHandler.checkParamPlayerId(playerId)) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Long familyId = dataScopeHandler.getFamilyForBase();
        Long roomId = dataScopeHandler.getRoomForBase(null, playerId);
        Long queryPlayerId = dataScopeHandler.getPlayerForBaseOrDefault(playerId);
        if (familyId == null) {
            return ResultVO.failure("当前用户无签约信息");
        }
        if(queryPlayerId == null){
            return ResultVO.failure("请选择主播");
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        //查询主播信息，包括签约状态
        Result<PlayerSignBean> signRes = userCommonService.getPlayerSignInfo(appId, familyId, roomId, queryPlayerId);
        if (RpcResult.isFail(signRes)) {
            if (signRes.rCode() == UserCommonService.USER_NOT_FOUND) {
                ContextUtils.getContext().addResLog("user not found,rpcRCode={}", signRes.rCode());
                return ResultVO.success(new PlayerAssessmentInfoVo());
            }
            return ResultVO.failure();
        }
        PlayerSignBean signBean = signRes.target();

        PlayerAssessmentInfoVo vo = new PlayerAssessmentInfoVo();
        vo.setPlayerInfo(UserCommonConvert.I.playerSignBean2Vo(signBean));
        vo.setFlushTime(System.currentTimeMillis());

        //查询考核业绩 魅力值查询业务&收入查询支付
        Result<PlayerAssessmentInfoBean> assessmentInfoRes = playerDataService.getAssessmentInfo(appId, queryPlayerId, familyId, roomId);
        if (RpcResult.isFail(assessmentInfoRes)) {
            log.error("assessmentInfoRes={}", assessmentInfoRes.rCode());
            return ResultVO.success(vo);
        }

        PlayerAssessmentInfoBean roomAssessmentInfoBean = assessmentInfoRes.target();
        vo = DataCenterConvert.I.playerAssessmentInfoBean2Vo(roomAssessmentInfoBean);
        vo.setPlayerInfo(UserCommonConvert.I.playerSignBean2Vo(signBean));
        vo.setFlushTime(System.currentTimeMillis());
        return ResultVO.success(vo);
    }

    /**
     * 趋势图
     * @param metric
     * @param playerId
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.PLAYER, RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("indicatorTrend")
    public ResultVO<IndicatorTrendResVo> indicatorTrend(@RequestParam("metric")String metric
            , @RequestParam(value = "playerId", required = false)Long playerId){
        if (!dataScopeHandler.checkParamPlayerId(playerId)) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        //家族长访问
        //查看改陪玩在该公会下所有签约厅的数据 条件=playerId&familyId
        //厅主访问
        //查看陪玩在该厅下的所有数据 条件=roomId&playerId
        //陪玩访问
        //查看当前已签约的厅的数据 条件 签约roomId&playerId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        Long roomId = dataScopeHandler.getRoomForFamilyOrRoom(null, playerId);
        Long queryPlayerId = dataScopeHandler.getPlayerForBaseOrDefault(playerId);
        if (queryPlayerId == null) {
            return ResultVO.failure("请选择主播");
        }

        Result<IndicatorTrendResBean> result = playerDataService.getIndicatorTrend(appId, familyId, roomId, queryPlayerId, metric);
        if (RpcResult.isFail(result)) {
            log.error("indicatorTrend,getIndicatorTrend,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(DataCenterConvert.I.indicatorTrendResBean2Vo(result.target()));
    }

    /**
     * 查询关键指标数据
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.PLAYER, RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("keyIndicators")
    public ResultVO<PlayerGetKeyIndicatorsResVo> keyIndicators(@Validated PlayerGetKeyIndicatorsParamVo paramVo){
        //参数校验
        if (CollectionUtils.isEmpty(paramVo.getRatioMetrics()) && CollectionUtils.isEmpty(paramVo.getValueMetrics())) {
            return ResultVO.success(new PlayerGetKeyIndicatorsResVo());
        }

        if (!dataScopeHandler.checkParamPlayerId(paramVo.getPlayerId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Long familyId = dataScopeHandler.getFamilyForFamily();
        Long roomId = dataScopeHandler.getRoomForFamilyOrRoom(null, paramVo.getPlayerId());
        Long queryPlayerId = dataScopeHandler.getPlayerForBaseOrDefault(paramVo.getPlayerId());
        if (queryPlayerId == null) {
            return ResultVO.failure("请选择主播");
        }

        Result<List<IndicatorBean>> result = playerDataService.getKeyIndicators(PlayerGetKeyIndicatorsParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .playerId(queryPlayerId)
                .familyId(familyId)
                .roomId(roomId)
                .ratioMetrics(paramVo.getRatioMetrics())
                .valueMetrics(paramVo.getValueMetrics())
                .startDate(paramVo.getStartDate())
                .endDate(paramVo.getEndDate())
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .build());
        if (RpcResult.isFail(result)) {
            log.error("playerKeyIndicators,error,rCode={}", result.rCode());
            return ResultVO.failure();
        }
        List<IndicatorBean> indicatorBeanList = result.target();
        return ResultVO.success(new PlayerGetKeyIndicatorsResVo()
                .setIndicators(DataCenterConvert.I.indicatorBeans2Vos(indicatorBeanList)));
    }

}
