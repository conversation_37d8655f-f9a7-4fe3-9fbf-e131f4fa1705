package fm.lizhi.ocean.wavecenter.web.module.live.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsPlayerParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsRoomParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.PlayerSmsStatBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomSmsStatBean;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveSmsService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.live.convert.LiveSmsConvert;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * 私信分析
 * <AUTHOR>
 * @date 2024/4/20 15:32
 */
@Slf4j
@RestController
@RequestMapping("live/sms")
public class LiveSmsController {


    @Autowired
    private LiveSmsService liveSmsService;
    @Autowired
    private DataScopeHandler dataScopeHandler;
    @Autowired
    private FileExportHandler fileExportHandler;

    /**
     * 主播数据
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.PLAYER, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("playerList")
    public ResultVO<PageVO<PlayerSmsStatVo>> playerList(@Validated LiveSmsPlayerParamVo paramVo){
        Long queryRoomId = dataScopeHandler.getRoomForBase(paramVo.getRoomId());
        Long queryFamilyId = dataScopeHandler.getFamilyForBase();
        Long queryPlayerId  = dataScopeHandler.getPlayerForBaseOrDefault(paramVo.getPlayerId());

        long userId = ContextUtils.getContext().getUserId();
        if (queryRoomId == null) {
            return ResultVO.failure("请选择厅");
        }

        LiveSmsPlayerParamBean.LiveSmsPlayerParamBeanBuilder builder = LiveSmsPlayerParamBean.builder()
                .roomId(queryRoomId)
                .familyId(queryFamilyId)
                .isPlayer(ContextUtils.getContext().isPlayer())
                .playerId(queryPlayerId)
                .userId(userId)
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .orderMetrics(paramVo.getOrderMetrics())
                .orderType(OrderType.fromValue(paramVo.getOrderType()))
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .filterZero(paramVo.getFilterZero().equals(1))
                .startDate(DateUtil.parse(paramVo.getStartDate(), DatePattern.NORM_DATE_PATTERN));
        if (StringUtils.isNotBlank(paramVo.getEndDate())) {
            builder.endDate(DateUtil.parse(paramVo.getEndDate(), DatePattern.NORM_DATE_PATTERN));
        }

        Result<PageBean<PlayerSmsStatBean>> result = liveSmsService.playerList(builder.build());
        if (RpcResult.isFail(result)) {
            log.error("playerList,error,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        PageBean<PlayerSmsStatBean> target = result.target();
        List<PlayerSmsStatVo> voList = LiveSmsConvert.I.playerSmsStatBeans2Vos(target.getList());
        return ResultVO.success(PageVO.of(target.getTotal(), voList));
    }

    /**
     * 厅列表
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN ,RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("roomList")
    public ResultVO<PageVO<RoomSmsStatVo>> roomList(@Validated LiveSmsRoomParamVo paramVo){

        return getRoomSmsStatVoPageVO(paramVo);
    }

    /**
     * 导出厅私信数据
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN ,RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("roomList/export")
    public ResultVO<Void> roomListExport(@Validated LiveSmsRoomExportParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        Long queryFamilyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        if (queryFamilyId == null) {
            return ResultVO.failure("未签约家族");
        }
        Long queryRoomId = dataScopeHandler.getRoomForRoomOrDefault(paramVo.getRoomId());

        LiveSmsRoomParamBean.LiveSmsRoomParamBeanBuilder builder = LiveSmsRoomParamBean.builder()
                .roomId(queryRoomId)
                .familyId(queryFamilyId)
                .userId(userId)
                .roomIds(ContextUtils.getContext().getRoomResource())
                .orderMetrics(paramVo.getOrderMetrics())
                .orderType(OrderType.fromValue(paramVo.getOrderType()))
                .appId(appId)
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .filterZero(paramVo.getFilterZero().equals(1))
                .startDate(DateUtil.parse(paramVo.getStartDate(), DatePattern.NORM_DATE_PATTERN));
        if (StringUtils.isNotBlank(paramVo.getEndDate())) {
            builder.endDate(DateUtil.parse(paramVo.getEndDate(), DatePattern.NORM_DATE_PATTERN));
        }

        HashSet<String> includeFields = null;
        if (CollUtil.isNotEmpty(paramVo.getMetrics())) {
            includeFields = new HashSet<>(paramVo.getMetrics());
            includeFields.add(LiveSmsRoomExportVo.Fields.roomName);
            includeFields.add(LiveSmsRoomExportVo.Fields.njId);
        }

        String fileName = "私信拓客数据_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, LiveSmsRoomExportVo.class,
                (pageNo, pageSize) -> {
                    builder.pageNo(pageNo);
                    builder.pageSize(pageSize);
                    Result<PageBean<RoomSmsStatBean>> result = liveSmsService.roomList(builder.build());
                    if (RpcResult.isFail(result)) {
                        log.error("roomListExport,error,rpcRCode={}", result.rCode());
                        return PageVO.empty();
                    }
                    PageBean<RoomSmsStatBean> target = result.target();
                    List<LiveSmsRoomExportVo> exportVos = LiveSmsConvert.I.roomSmsStatBeans2ExportVos(target.getList());
                    return PageVO.of(target.getTotal(), exportVos);
                }, includeFields);
    }


    /**
     * 导出主播私信数据
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.PLAYER, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("playerList/export")
    public ResultVO<Void> playerListExport(@Validated LiveSmsPlayerExportParamVo paramVo) {
        Long queryRoomId = dataScopeHandler.getRoomForBase(paramVo.getRoomId());
        Long queryFamilyId = dataScopeHandler.getFamilyForBase();
        Long queryPlayerId  = dataScopeHandler.getPlayerForBaseOrDefault(paramVo.getPlayerId());

        long userId = ContextUtils.getContext().getUserId();
        if (queryRoomId == null) {
            return ResultVO.failure("请选择厅");
        }

        LiveSmsPlayerParamBean.LiveSmsPlayerParamBeanBuilder builder = LiveSmsPlayerParamBean.builder()
                .roomId(queryRoomId)
                .familyId(queryFamilyId)
                .isPlayer(ContextUtils.getContext().isPlayer())
                .playerId(queryPlayerId)
                .userId(userId)
                .orderMetrics(paramVo.getOrderMetrics())
                .orderType(OrderType.fromValue(paramVo.getOrderType()))
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .filterZero(paramVo.getFilterZero().equals(1))
                .startDate(DateUtil.parse(paramVo.getStartDate(), DatePattern.NORM_DATE_PATTERN));
        if (StringUtils.isNotBlank(paramVo.getEndDate())) {
            builder.endDate(DateUtil.parse(paramVo.getEndDate(), DatePattern.NORM_DATE_PATTERN));
        }

        HashSet<String> includeFields = null;
        if (CollUtil.isNotEmpty(paramVo.getMetrics())) {
            includeFields = new HashSet<>(paramVo.getMetrics());
            includeFields.add(LiveSmsPlayerExportVo.Fields.roomName);
            includeFields.add(LiveSmsPlayerExportVo.Fields.njId);
            includeFields.add(LiveSmsPlayerExportVo.Fields.playerName);
            includeFields.add(LiveSmsPlayerExportVo.Fields.playerBand);
        }

        String fileName = "私信拓客数据_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, LiveSmsPlayerExportVo.class,
                (pageNo, pageSize) -> {
                    builder.pageNo(pageNo);
                    builder.pageSize(pageSize);
                    Result<PageBean<PlayerSmsStatBean>> result = liveSmsService.playerList(builder.build());
                    if (RpcResult.isFail(result)) {
                        log.error("playerListExport,playerList fail. rCode={}", result.rCode());
                        return PageVO.empty();
                    }
                    PageBean<PlayerSmsStatBean> target = result.target();
                    List<LiveSmsPlayerExportVo> exportVos = LiveSmsConvert.I.playerSmsStatBeans2ExportVos(target.getList());
                    return PageVO.of(target.getTotal(), exportVos);
                }, includeFields);
    }


    private ResultVO<PageVO<RoomSmsStatVo>> getRoomSmsStatVoPageVO(@Validated LiveSmsRoomParamVo paramVo){
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        Long queryFamilyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        if (queryFamilyId == null) {
            return ResultVO.failure("未签约家族");
        }
        Long queryRoomId = dataScopeHandler.getRoomForRoomOrDefault(paramVo.getRoomId());

        //调用dc查询
        LiveSmsRoomParamBean.LiveSmsRoomParamBeanBuilder build = LiveSmsRoomParamBean.builder()
                .roomId(queryRoomId)
                .familyId(queryFamilyId)
                .userId(userId)
                .roomIds(ContextUtils.getContext().getRoomResource())
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .orderMetrics(paramVo.getOrderMetrics())
                .orderType(OrderType.fromValue(paramVo.getOrderType()))
                .appId(appId)
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .filterZero(paramVo.getFilterZero().equals(1))
                .startDate(DateUtil.parse(paramVo.getStartDate(), DatePattern.NORM_DATE_PATTERN));
        if (StringUtils.isNotBlank(paramVo.getEndDate())) {
            build.endDate(DateUtil.parse(paramVo.getEndDate(), DatePattern.NORM_DATE_PATTERN));
        }

        Result<PageBean<RoomSmsStatBean>> result = liveSmsService.roomList(build.build());
        if (RpcResult.isFail(result)) {
            log.error("getRoomSmsStatVoPageVO,error,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        PageBean<RoomSmsStatBean> target = result.target();
        List<RoomSmsStatVo> voList = LiveSmsConvert.I.roomSmsStatBeans2Vos(target.getList());
        return ResultVO.success(PageVO.of(target.getTotal(), voList));
    }
}
