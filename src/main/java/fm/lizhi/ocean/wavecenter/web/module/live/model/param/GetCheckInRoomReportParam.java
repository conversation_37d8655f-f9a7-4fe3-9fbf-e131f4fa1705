package fm.lizhi.ocean.wavecenter.web.module.live.model.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 打卡报告请求
 */
@Data
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetCheckInRoomReportParam {

    /**
     * 业务id
     */
    @NotNull
    private Integer appId;

    /**
     * 厅主id
     */
    @NotNull
    private Long njId;

    /**
     * 统计的时间类型
     */
    @NotNull
    private CheckInDateTypeEnum dateType = CheckInDateTypeEnum.DAY;

    /**
     * 开始时间毫秒时间戳, 包含
     */
    @NotNull
    private Long startDate;

    /**
     * 结束时间毫秒时间戳, 包含
     */
    @NotNull
    private Long endDate;

    @NotBlank
    private String signCode;
}
