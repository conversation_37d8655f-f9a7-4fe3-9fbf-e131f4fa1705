package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @date 2024/5/21 18:12
 */
@Data
public class GMCSignPlayerPageListReqVo extends SignDateParamVo{

    /**
     * 签约厅ID
     */
    private Long roomId;

    /**
     * 主播ID
     */
    private Long playerId;

    /**
     * 签约状态
     * 1=已签约
     * 0=已解约
     */
    private Integer signStatus;

    /**
     * 结算比例-最小值
     */
    private Integer settleMin;

    /**
     * 结算比例-最大值
     */
    private Integer settleMax;

    @Min(value = 1)
    private Integer pageNo = 1;

    @Min(value = 1)
    @Max(value = 100)
    private Integer pageSize = 10;

}
