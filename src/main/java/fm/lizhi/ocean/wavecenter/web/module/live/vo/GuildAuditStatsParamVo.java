package fm.lizhi.ocean.wavecenter.web.module.live.vo;


import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
public class GuildAuditStatsParamVo {


    /**
     * 厅主ID
     */
    private Long roomId;


    /**
     * 开始时间
     * yyyy-MM-dd
     */
    @NotBlank(message = "开始时间为空")
    private String startDate;

    @NotBlank(message = "结束时间为空")
    private String endDate;

    @Min(value = 1)
    private Integer pageNo;

    @Min(value = 1)
    @Max(value = 50)
    private Integer pageSize;

    private String orderMetrics = "pushPeopleNumber";

    private String orderType = "desc";

}
