package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22 21:36
 */
@Data
public class GetSignPlayerExportParamVo extends GetSignPlayerParamVo{

    /**
     *  指标列表
     */
    private List<String> metrics;

}
