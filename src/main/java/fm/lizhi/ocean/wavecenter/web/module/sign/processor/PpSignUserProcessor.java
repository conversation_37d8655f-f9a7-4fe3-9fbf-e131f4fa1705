package fm.lizhi.ocean.wavecenter.web.module.sign.processor;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.ApplySignParamVo;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/23 14:33
 */
@Component
public class PpSignUserProcessor implements ISignUserProcessor{
    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public ResultVO<Void> applyAdminCheck(ApplySignParamVo param) {
        return ResultVO.failure("暂不支持该功能");
    }
}
