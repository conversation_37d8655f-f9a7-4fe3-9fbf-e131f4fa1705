package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/5 15:39
 */
@Data
public class MyFamilyVo {

    /**
     * 家族 ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 合同ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

    /**
     * 签约ID
     */
    private String signId;

    /**
     * 家族长信息
     */
    private UserVo familyUser;

    /**
     * 家族名称
     */
    private String familyName;

    /**
     * 家族简介
     */
    private String familyIntro;

    /**
     * 家族头像
     */
    private String familyIconUrl;

    /**
     * 结算方式
     */
    private String settleType;

    /**
     * 分成比例
     */
    private String settlePercentage;

    /**
     * 有效期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signDealLineTime;

    /**
     * 签约时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signStartTime;

    /**
     * 签约到期时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signExpireTime;

    /**
     * 发起时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;


}
