package fm.lizhi.ocean.wavecenter.web;

import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 微服务应用启动类
 *
 * <AUTHOR> 由脚手架生成
 */
@Slf4j
@SpringBootApplication
public class MicroservicesApplication {

    // ⚠️⚠️⚠️本地DEBUG启动，请记得先配置启动参数，见READNE.MD
    public static void main(String[] args) {
        String region = ConfigUtils.getRegion();
        String businessEnv = ConfigUtils.getBusinessEnv();
        Env env = ConfigUtils.getEnv();
        log.info("MicroservicesApplication region: {}, businessEnv: {}, env: {}", region, businessEnv, env);
        SpringApplication.run(MicroservicesApplication.class, args);
    }

}
