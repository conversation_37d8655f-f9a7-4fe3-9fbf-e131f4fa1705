package fm.lizhi.ocean.wavecenter.web.module.grow.ability.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPlayerRank;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPlayerRank;
import fm.lizhi.ocean.wavecenter.api.grow.ability.service.AbilityPerformanceService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.convert.GrowAbilityConvert;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.param.GetPlayerPerformanceParam;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.param.GetRoomPerformanceParam;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.param.GetRoomPlayerRankParam;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.vo.GetPlayerPerformanceResult;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.vo.GetRoomPerformanceResult;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.vo.GetRoomPlayerRankResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grow/ability")
@Slf4j
public class GrowAbilityController {

    @Autowired
    private AbilityPerformanceService abilityPerformanceService;

    @Autowired
    private GrowAbilityConvert growAbilityConvert;

    /**
     * 查询厅能力表现
     *
     * @param param 请求参数
     * @return 能力表现结果
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/week/roomPerformance")
    public ResultVO<GetRoomPerformanceResult> getRoomPerformance(@Validated GetRoomPerformanceParam param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId = ContextUtils.getContext().getSubjectId();
        RequestGetRoomPerformance request = growAbilityConvert.toRequestGetRoomPerformance(param, appId, roomId);
        Result<ResponseGetRoomPerformance> result = abilityPerformanceService.getRoomPerformance(request);
        int rCode = result.rCode();
        if (rCode == CommonService.PARAM_ERROR) {
            log.info("getRoomPerformance param invalid, request={}, message={}", request, result.getMessage());
            String message = StringUtils.defaultIfBlank(result.getMessage(), "参数错误");
            return ResultVO.failure(message);
        }
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getRoomPerformance error, request={}, rCode={}, message={}", request, rCode, result.getMessage());
            String message = StringUtils.defaultIfBlank(result.getMessage(), "查询厅能力表现失败");
            return ResultVO.failure(message);
        }
        ResponseGetRoomPerformance response = result.target();
        log.debug("getRoomPerformance success, request={}, response={}", request, response);
        return ResultVO.success(growAbilityConvert.toGetRoomPerformanceResult(response));
    }

    /**
     * 查询厅主播排名
     *
     * @param param 请求参数
     * @return 主播排名结果
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/week/roomPlayerRank")
    public ResultVO<GetRoomPlayerRankResult> getRoomPlayerRank(@Validated GetRoomPlayerRankParam param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId = ContextUtils.getContext().getSubjectId();
        RequestGetRoomPlayerRank request = growAbilityConvert.toRequestGetRoomPlayerRank(param, appId, roomId);
        Result<ResponseGetRoomPlayerRank> result = abilityPerformanceService.getRoomPlayerRank(request);
        int rCode = result.rCode();
        if (rCode == CommonService.PARAM_ERROR) {
            log.info("getRoomPlayerRank param invalid, request={}, message={}", request, result.getMessage());
            String message = StringUtils.defaultIfBlank(result.getMessage(), "参数错误");
            return ResultVO.failure(message);
        }
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getRoomPlayerRank error, request={}, rCode={}, message={}", request, rCode, result.getMessage());
            String message = StringUtils.defaultIfBlank(result.getMessage(), "查询厅主播排名失败");
            return ResultVO.failure(message);
        }
        ResponseGetRoomPlayerRank response = result.target();
        log.debug("getRoomPlayerRank success, request={}, response={}", request, response);
        return ResultVO.success(growAbilityConvert.toGetRoomPlayerRankResult(response));
    }

    /**
     * 查询主播能力表现
     *
     * @param param 请求参数
     * @return 主播能力表现结果
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/week/playerPerformance")
    public ResultVO<GetPlayerPerformanceResult> getPlayerPerformance(@Validated GetPlayerPerformanceParam param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId = ContextUtils.getContext().getSubjectId();
        RequestGetPlayerPerformance request = growAbilityConvert.toRequestGetPlayerPerformance(param, appId, roomId);
        Result<ResponseGetPlayerPerformance> result = abilityPerformanceService.getPlayerPerformance(request);
        int rCode = result.rCode();
        if (rCode == CommonService.PARAM_ERROR) {
            log.info("getPlayerPerformance param invalid, request={}, message={}", request, result.getMessage());
            String message = StringUtils.defaultIfBlank(result.getMessage(), "参数错误");
            return ResultVO.failure(message);
        }
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getPlayerPerformance error, request={}, rCode={}, message={}", request, rCode, result.getMessage());
            String message = StringUtils.defaultIfBlank(result.getMessage(), "查询主播能力表现失败");
            return ResultVO.failure(message);
        }
        ResponseGetPlayerPerformance response = result.target();
        log.debug("getPlayerPerformance success, request={}, response={}", request, response);
        return ResultVO.success(growAbilityConvert.toGetPlayerPerformanceResult(response));
    }
}
