package fm.lizhi.ocean.wavecenter.web.module.live.model.result;

import fm.lizhi.ocean.wavecenter.web.module.live.vo.checkin.WaveCheckInAllMicGiftConfigVo;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.checkin.WaveCheckInBaseConfigVo;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.checkin.WaveCheckInDayMicConfigVo;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.checkin.WaveCheckInLightGiftConfigVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.List;

/**
 * 获取麦序福利配置
 *
 * <AUTHOR>
 */
@Data
public class GetCheckInConfigResult {

    /**
     * 基础配置.
     */
    private WaveCheckInBaseConfigVo baseConfig;

    /**
     * 收光奖励配置.
     */
    private WaveCheckInLightGiftConfigVo lightGiftConfig;

    /**
     * 全麦配置.
     */
    private WaveCheckInAllMicGiftConfigVo allMicGiftConfig;

    /**
     * 日麦序配置.
     */
    private WaveCheckInDayMicConfigVo dayMicConfig;

    /**
     * 管理员列表配置
     */
    private List<UserVo> checkInManagerConfig;
}
