package fm.lizhi.ocean.wavecenter.web.module.sign.controller;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.PlayerSignHistoryRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestPlayerApplyCancel;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestPlayerOperateCancel;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestPlayerSignHistory;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestWithdrawCancel;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponsePlayerApplyCancel;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponsePlayerOperateCancel;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseWithdrawCancel;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignPlayerService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.sign.convert.SignConvert;
import fm.lizhi.ocean.wavecenter.web.module.sign.result.PlayerApplyCancelResult;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.PlayerSignHistoryParamVo;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.PlayerSignHistoryRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/5 15:36
 */
@Slf4j
@RestController
@RequestMapping("sign/player")
public class SignPlayerController {

    @Autowired
    private SignPlayerService signPlayerService;

    /**
     * 申请解约
     * @param contractId
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.PLAYER}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("applyCancel/{contractId}")
    public ResultVO<PlayerApplyCancelResult> applyCancel(@PathVariable("contractId") Long contractId){
        RequestPlayerApplyCancel param = RequestPlayerApplyCancel.builder()
                .playerSignId(contractId)
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .build();
        Result<ResponsePlayerApplyCancel> result = signPlayerService.applyCancel(param);
        if (RpcResult.isFail(result)) {
            log.error("applyCancel fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(param));
            return ResultVO.failure(MsgCodes.FAIL);
        }

        ResultVO<Void> voidResultVO = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voidResultVO.isOK()) {
            return ResultVO.failure(voidResultVO.getRCode(), voidResultVO.getPrompt());
        }

        return ResultVO.success(new PlayerApplyCancelResult().setContractId(result.target().getContractId()));
    }

    /**
     * 取消解约
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.PLAYER}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("withdrawCancel/{contractId}")
    public ResultVO<Void> withdrawCancel(@PathVariable("contractId") Long contractId){
        RequestWithdrawCancel param = RequestWithdrawCancel.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .playerSignId(contractId)
                .build();
        Result<ResponseWithdrawCancel> result = signPlayerService.withdrawCancel(param);
        if (RpcResult.isFail(result)) {
            log.error("withdrawCancel fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(param));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        return SignCommonUtil.signCommonCodeCheck(result.target());
    }

    /**
     * 签署解约
     * @param contractId
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.PLAYER}, onlySelfLogin = true)
    @PostMapping("signCancel/{contractId}/{status}")
    public ResultVO<Void> signCancel(@PathVariable("contractId") Long contractId, @PathVariable("status") String status){
        RequestPlayerOperateCancel param = RequestPlayerOperateCancel.builder()
                .playerSignId(contractId)
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .operateType(OperateTypeEnum.getByCode(status))
                .build();
        Result<ResponsePlayerOperateCancel> result = signPlayerService.operateAdminCancel(param);
        if (RpcResult.isFail(result)) {
            log.error("player operateAdminCancel fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(param));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        return SignCommonUtil.signCommonCodeCheck(result.target());
    }

    /**
     * 查询历史签约记录
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.PLAYER}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("signHistoryList")
    public ResultVO<PageVO<PlayerSignHistoryRecordVo>> signHistoryList(@Validated PlayerSignHistoryParamVo param){

        RequestPlayerSignHistory.RequestPlayerSignHistoryBuilder rpcParamBuilder = RequestPlayerSignHistory.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .playerUserId(ContextUtils.getContext().getUserId())
                .pageNo(param.getPageNo())
                .pageSize(param.getPageSize());

        //参数检查
        if (StringUtils.isNotBlank(param.getType())) {
            ContractTypeEnum type = ContractTypeEnum.from(param.getType());
            if (type == null) {
                return ResultVO.failure("不支持该类型");
            }
            rpcParamBuilder.type(type);
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            SignRelationEnum status = SignRelationEnum.fromCode(param.getStatus());
            if (status == SignRelationEnum.UNKNOWN) {
                return ResultVO.failure("不支持该状态");
            }
            rpcParamBuilder.status(status);
        }

        RequestPlayerSignHistory rpcParam = rpcParamBuilder.build();
        Result<PageBean<PlayerSignHistoryRecordBean>> result = signPlayerService.querySignHistory(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("player querySignHistory fail. rCode=={},param={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }

        PageBean<PlayerSignHistoryRecordBean> pageBean = result.target();
        List<PlayerSignHistoryRecordVo> voList = SignConvert.I.playerSignHistoryRecordBeans2Vos(pageBean.getList());
        return ResultVO.success(PageVO.of(pageBean.getTotal(), voList));
    }

}
