package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.List;

@Data
public class RoomHourStatsResVo implements IDetailList<RoomHourDetailVo>{

    /**
     * 厅主ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long roomId;

    private UserVo player;

    /**
     * 合计
     */
    private RoomHourStatsVo stats;

    /**
     * 明细
     */
    private List<RoomHourDetailVo> detail;

    @Override
    public List<RoomHourDetailVo> foundDetail() {
        return this.detail;
    }
}
