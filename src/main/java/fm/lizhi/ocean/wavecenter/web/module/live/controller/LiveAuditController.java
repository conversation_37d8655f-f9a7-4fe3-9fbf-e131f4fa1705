package fm.lizhi.ocean.wavecenter.web.module.live.controller;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.content.review.constant.OperationMarkEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.AuditMetricsEnum;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveAuditService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.util.WcAssert;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.AuditOp;
import fm.lizhi.ocean.wavecenter.web.module.live.convert.LiveAuditConvert;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RestController
@RequestMapping("live/audit")
public class LiveAuditController {


    @Autowired
    private AppConfig appConfig;

    @Autowired
    private LiveAuditService liveAuditService;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private DataScopeHandler dataScopeHandler;

    @Autowired
    private FileExportHandler fileExportHandler;

    /**
     * 工会的签约厅的违规的陪玩名单
     *
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/room/audit/player")
    public ResultVO<PageVO<UserBean>> roomAuditPlayer(@Validated RoomAuditPlayerParamVo paramVo) {

        Long roomId = paramVo.getRoomId();
        Long familyId = null;

        if (ContextUtils.getContext().isFamily()) {
            familyId = ContextUtils.getContext().getSubjectId();
        }
        if (ContextUtils.getContext().isRoom()) {
            roomId = ContextUtils.getContext().getSubjectId();
        }

        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Result<PageBean<UserBean>> result = liveAuditService.signRoomPushPlayer(RoomPushParamBean.builder()
                        .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                        .familyId(familyId)
                        .roomId(roomId)
                        .endTime(endDate)
                        .startTime(startDate).build(),
                paramVo.getPageNo(),
                paramVo.getPageSize()
        );
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomAuditPlayer,signRoomPushPlayer,rCode={}", result.rCode());
            return ResultVO.failure("查询名单失败！");
        }
        PageBean<UserBean> target = result.target();
        return ResultVO.success(PageVO.of(target.getTotal(), target.getList()));
    }


    /**
     * 工会的审核记录的统计
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/guild/record/detail/stats")
    public ResultVO<PageVO<GuildAuditRecordStatsVo>> guildAuditRecordStats(@Validated GuildAuditStatsParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Result<PageBean<GuildAuditRecordStatsBean>> result = liveAuditService.guildAuditRecordStats(GuildAuditStatsParamBean.builder()
                        .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                        .familyId(ContextUtils.getContext().getSubjectId())
                        .roomId(paramVo.getRoomId())
                        .endTime(endDate)
                        .startTime(startDate)
                        .orderType(OrderType.fromValue(paramVo.getOrderType()))
                        .orderMetrics(AuditMetricsEnum.fromValue(paramVo.getOrderMetrics()))
                        .roomIds(ContextUtils.getContext().getRoomResource())
                        .build(),
                paramVo.getPageNo(),
                paramVo.getPageSize()
        );
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("guildAuditRecordStats,error,rCode={}", result.rCode());
            return ResultVO.failure("查询审核统计失败！");
        }
        PageBean<GuildAuditRecordStatsBean> beanPageBeans = result.target();
        List<GuildAuditRecordStatsVo> guildAuditRecordVos = beanPageBeans.getList().stream().map(LiveAuditConvert.I::beanTo2GuildAuditRecordVo).collect(Collectors.toList());
        return ResultVO.success(PageVO.of(beanPageBeans.getTotal(), guildAuditRecordVos));
    }


    /**
     * 厅的审核记录的统计
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/room/record/detail/stats")
    public ResultVO<RoomAuditRecordStatsVo> roomAuditRecordStats(@Validated RoomAuditStatsParamVo paramVo) {

        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Result<RoomAuditRecordStatsBean> result = liveAuditService.roomAuditRecordStats(RoomAuditStatsParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .roomId(ContextUtils.getContext().getSubjectId())
                .endTime(endDate)
                .startTime(startDate)
                .build()
        );
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomAuditRecordStats,error,rCode={}", result.rCode());
            return ResultVO.failure("查询审核统计失败！");
        }
        RoomAuditRecordStatsBean target = result.target();
        RoomAuditRecordStatsVo roomAuditRecordVo = LiveAuditConvert.I.beanTo2RoomAuditRecordStatsBean(target);
        return ResultVO.success(roomAuditRecordVo);
    }


    /**
     * 审核操作类型
     *
     * @return
     */
    @PermissionCheck
    @VerifyUserToken
    @GetMapping("/op")
    public ResultVO<List<AuditOp>> auditOp() {
        List<AuditOp> auditOps = new ArrayList<>();
        String auditOp = appConfig.getAuditOp();
        String[] ops = auditOp.split(",");
        Set<Integer> opSet = Stream.of(ops).map(Integer::parseInt).collect(Collectors.toSet());
        for (OperationMarkEnum value : OperationMarkEnum.values()) {
            if (opSet.contains(value.getOp())) {
                auditOps.add(
                        AuditOp.builder()
                                .value(value.getOp())
                                .label(value.getDescription())
                                .build()
                );
            }
        }
        return ResultVO.success(auditOps);
    }


    /**
     * 工会的审核记录的详情
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/guild/record/detail")
    public ResultVO<PageVO<GuildAuditRecordVo>> guildAuditRecord(@Validated GuildAuditRecordSearchParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }
        if (!dataScopeHandler.checkParamPlayerBand(paramVo.getBand())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Long userId = null;
        if (StringUtils.isNoneEmpty(paramVo.getBand()) &&
                (userId = userHandler.getUserIdByBand(ContextUtils.getBusinessEvnEnum().getAppId(), paramVo.getBand())) == null) {
            return ResultVO.success(PageVO.empty());
        }

        Long familyId = ContextUtils.getContext().getSubjectId();
        GuildAuditRecordSearchParamBean.GuildAuditRecordSearchParamBeanBuilder builder = GuildAuditRecordSearchParamBean.builder()
                .familyId(familyId)
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .startTime(startDate)
                .userId(userId)
                .roomId(paramVo.getRoomId())
                .endTime(endDate)
                .roomIds(ContextUtils.getContext().getRoomResource())
                .op(paramVo.getOp());

        Result<PageBean<GuildAuditRecordBean>> result = liveAuditService.guildAuditRecordDetail(builder.build(),
                paramVo.getPageNo(),
                paramVo.getPageSize()
        );

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("guildAuditRecord,error,rCode={}", result.rCode());
            return ResultVO.success(PageVO.empty());
        }
        PageBean<GuildAuditRecordBean> beanPageBean = result.target();
        List<GuildAuditRecordBean> auditRecordBeans = beanPageBean.getList();
        List<GuildAuditRecordVo> guildAuditRecordVos = auditRecordBeans.stream().map(LiveAuditConvert.I::beanTo2GuildAuditRecordVo).collect(Collectors.toList());

        return ResultVO.success(PageVO.of(beanPageBean.getTotal(), guildAuditRecordVos));
    }


    /**
     * 厅的审核记录的详情
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/room/record/detail")
    public ResultVO<PageVO<RoomAuditRecordVo>> roomAuditRecord(@Validated RoomAuditRecordSearchParamVo paramVo) {


        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Long userId = null;
        if (StringUtils.isNoneEmpty(paramVo.getBand()) &&
                (userId = userHandler.getUserIdByBand(ContextUtils.getBusinessEvnEnum().getAppId(), paramVo.getBand())) == null) {
            return ResultVO.success(PageVO.empty());
        }

        Result<PageBean<RoomAuditRecordBean>> result = liveAuditService.roomAuditRecordDetail(RoomAuditRecordSearchParamBean.builder()
                        .roomId(ContextUtils.getContext().getSubjectId())
                        .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                        .userId(userId)
                        .startTime(startDate)
                        .endTime(endDate)
                        .op(paramVo.getOp()).build(),
                paramVo.getPageNo(),
                paramVo.getPageSize()
        );

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomAuditRecord,error,rCode={}", result.rCode());
            return ResultVO.success(PageVO.empty());
        }
        PageBean<RoomAuditRecordBean> beanPageBean = result.target();
        List<RoomAuditRecordBean> auditRecordBeans = beanPageBean.getList();
        List<RoomAuditRecordVo> roomAuditRecordVos = auditRecordBeans.stream().map(LiveAuditConvert.I::beanTo2RoomAuditRecordBean).collect(Collectors.toList());
        return ResultVO.success(PageVO.of(beanPageBean.getTotal(), roomAuditRecordVos));
    }


    /**
     * 陪玩的审核记录的详情
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.PLAYER})
    @VerifyUserToken
    @GetMapping("/player/record/detail")
    public ResultVO<PageVO<PlayerAuditRecordVo>> playerAuditRecord(@Validated PlayerAuditRecordSearchParamVo paramVo) {

        Result<PageBean<PlayerAuditRecordBean>> result = liveAuditService.playerAuditRecordDetail(PlayerAuditRecordSearchParamBean.builder()
                        .userId(ContextUtils.getContext().getUserId())
                        .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                        .startTime(DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN))
                        .endTime(DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN))
                        .op(paramVo.getOp()).build(),
                paramVo.getPageNo(),
                paramVo.getPageSize()
        );
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("playerAuditRecord,error,rCode={}", result.rCode());
            return ResultVO.success();
        }
        PageBean<PlayerAuditRecordBean> beanPageBean = result.target();
        List<PlayerAuditRecordBean> auditRecordBeans = beanPageBean.getList();
        List<PlayerAuditRecordVo> playerAuditRecordVos = auditRecordBeans.stream().map(LiveAuditConvert.I::beanTo2PlayerAuditRecordVo).collect(Collectors.toList());
        return ResultVO.success(PageVO.of(beanPageBean.getTotal(), playerAuditRecordVos));
    }


    /**
     * 工会的审核记录的详情导出
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/guild/record/detail/export")
    public ResultVO<Void> guildAuditRecordExport(@Validated GuildAuditRecordExportParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }
        if (!dataScopeHandler.checkParamPlayerBand(paramVo.getBand())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Long userId = null;
        if (StringUtils.isNoneEmpty(paramVo.getBand()) &&
                (userId = userHandler.getUserIdByBand(ContextUtils.getBusinessEvnEnum().getAppId(), paramVo.getBand())) == null) {
            return ResultVO.success();
        }

        Long familyId = ContextUtils.getContext().getSubjectId();
        GuildAuditRecordSearchParamBean.GuildAuditRecordSearchParamBeanBuilder builder = GuildAuditRecordSearchParamBean.builder()
                .familyId(familyId)
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .startTime(startDate)
                .userId(userId)
                .roomId(paramVo.getRoomId())
                .endTime(endDate)
                .roomIds(ContextUtils.getContext().getRoomResource())
                .op(paramVo.getOp());
        String fileName = "违规记录_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, GuildAuditRecordExportVo.class,
                (pageNo, pageSize) -> {
                    Result<PageBean<GuildAuditRecordBean>> result = liveAuditService.guildAuditRecordDetail(builder.build(),
                            pageNo,
                            pageSize
                    );
                    if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                        log.error("guildAuditRecordExport,error,rCode={}", result.rCode());
                        return PageVO.empty();
                    }
                    PageBean<GuildAuditRecordBean> beanPageBean = result.target();
                    List<GuildAuditRecordBean> auditRecordBeans = beanPageBean.getList();
                    List<GuildAuditRecordExportVo> exportVos = auditRecordBeans.stream().map(bean -> {
                        GuildAuditRecordVo vo = LiveAuditConvert.I.beanTo2GuildAuditRecordVo(bean);
                        return LiveAuditConvert.I.convertGuildAuditRecordExportVo(vo);
                    }).collect(Collectors.toList());
                    return PageVO.of(beanPageBean.getTotal(), exportVos);
                });
    }

    /**
     * 陪玩的审核记录的详情导出
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.PLAYER})
    @VerifyUserToken
    @GetMapping("/player/record/detail/export")
    public ResultVO<Void> playerAuditRecordExport(@Validated PlayerAuditRecordExportParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        String fileName = "违规记录_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, PlayerAuditRecordExportVo.class,
                (pageNo, pageSize) -> {
                    Result<PageBean<PlayerAuditRecordBean>> result = liveAuditService.playerAuditRecordDetail(PlayerAuditRecordSearchParamBean.builder()
                                    .userId(ContextUtils.getContext().getUserId())
                                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                                    .startTime(startDate)
                                    .endTime(endDate)
                                    .op(paramVo.getOp()).build(),
                            pageNo,
                            pageSize
                    );
                    if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                        log.error("playerAuditRecordExport,error,rCode={}", result.rCode());
                        return PageVO.empty();
                    }
                    PageBean<PlayerAuditRecordBean> beanPageBean = result.target();
                    List<PlayerAuditRecordBean> auditRecordBeans = beanPageBean.getList();
                    List<PlayerAuditRecordExportVo> exportVos = auditRecordBeans.stream().map(bean -> {
                        PlayerAuditRecordVo vo = LiveAuditConvert.I.beanTo2PlayerAuditRecordVo(bean);
                        return LiveAuditConvert.I.convertPlayerAuditRecordExportVo(vo);
                    }).collect(Collectors.toList());
                    return PageVO.of(beanPageBean.getTotal(), exportVos);
                });
    }


    /**
     * 厅的审核记录的详情导出
     *
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/room/record/detail/export")
    public ResultVO<Void> roomAuditRecordExport(@Validated RoomAuditRecordExportParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Long userId = null;
        if (StringUtils.isNoneEmpty(paramVo.getBand()) &&
                (userId = userHandler.getUserIdByBand(ContextUtils.getBusinessEvnEnum().getAppId(), paramVo.getBand())) == null) {
            return ResultVO.success();
        }

        Long finalUserId = userId;
        String fileName = "违规记录_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, RoomAuditRecordExportVo.class,
                (pageNo, pageSize) -> {
                    Result<PageBean<RoomAuditRecordBean>> result = liveAuditService.roomAuditRecordDetail(RoomAuditRecordSearchParamBean.builder()
                                    .roomId(ContextUtils.getContext().getSubjectId())
                                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                                    .userId(finalUserId)
                                    .startTime(startDate)
                                    .endTime(endDate)
                                    .op(paramVo.getOp()).build(),
                            pageNo,
                            pageSize
                    );

                    if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                        log.error("roomAuditRecordExport,error,rCode={}", result.rCode());
                        return PageVO.empty();
                    }
                    PageBean<RoomAuditRecordBean> beanPageBean = result.target();
                    List<RoomAuditRecordBean> auditRecordBeans = beanPageBean.getList();
                    List<RoomAuditRecordExportVo> exportVos = auditRecordBeans.stream().map(bean -> {
                        RoomAuditRecordVo vo = LiveAuditConvert.I.beanTo2RoomAuditRecordBean(bean);
                        return LiveAuditConvert.I.convertRoomAuditRecordExportVo(vo);
                    }).collect(Collectors.toList());
                    return PageVO.of(beanPageBean.getTotal(), exportVos);
                });
    }


}
