package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 离线区域厅数据VO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Accessors(chain = true)
public class OfflineZoneRoomDataVo {

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 周开始日期（时间戳）
     */
    private Long startWeekDate;

    /**
     * 周结束日期（时间戳）
     */
    private Long endWeekDate;

    /**
     * 公会ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 厅主ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long njId;

    /**
     * 厅主信息
     */
    private UserVo njInfo;

    /**
     * 厅主名称
     */
    private String njName;

    /**
     * 厅签约时间（时间戳）
     */
    private Long beginSignTime;

    /**
     * 厅分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 厅收入
     */
    private BigDecimal income;

    /**
     * 线下主播数
     */
    private Integer offlinePlayerCnt;

    /**
     * 线下主播数占比
     */
    private BigDecimal offlinePlayerCntRate;

    /**
     * 受保护主播数
     */
    private Integer protectedPlayerCnt;

    /**
     * 受保护主播数占比
     */
    private BigDecimal protectedPlayerCntRate;

    /**
     * 线下主播收入
     */
    private BigDecimal offlinePlayerIncome;

    /**
     * 线下主播收入占比
     */
    private BigDecimal offlinePlayerIncomeRate;
}
