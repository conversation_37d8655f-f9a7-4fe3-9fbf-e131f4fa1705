package fm.lizhi.ocean.wavecenter.web.module.datacenter.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.datacenter.request.RequestGuildGetAssessmentInfo;
import fm.lizhi.ocean.wavecenter.api.datacenter.request.RequestGuildGetIndicatorTrend;
import fm.lizhi.ocean.wavecenter.api.datacenter.request.RequestGuildRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.GuildDataService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyAuthBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.convert.DataCenterConvert;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公会数据
 * <AUTHOR>
 * @date 2024/4/17 16:45
 */
@RestController
@RequestMapping("guild/data")
@PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
public class GuildDataController {

    private static final Logger log = LoggerFactory.getLogger(GuildDataController.class);
    @Autowired
    private GuildDataService guildDataService;
    @Autowired
    private UserFamilyService userFamilyService;

    /**
     * 查询关键指标数据
     * @return
     */
    @VerifyUserToken
    @GetMapping("keyIndicators")
    public ResultVO<GuildGetKeyIndicatorsResVo> keyIndicators(@Validated GuildGetKeyIndicatorsParamVo paramVo){
        //参数校验
        if (CollectionUtils.isEmpty(paramVo.getRatioMetrics()) && CollectionUtils.isEmpty(paramVo.getValueMetrics())) {
            return ResultVO.success(new GuildGetKeyIndicatorsResVo());
        }

        Long familyId = ContextUtils.getContext().getSubjectId();
        Result<List<IndicatorBean>> result = guildDataService.getKeyIndicators(GuildGetKeyIndicatorsParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(familyId)
                .ratioMetrics(paramVo.getRatioMetrics())
                .valueMetrics(paramVo.getValueMetrics())
                .startDate(paramVo.getStartDate())
                .endDate(paramVo.getEndDate())
                .roomIds(ContextUtils.getContext().getRoomResource())
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .build());
        if (RpcResult.isFail(result)) {
            log.error("guildKeyIndicators,error,rCode={}", result.rCode());
            return ResultVO.failure();
        }
        List<IndicatorBean> indicatorBeanList = result.target();
        return ResultVO.success(new GuildGetKeyIndicatorsResVo()
                .setIndicators(DataCenterConvert.I.indicatorBeans2Vos(indicatorBeanList)));
    }

    /**
     * 关键指标趋势图
     * @param metric
     * @return
     */
    @VerifyUserToken
    @GetMapping("indicatorTrend")
    public ResultVO<IndicatorTrendResVo> indicatorTrend(@RequestParam("metric") String metric){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        //公会ID
        Long familyId = ContextUtils.getContext().getSubjectId();
        List<Long> roomIds = ContextUtils.getContext().getRoomResource();

        RequestGuildGetIndicatorTrend request = new RequestGuildGetIndicatorTrend();
        request.setAppId(appId);
        request.setFamilyId(familyId);
        request.setRoomIds(roomIds);
        request.setMetric(metric);

        Result<IndicatorTrendResBean> result = guildDataService.getIndicatorTrendV2(request);
        if (RpcResult.isFail(result)) {
            log.error("guildIndicatorTrend,error,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(DataCenterConvert.I.indicatorTrendResBean2Vo(result.target()));
    }

    /**
     * 公会数据
     * @return
     */
    @VerifyUserToken
    @GetMapping("familyInfo")
    public ResultVO<FamilyAuthVo> familyInfo(){
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<FamilyAuthBean> rpcResult = userFamilyService.getUserFamilyAuth(appId, familyId);
        if (RpcResult.isFail(rpcResult)) {
            log.error("familyInfo,getUserFamilyAuth,error,rpcRCode={}", rpcResult.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(DataCenterConvert.I.familyAuthBean2Vo(rpcResult.target()));
    }

    /**
     * 公会本期考核信息
     * @return
     */
    @VerifyUserToken
    @GetMapping("assessmentInfo")
    public ResultVO<GuildAssessmentInfoVo> assessmentInfo(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        //家族ID
        Long subjectId = ContextUtils.getContext().getSubjectId();
        log.info("getAssessmentInfo appId={}, subjectId={}", appId, subjectId);
        RequestGuildGetAssessmentInfo request = new RequestGuildGetAssessmentInfo();
        request.setAppId(appId);
        request.setFamilyId(subjectId);
        request.setRoomIds(ContextUtils.getContext().getRoomResource());
        Result<GuildAssessmentInfoBean> result = guildDataService.getAssessmentInfoV2(request);
        if (RpcResult.isFail(result)) {
            log.error("assessmentInfo,getAssessmentInfoV2,rCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(DataCenterConvert.I.guildAssessmentInfoBean2Vo(result.target()));
    }

    /**
     * 签约厅业绩-查询
     * @return
     */
    @VerifyUserToken
    @GetMapping("room/performance")
    public ResultVO<GuildRoomPerformanceResVo> roomPerformance(){
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        log.info("getRoomPerformance appId={}, familyId={}", appId, familyId);
        Result<GuildRoomPerformanceResBean> result = guildDataService.roomPerformanceV2(new RequestGuildRoomPerformance()
                .setAppId(appId)
                .setFamilyId(familyId)
                .setRoomIds(ContextUtils.getContext().getRoomResource())
        );
        if (RpcResult.isFail(result)) {
            log.error("roomPerformance,error,result.rCode={}", result.rCode());
            return ResultVO.failure();
        }
        GuildRoomPerformanceResVo voRes = DataCenterConvert.I.guildRoomPerformanceResBean2Vo(result.target());
        return ResultVO.success(voRes);
    }

}
