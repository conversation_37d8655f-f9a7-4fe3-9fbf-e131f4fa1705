package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class GetResourceTimeParamVO {

    /**
     * 开始时间
     */
    private Long startDate;

    /**
     * 结束时间
     */
    private Long endDate;

    /**
     * 座位
     */
    private Integer seat;

    /**
     * 活动模板id
     */
    @NotNull(message = "请选择活动模板")
    private Long templateId;
}
