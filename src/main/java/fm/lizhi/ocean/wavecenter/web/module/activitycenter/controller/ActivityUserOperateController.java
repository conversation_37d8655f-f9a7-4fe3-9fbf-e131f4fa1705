package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestUserCancelActivity;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestUserModifyActivityAfterAudit;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseUserCancelActivity;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOperateService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.ActivityCancelParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.ActivityModifyAfterAuditParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityCancelResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/activity")
public class ActivityUserOperateController {

    @Autowired
    private ActivityOperateService activityOperateService;

    /**
     * 用户取消活动
     *
     * @param paramVO 取消活动参数
     * @return 取消活动结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER})
    @PostMapping("/cancel")
    public ResultVO<ActivityCancelResult> cancelActivity(@RequestBody ActivityCancelParam paramVO) {
        RequestUserCancelActivity request = new RequestUserCancelActivity();
        request.setActivityId(paramVO.getActivityId());
        request.setReason(paramVO.getReason());
        request.setVersion(paramVO.getVersion());
        request.setOperateUserId(ContextUtils.getContext().getSubjectId());
        request.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());

        Result<ResponseUserCancelActivity> result = activityOperateService.userCancelActivityV2(request);
        if (RpcResult.isFail(result)) {
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        ResponseUserCancelActivity target = result.target();
        ActivityCancelResult activityCancelResult = new ActivityCancelResult();
        if (target != null && StringUtils.isBlank(target.getErrorMsg())) {
            activityCancelResult.setMessage(target.getErrorMsg());
        }
        return ResultVO.success(activityCancelResult);
    }

    /**
     * 用户修改审批后的活动
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER})
    @PostMapping("/modifyAfterAudit")
    public ResultVO<String> modifyAfterAudit(@RequestBody ActivityModifyAfterAuditParam paramVO) {
        RequestUserModifyActivityAfterAudit request = new RequestUserModifyActivityAfterAudit();
        request.setActivityId(paramVO.getActivityId());
        request.setVersion(paramVO.getVersion());
        request.setAccompanyNjIds(CollectionUtils.isNotEmpty(paramVO.getAccompanyNjIdList()) ?
                paramVO.getAccompanyNjIdList().stream().map(String::valueOf).collect(Collectors.joining(",")) : "");
        request.setHostId(paramVO.getHostId());
        request.setOperateUserId(ContextUtils.getContext().getUserId());
        request.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());

        Result<Void> result = activityOperateService.userModifyActivityAfterAudit(request);
        if (RpcResult.isFail(result)) {
            log.error("用户修改审批后的活动失败，request: {}, result: {}", request, result);
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        return ResultVO.success();
    }

}
