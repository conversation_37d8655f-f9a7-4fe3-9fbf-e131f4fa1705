package fm.lizhi.ocean.wavecenter.web.config;

import com.dianping.cat.aop.SpringMvcUrlAspect;
import fm.lizhi.biz.data.collector.DataCollectFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * WEB相关初始化
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Bean
    public FilterRegistrationBean<CorsFilter> corsFilter() {
        FilterRegistrationBean<CorsFilter> filterRegistrationBean = new FilterRegistrationBean<>();
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedOrigin("*");
        corsConfiguration.addAllowedHeader("*");
        corsConfiguration.addAllowedMethod("*");
        corsConfiguration.setAllowCredentials(true);
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfiguration);
        filterRegistrationBean.setFilter(new CorsFilter(source));
        filterRegistrationBean.setOrder(0);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean<DataCollectFilter> dataCollectFilter() {
        DataCollectFilter filter = new DataCollectFilter(request -> 0L);
        FilterRegistrationBean<DataCollectFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(filter);
        registration.setName("dataCollect-filter");
        registration.addUrlPatterns("/*");
        registration.setOrder(3);
        return registration;
    }

    /**
     * 打点上报URL请求信息
     *
     * @return 切面
     */
    @Bean
    public SpringMvcUrlAspect aspect() {
        return new SpringMvcUrlAspect();
    }
}
