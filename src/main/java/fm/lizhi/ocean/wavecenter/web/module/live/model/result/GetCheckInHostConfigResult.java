package fm.lizhi.ocean.wavecenter.web.module.live.model.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveCheckInHostConfigBean;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.CheckInHostConfigVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 麦序福利表主持人配置响应.
 * <AUTHOR>
 */
@Data
public class GetCheckInHostConfigResult {

    /**
     * 应用ID.
     */
    private Integer appId;

    /**
     * 家族ID.
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 房间ID.
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long roomId;


    /**
     * 主持人配置列表
     */
    private List<CheckInHostConfigVo> hostDetails;

    /**
     * 周开始时间
     */
    private Long startTime;

    /**
     * 周结束时间
     */
    private Long endTime;
}
