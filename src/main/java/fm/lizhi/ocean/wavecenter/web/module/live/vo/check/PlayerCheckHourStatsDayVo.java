package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import fm.lizhi.ocean.wavecenter.web.common.BigDecimalDownSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 陪玩小时统计-日集合
 * <AUTHOR>
 * @date 2024/6/11 17:59
 */
@Data
@Accessors(chain = true)
public class PlayerCheckHourStatsDayVo {

    private Date time;

    private List<PlayerCheckHourStatsVo> detail;

    /**
     * 合计
     */
    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal income;

    private long charm;

    private int seatOrder;

    private int hostCnt;

}
