package fm.lizhi.ocean.wavecenter.web.module.live.handler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SignUtil;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomStatisticReport;
import fm.lizhi.ocean.wavecenter.api.live.service.WaveCheckInDataService;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.live.model.converter.CheckInConverter;
import fm.lizhi.ocean.wavecenter.web.module.live.model.param.GetCheckInRoomReportParam;
import fm.lizhi.ocean.wavecenter.web.module.live.model.result.GetCheckInRoomReportResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;


@Component
@Slf4j
public class CheckInReportHandler {

    @Autowired
    private WaveCheckInDataService checkInDataService;
    @Autowired
    private RedisHandler redisHandler;
    @Autowired
    private AppConfig config;
    @Autowired
    private CheckInConverter converter;



    public GetCheckInRoomReportResult getCheckInRoomReportResult(GetCheckInRoomReportParam param) {
        GetCheckInRoomReportResult result = new GetCheckInRoomReportResult();
        // room
        Result<ResponseGetCheckInRoomStatisticReport> reportData =
                checkInDataService.getCheckInRoomStatisticReport(converter.toRequestGetCheckInRoomStatisticReport(param));
        if(reportData.rCode() != 0) {
            log.error("getCheckInRoomReportResult fail;rCode={};msg={};param={}", reportData.rCode(), reportData.getMessage(), param);
            result.setList(Collections.emptyList());
            return result;
        }
        result.setList(converter.toGetCheckInRoomReportResult(reportData.target().getList()));
        result.setRoomInfo(converter.toUser(reportData.target().getRoomInfo()));
        return result;
    }


    /**
     * 同个ip 1分钟不能超过100次请求
     * @param appId
     * @param ip
     * @return
     */
    public boolean isLimit(Integer appId, String ip) {
        try {
            if (!config.isCheckInReportLimitSwitch()) {
                return false;
            }
            long count = increaseCountPerMin(appId, ip);
            return count >= config.getCheckInReportLimitMaxCountPerMin();
        } catch (Exception e) {
            log.warn("isLimit fail, appId={};deviceId={}", appId, ip, e);
            return false;
        }
    }

    public long increaseCountPerMin(int appId, String ip) {
        RedisClient redisClient = redisHandler.getRedisClient();
        String time = DateUtil.format(new Date(), "HHmm");
        String key = "WEB_OCEAN_WC_LIMIT_" + time + "_" + appId + "_" + ip;
        Long count = redisClient.incr(key);
        redisClient.expire(key, 60);
        return count;
    }

    public boolean verifySign(GetCheckInRoomReportParam param) {
        if (!config.isCheckInReportVerifySignSwitch()) {
            return true;
        }
        //todo 封装一下 先上线 下次一定
        TreeMap<String, Object> sortedParams = new TreeMap<>();
        sortedParams.put("appId", param.getAppId().toString());
        sortedParams.put("dateType", param.getDateType().name());
        sortedParams.put("njId", param.getNjId().toString());
        sortedParams.put("startDate", param.getStartDate().toString());
        sortedParams.put("endDate", param.getEndDate().toString());
        String signed = SignUtil.signParamsMd5(sortedParams, config.getCheckInMD5SaltValue());
        return signed.equals(param.getSignCode());
    }
}
