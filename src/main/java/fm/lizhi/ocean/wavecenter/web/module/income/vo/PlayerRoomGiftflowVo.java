package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/25 10:27
 */
@Data
public class PlayerRoomGiftflowVo {

//    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date date;

    private UserVo recRoomInfo;

    private UserVo sendUserInfo;

    private UserVo recUserInfo;

    private String giftName;

    private Integer charm;

    private String income;

    private String content;

}
