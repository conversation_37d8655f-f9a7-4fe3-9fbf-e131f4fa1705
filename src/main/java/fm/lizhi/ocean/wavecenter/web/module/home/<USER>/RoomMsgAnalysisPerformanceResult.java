package fm.lizhi.ocean.wavecenter.web.module.home.result;

import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsPerformanceBean;
import fm.lizhi.ocean.wavecenter.web.module.home.vo.MetricsPerformanceVO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 厅私信拓客分析
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RoomMsgAnalysisPerformanceResult {


    /**
     * 私信用户数
     */
    private MetricsPerformanceVO chatUserCnt;

    /**
     * 私信回复数
     */
    private MetricsPerformanceVO replyChatUserCnt;

    /**
     * 私信进房数
     */
    private MetricsPerformanceVO chatEnterRoomUserCnt;


    /**
     * 私信付费数
     */
    private MetricsPerformanceVO chatGiftUserCnt;

}
