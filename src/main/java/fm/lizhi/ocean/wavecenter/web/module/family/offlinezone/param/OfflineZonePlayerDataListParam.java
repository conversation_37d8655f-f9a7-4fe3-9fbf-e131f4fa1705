package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 离线区域主播数据列表请求参数
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Accessors(chain = true)
public class OfflineZonePlayerDataListParam {

    /**
     * 开始时间 YYYY-MM-DD
     */
    private String startDate;

    /**
     * 结束时间 YYYY-MM-DD
     */
    private String endDate;

    /**
     * 签约主播ID
     */
    private String playerId;

    /**
     * 签约主播名称
     */
    private String playerName;

    /**
     * 主播分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 签约厅ID
     */
    private Long njId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序类型：ASC/DESC
     */
    private String orderType;

    /**
     * 排序指标
     */
    private String orderMetrics;

    /**
     * 是否受跳槽保护
     */
    private Boolean protection;

    /**
     * 保护状态：0 未上传，1 已上传，2 已生效，3 主播同意，4 主播拒绝，5 上传过期
     */
    private Integer protectionStatus;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNo = 1;

    /**
     * 页大小
     */
    @Min(value = 1, message = "页大小不能小于1")
    @Max(value = 100, message = "页大小不能大于100")
    private Integer pageSize = 20;
}
