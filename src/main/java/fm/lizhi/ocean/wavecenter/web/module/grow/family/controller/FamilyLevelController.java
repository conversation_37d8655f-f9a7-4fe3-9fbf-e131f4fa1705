package fm.lizhi.ocean.wavecenter.web.module.grow.family.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLastLevelBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigAwardBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLastLevel;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLevelConfigList;
import fm.lizhi.ocean.wavecenter.api.grow.level.service.FamilyLevelConfigService;
import fm.lizhi.ocean.wavecenter.api.grow.level.service.FamilyLevelService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.grow.family.convert.FamilyLevelConvert;
import fm.lizhi.ocean.wavecenter.web.module.grow.family.vo.FamilyLevelConfigVO;
import fm.lizhi.ocean.wavecenter.web.module.grow.family.vo.FamilyLevelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/14 17:15
 */
@Slf4j
@RestController
@RequestMapping("/grow/family/level")
public class FamilyLevelController {

    @Autowired
    private FamilyLevelConfigService familyLevelConfigService;
    @Autowired
    private FamilyLevelService familyLevelService;

    /**
     * 公会等级-等级配置列表
     * @return
     */
    @VerifyUserToken
    @GetMapping("list")
    public ResultVO<List<FamilyLevelConfigVO>> list(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<List<FamilyLevelConfigAwardBean>> result = familyLevelConfigService.list(new RequestGetFamilyLevelConfigList()
                .setAppId(appId)
        );
        if (RpcResult.isFail(result)) {
            log.error("family level config error. rCode={},appId={}", result.rCode(), appId);
            return ResultVO.failure("等级加载失败");
        }
        List<FamilyLevelConfigAwardBean> beanList = result.target();
        beanList.sort(Comparator.comparing(FamilyLevelConfigAwardBean::getLevelValue));
        return ResultVO.success(FamilyLevelConvert.I.configAwardBeans2VOs(beanList));
    }

    /**
     * 公会等级-查询公会等级和收入
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY})
    @VerifyUserToken
    @GetMapping("getFamilyLevel")
    public ResultVO<FamilyLevelVO> getFamilyLevel() {
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<FamilyLastLevelBean> result = familyLevelService.getFamilyLastLevel(new RequestGetFamilyLastLevel()
                .setFamilyId(familyId)
                .setAppId(appId));
        if (RpcResult.isFail(result) && result.rCode() != FamilyLevelService.GET_FAMILY_LAST_LEVEL_NOT_EXIST) {
            log.error("getFamilyLastLevel fail. familyId={},appId={},rCode={}", familyId, appId, result.rCode());
            return ResultVO.failure("等级加载失败");
        }

        if (FamilyLevelService.GET_FAMILY_LAST_LEVEL_NOT_EXIST == result.rCode()) {
            return ResultVO.success(new FamilyLevelVO());
        }

        FamilyLastLevelBean target = result.target();
        return ResultVO.success(new FamilyLevelVO()
                .setIncome(target.getExp())
                .setLevelId(target.getLevelId()));
    }



}
