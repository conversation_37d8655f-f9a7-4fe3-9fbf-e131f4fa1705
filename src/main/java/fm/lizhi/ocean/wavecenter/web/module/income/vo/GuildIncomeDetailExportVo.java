package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/23 20:19
 */
@Data
public class GuildIncomeDetailExportVo {

    @ExcelProperty(value = "记录时间")
    @DateTimeFormat(DatePattern.NORM_DATETIME_PATTERN)
    private Date date;

    @ExcelProperty(value = "收入类型")
    private String incomeName;

    /**
     * 昵称
     */
    @ExcelProperty(value = "签约厅")
    private String name;
    /**
     * 波段号
     */
    @ExcelProperty(value = "签约厅ID")
    private String band;

    @ExcelProperty(value = "收入钻")
    private String income;

    @ExcelProperty(value = "内容")
    private String content;

}
