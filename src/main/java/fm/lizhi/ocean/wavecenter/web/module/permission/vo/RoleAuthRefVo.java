package fm.lizhi.ocean.wavecenter.web.module.permission.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11 11:53
 */
@Data
public class RoleAuthRefVo {

    /**
     * 配置数据的ID
     */
    private String id;

    /**
     * 被授权用户
     */
    private UserVo userInfo;

    /**
     * 角色code
     */
    private String roleCode;

    /**
     * 授权账号
     */
    private UserVo subject;

    /**
     * 状态
     * 1=启用 0=禁用
     */
    private Integer status;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date modifyTime;

    /**
     * 授权厅列表
     */
    private List<UserVo> authRoomList;

}
