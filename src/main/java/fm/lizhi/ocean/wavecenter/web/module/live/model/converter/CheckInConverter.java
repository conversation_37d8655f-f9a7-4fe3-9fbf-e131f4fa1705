package fm.lizhi.ocean.wavecenter.web.module.live.model.converter;

import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveCheckInHostConfigBean;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestGetCheckInConfig;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestSaveCheckInConfig;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestSaveCheckInHostConfig;
import fm.lizhi.ocean.wave.platform.api.platform.request.ResponseGetCheckInHostConfig;
import fm.lizhi.ocean.wave.platform.api.platform.response.ResponseGetCheckInConfig;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInRoomStatisticBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInRoomStatisticReportBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserBean;
import fm.lizhi.ocean.wavecenter.api.live.request.*;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInPlayerStatistic;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomSum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.web.module.live.constant.CheckInExportSheetDataType;
import fm.lizhi.ocean.wavecenter.web.module.live.constant.CheckInMetricsEnum;
import fm.lizhi.ocean.wavecenter.web.module.live.model.param.*;
import fm.lizhi.ocean.wavecenter.web.module.live.model.result.*;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.CheckInHostConfigVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import org.mapstruct.*;

import java.util.Date;
import java.util.List;

/**
 * 麦序福利转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING
        , unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface CheckInConverter {
    @Named("convertDate")
    default Date convertDate(String date) {
        return DateUtil.parse(date, "yyyy-MM-dd");
    }

    default Long dateToLong(Date date) {
        return date.getTime();
    }


    RequestSaveCheckInConfig toRequestSaveCheckInConfig(SaveCheckInConfigParam param, Integer appId, Long familyId, Long roomId, Long njId);

    RequestGetCheckInConfig toRequestGetCheckInConfig(Integer appId, Long familyId, Long roomId);

    @Mapping(target = "dateType", ignore = true)
    RequestGetCheckInRoomSum toRequestGetCheckInRoomSum(GetCheckInRoomSumParam param, Integer appId, Long roomId, Long familyId);

    GetCheckInRoomSumResult toGetCheckInRoomSumResult(ResponseGetCheckInRoomSum resp, Integer signPlayerCnt, Integer incomePlayerCnt);

    RequestGetCheckInRoomStatistic toRequestGetCheckInRoomStatistic(GetCheckInRoomDetailParam param, Integer appId, Long roomId, Long familyId);

    @Mapping(target = "hostInfo", ignore = true)
    @Mapping(target = "detailSum", ignore = true)
    GetCheckInRoomDetailResult toGetCheckInRoomDetailResult(ResponseGetCheckInRoomStatistic resp);

    GetCheckInRoomDetailResult.Statistic waveCheckInRoomStatisticBean2Statistic(WaveCheckInRoomStatisticBean bean);

    RequestGetCheckInRoomStatistic toRequestGetCheckInRoomStatistic(ExportCheckInRoomSheetParam param, Long roomId, Long familyId);

    @Mapping(target = "njId", source = "njId")
    ExportCheckInRoomSheetParam toExportCheckInRoomSheetParam(ExportCheckInRoomDetailParam param, Integer appId, Long njId, String sheetName, CheckInExportSheetDataType sheetDataType);

    @Mapping(target = "playerId", source = "playerId")
    RequestGetCheckInPlayerSum toRequestGetCheckInPlayerSum(GetCheckInPlayerSumParam param, Integer appId, Long playerId, Long familyId, Long roomId);

    @Mapping(target = "playerId", source = "playerId")
    RequestGetCheckInPlayerStatistic toRequestGetCheckInPlayerStatistic(GetCheckInPlayerDetailParam param, Integer appId, Long playerId, Long familyId, Long roomId);

    GetCheckInPlayerDetailResult toGetCheckInPlayerDetailResult(ResponseGetCheckInPlayerStatistic resp);

    @Mapping(target = "playerId", source = "playerId")
    RequestGetCheckInPlayerStatistic toRequestGetCheckInPlayerStatistic(ExportCheckInPlayerDetailParam param, Integer appId, Long familyId, Long roomId, Long playerId);

    @Mappings({
            @Mapping(target = "startTime", source = "param.startDate", qualifiedByName = "convertDate"),
            @Mapping(target = "endTime", source = "param.endDate", qualifiedByName = "convertDate"),
    })
    RequestSaveCheckInHostConfig toRequestSaveCheckInHostConfig(SaveCheckInHostConfigParam param, int appId, Long familyId, Long roomId);

    @Mappings({
            @Mapping(target = "hostInfo", ignore = true),
    })
    CheckInHostConfigVo toCheckInHostConfigVo(WaveCheckInHostConfigBean bean);
    GetCheckInHostConfigResult toGetCheckInHostConfigResult(ResponseGetCheckInHostConfig resp);

    @Mapping(source = "checkInManagerConfig", target = "checkInManagerConfig")
    GetCheckInConfigResult toGetCheckInConfigResult(ResponseGetCheckInConfig resp, List<UserVo> checkInManagerConfig);

    List<UserVo> userBeans2Vos(List<UserBean> managerBeanList);

    GetCheckInRoomDetailResult.User waveCheckInUserBeanToUser(WaveCheckInUserBean host);

    RequestGetCheckInRoomStatisticReport toRequestGetCheckInRoomStatisticReport(GetCheckInRoomReportParam param);

    List<GetCheckInRoomReportResult.ReportData> toGetCheckInRoomReportResult(List<WaveCheckInRoomStatisticReportBean> list);

    GetCheckInRoomReportResult.User toUser(WaveCheckInUserBean roomInfo);
}
