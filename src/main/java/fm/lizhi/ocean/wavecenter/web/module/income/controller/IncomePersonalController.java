package fm.lizhi.ocean.wavecenter.web.module.income.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.GetPersonalIncomeDetailParamBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.GetPersonalIncomeDetailSumParamBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.PersonalIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.PersonalIncomeDetailSumBean;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomePlayerService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.util.WcAssert;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.income.convert.IncomeConvert;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26 12:47
 */
@Slf4j
@RestController
@RequestMapping("income/personal/")
public class IncomePersonalController {

    @Autowired
    private IncomePlayerService incomeService;

    @Autowired
    private FileExportHandler fileExportHandler;

    /**
     * 个人收益-个人收入明细
     */
    @VerifyUserToken
    @GetMapping("/incomeDetail")
    public ResultVO<PageVO<PersonalIncomeDetailVo>> personalIncomeDetail(@Validated GetPersonalIncomeDetailParamVo paramVo){
        DateTime startDate = DateUtil.parse(paramVo.getStartDate()+ " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate()+ " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);
        Long flushTime = endDate.getTime()> paramVo.getFlushTime()? paramVo.getFlushTime():endDate.getTime();

        Result<PageBean<PersonalIncomeDetailBean>> result = incomeService.getPersonalIncomeDetail(GetPersonalIncomeDetailParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .endDate(endDate)
                .startDate(startDate)
                .flushTime(flushTime)
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .build());
        if (RpcResult.isFail(result)) {
            log.error("personalIncomeDetail,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        PageBean<PersonalIncomeDetailBean> target = result.target();
        List<PersonalIncomeDetailVo> voList = IncomeConvert.I.personalIncomeDetailBeans2Vos(target.getList());
        return ResultVO.success(PageVO.of(target.getTotal(), voList,flushTime));
    }

    /**
     * 个人收入-收入账户明细记录-导出
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("/incomeDetail/export")
    public ResultVO<Void> personalIncomeDetailExport(@Validated GetPersonalIncomeDetailExportParamVo paramVo){
        DateTime startDate = DateUtil.parse(paramVo.getStartDate()+ " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate()+ " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        String fileName = "个人收入账户明细_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, PersonalIncomeDetailExportVo.class, (pageNo, pageSize)->{
            Result<PageBean<PersonalIncomeDetailBean>> result = incomeService.getPersonalIncomeDetailOut(GetPersonalIncomeDetailParamBean.builder()
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .userId(ContextUtils.getContext().getUserId())
                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                    .endDate(endDate)
                    .startDate(startDate)
                    .build());
            if (RpcResult.isFail(result)) {
                log.error("personalIncomeDetailExport fail pageNo={}, pageSize={}, rCode={}, paramVo={}", pageNo
                        , pageSize, result.rCode(), JsonUtil.dumps(paramVo));
                return PageVO.empty();
            }
            int total = result.target().getTotal();
            List<PersonalIncomeDetailBean> list = result.target().getList();
            return PageVO.of(total, IncomeConvert.I.personalIncomeDetailBeans2ExportVos(list));
        });

    }

    /**
     * 个人收入-收入账户明细记录-合计
     *
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("/incomeDetail/sum")
    public ResultVO<PersonalIncomeDetailSumVo> personalIncomeDetailSum(@Validated GetDetailBaseParamVo paramVo){
        DateTime startDate = DateUtil.parse(paramVo.getStartDate()+ " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate()+ " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Result<PersonalIncomeDetailSumBean> result = incomeService.getPersonalIncomeDetailSum(GetPersonalIncomeDetailSumParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .endDate(endDate)
                .startDate(startDate)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("personalIncomeDetailSum,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }

        return ResultVO.success(IncomeConvert.I.personalIncomeDetailSumBean2Vo(result.target()));
    }

}
