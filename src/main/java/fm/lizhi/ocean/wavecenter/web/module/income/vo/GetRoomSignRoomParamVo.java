package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:17
 */
@Data
public class GetRoomSignRoomParamVo {

    @NotBlank(message = "开始时间不可为空")
    private String startDate;

    @NotBlank(message = "结束时间不可为空")
    private String endDate;

    /**
     * 厅主ID
     */
    private Long roomId;


    /**
     * 送礼人ID
     */
    private Long sendUserId;

    /**
     * 收礼人
     */
    private Long recUserId;



    @Min(value = 1)
    private Integer pageNo = 1;

    @Min(value = 1)
    @Max(value = 100)
    private Integer pageSize = 20;

    private Long flushTime = System.currentTimeMillis();
}
