package fm.lizhi.ocean.wavecenter.web.module.grow.ability.convert;

import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPlayerRank;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPlayerRank;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.param.GetPlayerPerformanceParam;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.param.GetRoomPerformanceParam;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.param.GetRoomPlayerRankParam;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.vo.GetPlayerPerformanceResult;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.vo.GetRoomPerformanceResult;
import fm.lizhi.ocean.wavecenter.web.module.grow.ability.vo.GetRoomPlayerRankResult;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface GrowAbilityConvert {

    RequestGetRoomPerformance toRequestGetRoomPerformance(GetRoomPerformanceParam param, Integer appId, Long roomId);

    GetRoomPerformanceResult toGetRoomPerformanceResult(ResponseGetRoomPerformance response);

    RequestGetRoomPlayerRank toRequestGetRoomPlayerRank(GetRoomPlayerRankParam param, Integer appId, Long roomId);

    GetRoomPlayerRankResult toGetRoomPlayerRankResult(ResponseGetRoomPlayerRank response);

    RequestGetPlayerPerformance toRequestGetPlayerPerformance(GetPlayerPerformanceParam param, Integer appId, Long roomId);

    GetPlayerPerformanceResult toGetPlayerPerformanceResult(ResponseGetPlayerPerformance response);
}
