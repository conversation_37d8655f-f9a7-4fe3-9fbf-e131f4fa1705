package fm.lizhi.ocean.wavecenter.web.module.sign.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/25 19:43
 */
@Data
@Accessors(chain = true)
public class FamilyUnwindResult {

    /**
     * 合同签约url
     */
    private String contractUrl;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

}
