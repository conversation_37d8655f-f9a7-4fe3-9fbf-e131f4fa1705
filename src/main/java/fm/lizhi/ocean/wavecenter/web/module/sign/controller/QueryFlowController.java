package fm.lizhi.ocean.wavecenter.web.module.sign.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.ActFlowBean;
import fm.lizhi.ocean.wavecenter.api.sign.service.QueryFlowService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.sign.convert.SignConvert;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.CurrentActivityFlowVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 签约流程查询服务
 * <AUTHOR>
 * @date 2024/10/5 09:52
 */
@Slf4j
@RestController
@RequestMapping("/sign/query/flow")
public class QueryFlowController {

    @Autowired
    private QueryFlowService queryFlowService;

    /**
     * 查询当前激活的流程和签署信息
     * 如果没有激活的流程，就只返回签署信息
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER, RoleEnum.USER})
    @GetMapping("currentActivity")
    public ResultVO<CurrentActivityFlowVo> currentActivity(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();

        //不同角色查询不同的流程
        boolean isRoom = ContextUtils.getContext().isRoom();
        boolean isPlayer = ContextUtils.getContext().isPlayer();
        Result<ActFlowBean> actFlowBeanResult = null;
        if (isRoom) {
            actFlowBeanResult = queryFlowService.adminActCancelFlow(appId, userId);
        }
        else if (isPlayer) {
            actFlowBeanResult = queryFlowService.playerActCancelFlow(appId, userId);
        }
        else {
            actFlowBeanResult = queryFlowService.userActSignFlow(appId, userId);
        }

        if (RpcResult.isFail(actFlowBeanResult)) {
            log.error("currentActivity fail. rCode={},appId={},userId={}", actFlowBeanResult.rCode(), appId, userId);
            return ResultVO.failure("查询流程失败");
        }

        ActFlowBean actFlowBean = actFlowBeanResult.target();
        if (actFlowBean == null) {
            return ResultVO.success();
        }

        return ResultVO.success(SignConvert.I.actFlowBean2Vo(actFlowBean));
    }

}
