package fm.lizhi.ocean.wavecenter.web.module.datacenter.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.RoomDataService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyAuthBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.convert.DataCenterConvert;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 厅数据
 * <AUTHOR>
 * @date 2024/4/18 15:07
 */
@Slf4j
@RestController
@RequestMapping("room/data")
public class RoomDataController {

    @Autowired
    private UserFamilyService userFamilyService;
    @Autowired
    private RoomDataService roomDataService;
    @Autowired
    private DataScopeHandler dataScopeHandler;

    /**
     * 趋势图
     * @param metric
     * @param roomId
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("indicatorTrend")
    public ResultVO<IndicatorTrendResVo> indicatorTrend(@RequestParam("metric")String metric
            , @RequestParam(value = "roomId", required = false)Long roomId){
        if (!dataScopeHandler.checkParamRoom(roomId)) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        //家族长访问时，只能查看访问的厅在该家族长下的数据
        Long familyId = dataScopeHandler.getFamilyForFamily();
        Long queryRoomId = dataScopeHandler.getRoomForRoomOrDefault(roomId);

        //校验
        if (queryRoomId == null) {
            return ResultVO.failure("请选择厅");
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<IndicatorTrendResBean> result = roomDataService.getIndicatorTrend(appId, familyId, queryRoomId, metric);
        if (RpcResult.isFail(result)) {
            log.error("indicatorTrend,error,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(DataCenterConvert.I.indicatorTrendResBean2Vo(result.target()));
    }

    /**
     * 查询关键指标数据
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("keyIndicators")
    public ResultVO<RoomGetKeyIndicatorsResVo> keyIndicators(@Validated RoomGetKeyIndicatorsParamVo paramVo){
        //参数校验
        if (CollectionUtils.isEmpty(paramVo.getRatioMetrics()) && CollectionUtils.isEmpty(paramVo.getValueMetrics())) {
            return ResultVO.success(new RoomGetKeyIndicatorsResVo());
        }

        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Long familyId = dataScopeHandler.getFamilyForFamily();
        Long roomId = dataScopeHandler.getRoomForRoomOrDefault(paramVo.getRoomId());
        //校验
        if (roomId == null) {
            return ResultVO.failure("请选择厅");
        }

        Result<List<IndicatorBean>> result = roomDataService.getKeyIndicators(RoomGetKeyIndicatorsParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .roomId(roomId)
                .familyId(familyId)
                .ratioMetrics(paramVo.getRatioMetrics())
                .valueMetrics(paramVo.getValueMetrics())
                .startDate(paramVo.getStartDate())
                .endDate(paramVo.getEndDate())
                .dateType(DateType.fromValue(paramVo.getDateType()))
                .build());
        if (RpcResult.isFail(result)) {
            log.error("keyIndicators,error,rCode={}", result.rCode());
            return ResultVO.failure();
        }
        List<IndicatorBean> indicatorBeanList = result.target();
        return ResultVO.success(new RoomGetKeyIndicatorsResVo()
                .setIndicators(DataCenterConvert.I.indicatorBeans2Vos(indicatorBeanList)));
    }

    /**
     * 查询厅主播考核周期的业绩列表
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("playerPerformance")
    public ResultVO<RoomPlayerPerformanceResVo> playerPerformance(@Validated RoomPlayerPerformanceParamVo paramVo){
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long roomId = dataScopeHandler.getRoomForRoomOrDefault(paramVo.getRoomId());

        //校验
        if (roomId == null) {
            return ResultVO.failure("请选择厅");
        }
        if (familyId == null) {
            return ResultVO.failure("当前厅没有签约家族");
        }

        Result<RoomPlayerPerformanceResBean> result = roomDataService.getPlayerPerformance(GetRoomPlayerPerformanceBean.builder()
                .roomId(roomId)
                .familyId(familyId)
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .orderMetrics(MetricsEnum.fromValue(paramVo.getOrderMetrics()))
                .orderType(OrderType.fromValue(paramVo.getOrderType()))
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .build());
        if (RpcResult.isFail(result)) {
            log.error("playerPerformance,error,rCode={}", result.rCode());
            return ResultVO.failure();
        }

        RoomPlayerPerformanceResVo vo = DataCenterConvert.I.roomPlayerPerformanceResBean2Vo(result.target());
        vo.setFlushTime(new Date());
        return ResultVO.success(vo);
    }

    /**
     * 厅业绩
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("assessmentInfo")
    public ResultVO<RoomAssessmentInfoVo> assessmentInfo(@RequestParam(value = "roomId", required = false) Long roomId){
        if (!dataScopeHandler.checkParamRoom(roomId)) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long queryRoomId = dataScopeHandler.getRoomForRoomOrDefault(roomId);
        //校验
        if (queryRoomId == null) {
            return ResultVO.failure("请选择厅");
        }
        if (familyId == null) {
            return ResultVO.failure("当前厅没有签约家族");
        }

        //查询签约状态
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<RoomSignBean> roomSignInfoRes = userFamilyService.getRoomSignInfo(appId, familyId, queryRoomId);
        if (RpcResult.isFail(roomSignInfoRes)) {
            log.error("assessmentInfo error appId={} familyId={} queryRoomId={}",appId,familyId,queryRoomId);
            return ResultVO.failure("");
        }
        RoomSignBean roomSignBean = roomSignInfoRes.target();

        //查询数据
        Result<RoomAssessmentInfoBean> assessmentInfoRes = roomDataService.getAssessmentInfo(appId, queryRoomId, familyId);
        if (RpcResult.isFail(assessmentInfoRes)) {
            log.error("assessmentInfo getAssessmentInfo error appId={} familyId={} queryRoomId={}",appId,familyId,queryRoomId);
            return ResultVO.failure();
        }
        RoomAssessmentInfoBean roomAssessmentInfoBean = assessmentInfoRes.target();
        RoomAssessmentInfoVo vo = DataCenterConvert.I.roomAssessmentInfoBean2Vo(roomAssessmentInfoBean);
        vo.setRoomInfo(UserCommonConvert.I.roomSignBean2Vo(roomSignBean));
        return ResultVO.success(vo);
    }

    /**
     * 厅数据-家族信息
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN, RoleEnum.PLAYER})
    @VerifyUserToken
    @GetMapping("familyInfo")
    public ResultVO<FamilyAuthVo> familyInfo(){
        // 获取当前角色对应的家族ID
        Long familyId = dataScopeHandler.getFamilyForBase();

        // 校验家族ID
        if (familyId == null) {
            return ResultVO.failure("未找到关联的家族信息");
        }

        // 调用服务获取家族认证信息
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<FamilyAuthBean> rpcResult = userFamilyService.getUserFamilyAuth(appId, familyId);

        // 处理结果
        if (RpcResult.isFail(rpcResult)) {
            log.error("familyInfo,getUserFamilyAuth,error,rpcRCode={}", rpcResult.rCode());
            return ResultVO.failure();
        }

        // 转换并返回
        return ResultVO.success(DataCenterConvert.I.familyAuthBean2Vo(rpcResult.target()));
    }
}
