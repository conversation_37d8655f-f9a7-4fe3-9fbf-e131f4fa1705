package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import lombok.Data;

@Data
public class ActivityNoticeVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private Integer appId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 公告内容
     */
    private String content;
}
