package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Accessors(chain = true)
public class GetOfficialSeatTimeParamVO {

    /**
     * 开始时间
     */
    private Long startDate;

    /**
     * 结束时间
     */
    private Long endDate;

    /**
     * 座位
     */
    @NotNull(message = "请选择官频位位置")
    private Integer seat;
}
