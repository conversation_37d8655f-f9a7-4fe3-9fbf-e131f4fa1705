package fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityInfoDetail;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.constants.ActivityFlowResourceGiveStatusEnum;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.GetActivityInfoDetailResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityApplyConvert {

    ActivityApplyConvert I = Mappers.getMapper(ActivityApplyConvert.class);

    @Mappings({
            @Mapping(target = "applyType", expression = "java(fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum.NJ_APPLY)"),
            @Mapping(target = "applicantUid", expression = "java(fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils.getContext().getUserId())"),
            @Mapping(target = "njId", ignore = true),
            @Mapping(target = "appId", expression = "java(fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils.getBusinessEvnEnum().getAppId())"),

    })
    RequestActivityApplyBean activityApplyParamVO2Bean(ActivityApplyParamVO vo);

    @Mappings({
            @Mapping(target = "pageParam", ignore = true),
            @Mapping(target = "appId", expression = "java(fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils.getBusinessEvnEnum().getAppId())"),
            @Mapping(target = "applyType", expression = "java(fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum.NJ_APPLY.getApplyType())"),
    })
    RequestQueryActivityListBean queryActivityListParamVO2Bean(QueryActivityListParamVO vo);

    ActivitySimpleInfoVO activitySimpleInfoBean2VO(ActivitySimpleInfoBean bean);

    List<ActivitySimpleInfoVO> activitySimpleInfoBeanList2VOList(List<ActivitySimpleInfoBean> list);

    GetActivityInfoDetailResult responseActivityInfoDetail2ActivityInfoDetailVO(ResponseActivityInfoDetail target);

    DecorateVO decorateBean2Vo(DecorateBean bean);

    List<DecorateVO> decorateBeans2Vos(List<DecorateBean> beans);

    List<ActivityFlowResourceDetailVO> activityFlowResourceDetailBean2VO(List<ActivityFlowResourceDetailBean> target);

    @Mappings({
            @Mapping(target = "giveStatus", qualifiedByName = "convertGiveStatus", source = "giveStatus"),
            @Mapping(target = "resourceConfigId", source = "id"),
            @Mapping(target = "resourceImageUrl", source = "resourceImageUrl"),
    })
    ActivityFlowResourceDetailVO activityFlowResourceDetailBean2VO(ActivityFlowResourceDetailBean target);


    @Named("convertGiveStatus")
    default Integer convertGiveStatus(Integer giveStatus) {
        return ActivityFlowResourceGiveStatusEnum.getStatusByBizStatus(giveStatus);
    }

    OfficialSeatTimeVO convertOfficialSeatBean2VO(OfficialSeatTimeBean time);

    List<OfficialSeatTimeVO> convertOfficialSeatBeans2VOList(List<OfficialSeatTimeBean> timeList);

    List<ResourceTimeVO> convertTimeInfoListBeans2VOList(List<ResourceTimeBean> timeInfoList);
}
