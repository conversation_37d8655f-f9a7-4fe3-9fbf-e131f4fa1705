package fm.lizhi.ocean.wavecenter.web.common;

import lombok.Getter;

/**
 * - 固定为8位数字，分为4段；
 * - a：固定为1
 * - xx：业务，对应业务，从00开始；
 * - yy：功能，对应XxxController文件，从00开始；
 * - zzz：错误，对应XxxController相关的错误码，从001开始；
 * 通用库 00
 * 用户   01
 * 登录   02
 */
@Getter
public enum MsgCodes implements IRCodes {
    SUCCESS(0, "请求成功"),

    //权限相关
    NOT_LOGGED_IN(400, "未登录"),
    PERMISSION_ERROR(401, "无权访问"),
    //登录的token关联的角色不匹配，比如授权角色被收回，签约合同到期。前端跳转到角色选择页面
    CHOSE_DATA_SCOPE(402, "请选择角色"),
    ACCESS_TOKEN_EXPIRED(403, "访问token过期，需要刷新"),
    ONLY_SELF_LOGIN(404, "仅本人登录可操作"),
    DATA_SCOPE_EMPTY(405, "数据权限范围为空"),

    //通用异常
    FAIL(500, "服务繁忙,请稍后再试"),
    BLOCK(501, "请求频繁,请稍后再试"),
    WEB_BLOCK(502, "请求频繁,请稍后再试"),
    PARAM_ERROR(503, "参数错误"),

    // --------------------通用业务类--------------------------
    USER_NOT_FOUND(10100001,"该用户不存在"),
    DATE_TO_LONG(10100002,"查询日期超范围"),

    // --------------------登录相关--------------------------
    LOGIN_AUTH_FAIL(10200001, "鉴权失败"),
    LOGIN_USER_NO_VERIFY(10200002, "用户未认证"),
    LOGIN_USER_INVALID(10200003, "用户状态异常"),
    LOGIN_USER_BAN(10200004, "用户被封禁"),
    LOGIN_RISK_FAILED(10200005, "风控不通过"),
    QR_CODE_NOT_EXIST(10200006, "二维码无效"),


    // --------------------活动相关--------------------------
    ACTIVITY_APPLY_USER_FAMILY_NOT_EXIST(10300001, "家族信息不存在"),

    // --------------------签约相关---------------------------
    PLATFORM_VERIFY_NO_PASS(10400001, "请求用户未完成平台实名认证"),
    MEDIA_INFO_NOT_EXIST(10400002, "请求用户平台信息不完整"),
    PLATFORM_VERIFY_NO_PASS_ERROR(10400003, "对方未完成平台实名认证"),
    MEDIA_INFO_NOT_EXIST_ERROR(10400004, "对方平台信息不完整"),
    SIGN_VERIFY_NO_PASS(10400005, "请求用户未完成上上签实名认证"),
    PLAYER_SIGNED(10400006, "已签约或在签约中"),
    PLAYER_CENTER_AUTH(10400007, "主播中心未认证"),

    // ----------------------授权相关---------------------------
    CONFIG_PARAM_ERROR(10500001, "参数错误"),

    // ----------------------成长系统----------------------------
    FAMILY_LEVEL_NOT_EXIST(10600001, "家族等级不存在"),

    // ----------------------推荐卡相关---------------------------
    RECOMMEND_USE_RECORD_ROOM_TOO_MUCH(10700001, "房间数量超过上限"),

    ;
    private int code;
    private String msg;

    MsgCodes(int rCode, String msg) {
        this.code = rCode;
        this.msg = msg;
    }

    public MsgCodes setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    @Override
    public String toString() {
        return "code: " + this.code + ", msg: " + this.msg;
    }
}