package fm.lizhi.ocean.wavecenter.web.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 将{@link BigDecimal}序列化为百分比格式的序列化器. 精确到百分比中的整数, 四舍五入, 比如0.125会被序列化为13%.
 * 如果需要更高精度, 可以使用@{@link #findContextualConvertingSerializer}方法搭配获取注解属性, 实现通过注解定义精度.
 */
public class PercentBigDecimalSerializer extends StdSerializer<BigDecimal> {

    private static final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);

    public PercentBigDecimalSerializer() {
        super(BigDecimal.class);
    }

    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        String percentInteger = value.multiply(ONE_HUNDRED).setScale(0, RoundingMode.HALF_UP).toPlainString();
        gen.writeString(percentInteger + "%");
    }
}
