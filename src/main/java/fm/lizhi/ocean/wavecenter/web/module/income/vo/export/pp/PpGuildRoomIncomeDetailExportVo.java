package fm.lizhi.ocean.wavecenter.web.module.income.vo.export.pp;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/27 20:07
 */
@Data
public class PpGuildRoomIncomeDetailExportVo {
    /**
     * 厅主信息
     */
    @ExcelProperty(value = "签约厅")
    private String roomName;

    @ExcelProperty(value = "签约厅ID")
    private String roomBand;
    /**
     * 厅品类
     */
    @ExcelProperty(value = "厅品类")
    private String cateName;
    /**
     * 收入
     */
    @ExcelProperty(value = "总收入（钻）")
    private String income;
    /**
     * 魅力值
     */
    @ExcelProperty(value = "总魅力值")
    private String charm;
    /**
     * 签约厅收礼（收入钻）
     */
    @ExcelProperty(value = "签约厅收礼（收入钻）")
    private String signRoomIncome;
    /**
     * 个播收入（钻）
     */
    @ExcelProperty(value = "个播收入（钻）")
    private String  personalRoomIncome;
    /**
     * 厅贵族提成
     */
    @ExcelProperty(value = "厅贵族提成")
    private String signRoomVipIncome;
    /**
     * 个播贵族收入
     */
    @ExcelProperty(value = "个播贵族收入")
    private String personalRoomVipIncome;
    /**
     * 有收入主播数
     */
    @ExcelProperty(value = "有收入主播数")
    private String playerPayCount;
    /**
     * 官方厅收礼收入
     */
    @ExcelProperty(value = "官方厅收礼收入")
    private String officialIncome;
}
