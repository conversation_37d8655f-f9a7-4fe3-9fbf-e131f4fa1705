package fm.lizhi.ocean.wavecenter.web.common.util;

import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;

/**
 * <AUTHOR>
 * @date 2024/4/18 11:02
 */
public class EnvUtil {
    public static boolean isTestEnv() {
        return ConfigUtils.getEnv() == Env.TEST
                || ConfigUtils.getEnv() == Env.LOCAL
                || ConfigUtils.getEnv() == Env.DEV;
    }
}
