package fm.lizhi.ocean.wavecenter.web.module.user.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageConfigBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.SaveConfigReqBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.PageConfigVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.SaveConfigReqVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 14:24
 */
@Slf4j
@RestController
@RequestMapping("/user/personal")
public class UserPersonalController {

    @Autowired
    private CommonConfigService commonConfigService;

    @VerifyUserToken
    @PostMapping("saveConfig")
    public ResultVO<Void> saveConfig(@Validated @RequestBody SaveConfigReqVo reqVo){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        Result<Void> result = commonConfigService.savePageConfig(SaveConfigReqBean.builder()
                .appId(appId)
                .userId(userId)
                .config(reqVo.getConfig())
                .pageCode(reqVo.getPageCode())
                .build());
        if (RpcResult.isFail(result)) {
            log.error("saveConfig,savePageConfig,error,rCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success();
    }

    @VerifyUserToken
    @GetMapping("config")
    public ResultVO<List<PageConfigVo>> config(@RequestParam(value = "pageCode", required = false) String pageCode){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        Result<List<PageConfigBean>> result = commonConfigService.getPageConfig(appId, userId, pageCode);
        if (RpcResult.isFail(result)) {
            log.error("config,getPageConfig,error,rCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(UserCommonConvert.I.pageConfigBeans2Vos(result.target()));
    }


}
