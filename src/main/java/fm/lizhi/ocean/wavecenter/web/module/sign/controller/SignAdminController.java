package fm.lizhi.ocean.wavecenter.web.module.sign.controller;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.AdminSignPlayerRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.TodoSignPlayerBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignAdminService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.sign.convert.SignConvert;
import fm.lizhi.ocean.wavecenter.web.module.sign.param.AdminDoUnwindParam;
import fm.lizhi.ocean.wavecenter.web.module.sign.param.AdminUnwindParam;
import fm.lizhi.ocean.wavecenter.web.module.sign.result.AdminApplyCancelPlayerResult;
import fm.lizhi.ocean.wavecenter.web.module.sign.result.AdminDoUnwindResult;
import fm.lizhi.ocean.wavecenter.web.module.sign.result.AdminInviteUserResult;
import fm.lizhi.ocean.wavecenter.web.module.sign.result.AdminUnwindResult;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/5 15:36
 */
@Slf4j
@RestController
@RequestMapping("sign/admin")
public class SignAdminController {

    @Autowired
    private SignAdminService signAdminService;

    /**
     * 签约主播
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("signPlayer")
    public ResultVO<Void> signPlayer(@Validated @RequestBody AdminSignPlayerParamVo param){
        RequestAdminOperateSign rpcParam = RequestAdminOperateSign.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .playerSignId(param.getContractId())
                .operateType(OperateTypeEnum.getByCode(param.getStatus()))
                .build();
        Result<ResponseAdminOperateSign> result = signAdminService.operateSign(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("signPlayer fail. rCode={}, rpcParam={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        return SignCommonUtil.signCommonCodeCheck(result.target());
    }

    /**
     * 邀请用户
     * @param param
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM}, onlySelfLogin = true)
    @PostMapping("inviteUser")
    @VerifyUserToken
    public ResultVO<AdminInviteUserResult> inviteUser(@Validated @RequestBody AdminInviteUserParamVo param){
        RequestAdminInviteUser rpcParam = RequestAdminInviteUser.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .targetUserId(param.getTargetUserId())
                .build();

        Result<ResponseAdminInviteUser> result = signAdminService.inviteUser(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("inviteUser fail. rCode={},rpcParam={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        Integer code = result.target().getCode();
        if (code == SignAdminService.INVITE_USER_REQ_NOT_ADMIN) {
            return ResultVO.failure("当前用户不是管理员");
        }

        ResultVO<Void> voidResultVO = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voidResultVO.isOK()) {
            return ResultVO.failure(voidResultVO.getRCode(), voidResultVO.getPrompt());
        }

        return ResultVO.success(new AdminInviteUserResult()
                .setContractId(result.target().getContractId())
        );
    }

    /**
     * 签署解约
     * @param paramVo
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("signCancel")
    public ResultVO<Void> signCancel(@Validated @RequestBody AdminSignCancelParamVo paramVo){
        RequestAdminOperateCancel rpcParam = RequestAdminOperateCancel.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .playerSignId(paramVo.getContractId())
                .curUserId(ContextUtils.getContext().getUserId())
                .operateType(OperateTypeEnum.getByCode(paramVo.getOperateType()))
                .build();
        Result<ResponseAdminOperateCancel> result = signAdminService.operateCancel(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("signCancel fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        return SignCommonUtil.signCommonCodeCheck(result.target());
    }

    /**
     * 取消解约
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("withdrawCancel/{contractId}")
    public ResultVO<Void> withdrawCancel(@PathVariable("contractId") Long contractId){
        RequestWithdrawCancel param = RequestWithdrawCancel.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .playerSignId(contractId)
                .build();
        Result<ResponseWithdrawCancel> result = signAdminService.withdrawCancel(param);
        if (RpcResult.isFail(result)) {
            log.error("withdrawCancel fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(param));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        return SignCommonUtil.signCommonCodeCheck(result.target());
    }

    /**
     * 申请解约
     * @param contractId
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("applyCancelPlayer/{contractId}")
    public ResultVO<AdminApplyCancelPlayerResult> applyCancelPlayer(@PathVariable("contractId") Long contractId){
        RequestAdminApplyCancelPlayer param = RequestAdminApplyCancelPlayer.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .playerSignId(contractId)
                .build();
        Result<ResponseAdminApplyCancelPlayer> result = signAdminService.applyCancelPlayer(param);
        if (RpcResult.isFail(result)) {
            log.error("applyCancelPlayer fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(param));
            return ResultVO.failure(MsgCodes.FAIL);
        }

        ResultVO<Void> voidResultVO = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voidResultVO.isOK()) {
            return ResultVO.failure(voidResultVO.getRCode(), voidResultVO.getPrompt());
        }

        return ResultVO.success(new AdminApplyCancelPlayerResult().setContractId(result.target().getContractId()));
    }

    /**
     * 工作台列表
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("todoPlayerList")
    public ResultVO<List<TodoSignPlayerVo>> todoPlayerList(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long njId = ContextUtils.getContext().getSubjectId();
        Result<List<TodoSignPlayerBean>> result = signAdminService.getTodoList(RequestAdminTodoList.builder()
                .appId(appId)
                .njId(njId)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("getAdminTodoList fail. appId={},njId={},rCode={}", appId, njId, result.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }
        return ResultVO.success(SignConvert.I.todoSignPlayerBeans2Vos(result.target()));
    }

    /**
     * 签约解约列表
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("signPlayerList")
    public ResultVO<PageVO<AdminSignPlayerRecordVo>> signPlayerList(@Validated AdminGetSignPlayerListParamVo paramVo){
        //参数检查
        ContractTypeEnum contractType = ContractTypeEnum.from(paramVo.getType());
        if (contractType == null) {
            return ResultVO.failure("不支持该类型");
        }

        SignRelationEnum status = SignRelationEnum.fromCode(paramVo.getStatus());
        if (status == SignRelationEnum.UNKNOWN) {
            return ResultVO.failure("不支持该状态查询");
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long njId = ContextUtils.getContext().getSubjectId();

        RequestQuerySignPlayerList rpcParam = RequestQuerySignPlayerList.builder()
                .appId(appId)
                .njId(njId)
                .type(contractType)
                .status(status)
                .userBand(paramVo.getUserBand())
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .build();

        Result<PageBean<AdminSignPlayerRecordBean>> result = signAdminService.querySignPlayerList(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("admin querySignPlayerList fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }

        List<AdminSignPlayerRecordVo> voList = SignConvert.I.adminSignPlayerRecordBeans2Vos(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList));
    }

    /**
     * 已签约主播数
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("signPlayerNum")
    public ResultVO<Integer> signPlayerNum(){
        Long njId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        Result<Integer> result = signAdminService.countSignPlayerNum(appId, njId);
        if (RpcResult.isFail(result)) {
            log.error("admin countSignPlayerNum fail. appId={},njId={},rCode={}", appId, njId, result.rCode());
            return ResultVO.success(0);
        }

        return ResultVO.success(result.target());
    }

    /**
     * 申请解约（家族长-厅主）
     * 西米和黑叶需要返回合同url, pp不需要
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("/unwind")
    public ResultVO<AdminUnwindResult> unwind(@Validated @RequestBody AdminUnwindParam param){

        RequestAdminApplyCancelFamily request = RequestAdminApplyCancelFamily.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .contractId(param.getContractId())
                .curUserId(ContextUtils.getContext().getUserId())
                .build();

        Result<ResponseAdminApplyCancelFamily> result = signAdminService.applyCancelFamily(request);
        if (RpcResult.isFail(result)) {
            log.error("admin applyCancelFamily fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(request));
            return ResultVO.failure(MsgCodes.FAIL);
        }

        ResultVO<Void> voRes = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voRes.isOK()) {
            return ResultVO.failure(voRes.getRCode(), voRes.getPrompt());
        }

        return ResultVO.success(new AdminUnwindResult()
                .setContractUrl(result.target().getContractUrl())
                .setContractId(result.target().getContractId())
        );
    }

    /**
     * 解约（家族长-厅主）
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("/doUnwind")
    public ResultVO<AdminDoUnwindResult> doUnwind(@Validated @RequestBody AdminDoUnwindParam param){

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long curUserId = ContextUtils.getContext().getUserId();

        RequestAdminDoCancelFamily rpcParam = RequestAdminDoCancelFamily.builder()
                .appId(appId)
                .contractId(param.getContractId())
                .signId(param.getSignId())
                .curUserId(curUserId)
                .build();

        Result<ResponseAdminDoCancelFamily> result = signAdminService.doCancelFamily(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("admin doCancelFamily fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        ResultVO<Void> voRes = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voRes.isOK()) {
            return ResultVO.failure(voRes.getRCode(), voRes.getPrompt());
        }

        return ResultVO.success(new AdminDoUnwindResult()
                .setContractUrl(result.target().getContractUrl()));
    }


}
