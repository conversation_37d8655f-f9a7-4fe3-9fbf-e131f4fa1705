package fm.lizhi.ocean.wavecenter.web.module.permission.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.permissions.service.RoleService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.module.permission.convert.RoleConvert;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.RoleVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统角色管理
 * <AUTHOR>
 * @date 2024/4/11 11:02
 */
@RestController
@RequestMapping("/role")
public class RoleController {

    @Autowired
    private RoleService roleService;

    /**
     * 获取可授权角色列表
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY})
    @GetMapping("authRoles")
    public ResultVO<List<RoleVo>> authRoles(){
        Result<List<RoleBean>> result = roleService.getAuthRoles();
        if (RpcResult.isFail(result)) {
            return ResultVO.failure("获取角色失败");
        }
        return ResultVO.success(RoleConvert.I.beans2Vos(result.target()));
    }

}
