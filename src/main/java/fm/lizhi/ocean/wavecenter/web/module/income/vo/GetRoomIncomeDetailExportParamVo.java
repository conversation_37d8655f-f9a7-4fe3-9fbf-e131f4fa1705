package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetRoomIncomeDetailExportParamVo extends GetDetailBaseParamVo{

    private Long roomId;

    /**
     * 收入类型
     */
    private List<Integer> incomeType;

}
