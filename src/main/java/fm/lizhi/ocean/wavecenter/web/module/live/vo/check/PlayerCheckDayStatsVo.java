package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import fm.lizhi.ocean.wavecenter.web.common.BigDecimalDownSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 陪玩日统计指标
 * <AUTHOR>
 * @date 2024/6/11 15:44
 */
@Data
@Accessors(chain = true)
public class PlayerCheckDayStatsVo {

    /**
     * 天
     */
    private Date time;

    /**
     * 收入
     */
    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal income;

    /**
     * 魅力值
     */
    private Long charm;

    /**
     * 麦序
     */
    private int seatOrder;

    /**
     * 上麦时长
     */
    private int upGuestDur;

    /**
     * 主持档
     */
    private int hostCnt;

}
