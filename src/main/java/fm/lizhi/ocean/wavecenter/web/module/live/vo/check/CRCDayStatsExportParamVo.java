package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/6/11 10:27
 */
@Data
public class CRCDayStatsExportParamVo {

    private Long roomId;

    /**
     * 开始时间 yyyy-MM-dd
     */
    @NotBlank(message = "开始时间不可为空")
    private String startDate;

    /**
     * 结束时间 yyyy-MM-dd
     */
    @NotBlank(message = "结束时间不可为空")
    private String endDate;

    /**
     * 导出范围
     * all=全部，current=当前厅
     */
    private String scope;

}
