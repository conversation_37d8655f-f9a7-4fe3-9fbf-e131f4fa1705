package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:04
 */
@Data
public class CancelPlayerRecordVo {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

    private UserVo roomInfo;

    private UserVo playerInfo;

    /**
     * 发起时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

    private String type;

    /**
     * 状态
     */
    private String status;

}
