package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListAdPositionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListAdPosition;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result.ListAdPositionResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface OfflineZoneAdPositionConvert {

    /**
     * 转换为查询广告展位列表的请求对象
     *
     * @param appId 应用ID
     * @return 查询广告展位列表的请求对象
     */
    @Mapping(target = "appId", source = "appId")
    RequestListAdPosition toRequestListAdPosition(Integer appId);

    /**
     * 将广告展位bean列表转换为web结果列表
     *
     * @param beans 广告展位bean列表
     * @return 广告展位结果列表
     */
    List<ListAdPositionResult> toListAdPositionResults(List<ListAdPositionBean> beans);
}
