package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 主播信息VO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class PlayerInfoVo {

    /**
     * 主播ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 主播名称
     */
    private String name;

    /**
     * 实名证件号码
     */
    private String idCardNumber;

    /**
     * 实名姓名
     */
    private String idName;

    /**
     * 波段号
     */
    private String band;
}
