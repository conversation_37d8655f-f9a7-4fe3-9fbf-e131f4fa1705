package fm.lizhi.ocean.wavecenter.web.module.activitycenter.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 活动等级推荐卡配置结果
 */
@Data
@Accessors(chain = true)
public class ActivityLevelRecCardConfigResult {

    /**
     * 等级ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long levelId;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 推荐卡配置信息
     */
    private RecCardInfo recCardInfo;

    /**
     * 推荐卡配置信息
     */
    @Data
    @Accessors(chain = true)
    public static class RecCardInfo {
        /**
         * 有效天数
         */
        private Integer validDay;
        
        /**
         * 有效次数
         */
        private Integer count;
    }
}
