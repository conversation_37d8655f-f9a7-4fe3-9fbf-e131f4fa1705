package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 活动模板流量资源
 */
@Data
@Accessors(chain = true)
public class ActivityTemplateFlowResourceVO {

    /**
     * 资源配置id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long resourceConfigId;

    private String resourceCode;

    /**
     * 资源名称
     */
    private String name;
}
