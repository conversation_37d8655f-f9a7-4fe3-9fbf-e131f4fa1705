package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 跳槽保护-主播确认请求参数
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class OfflineZoneProtectionPlayerConfirmParam {

    /**
     * 协议ID
     */
    @NotNull(message = "协议ID不能为空")
    private Long protectionId;

    /**
     * 同意状态：0-拒绝，1-同意
     */
    @NotNull(message = "处理状态不能为空")
    private Integer status;
}
