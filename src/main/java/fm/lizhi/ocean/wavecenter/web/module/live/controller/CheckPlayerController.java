package fm.lizhi.ocean.wavecenter.web.module.live.controller;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInStatus;
import fm.lizhi.ocean.wavecenter.api.live.constants.HostStatus;
import fm.lizhi.ocean.wavecenter.api.live.service.LivePlayerCheckInService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.constants.SingStatusEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.file.convert.FileConvert;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.DynamicColTable;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.live.convert.CheckConvert;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.*;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserFamilyHandler;
import fm.lizhi.ocean.wavecenter.web.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;

/**
 * 主播打卡明细
 * <AUTHOR>
 * @date 2024/6/6 18:30
 * @deprecated 已迁移
 * @see CheckInTempController
 */
@Slf4j
@RestController
@RequestMapping("live/check/player")
@Deprecated
public class CheckPlayerController {

    @Autowired
    private DataScopeHandler dataScopeHandler;
    @Autowired
    private FileExportHandler fileExportHandler;
    @Autowired
    private LivePlayerCheckInService playerCheckInService;
    @Autowired
    private UserFamilyHandler userFamilyHandler;

    /**
     * 汇总-小时和天通用
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.PLAYER, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/sum")
    public ResultVO<CPCSumVo> sum(@Validated CPCHourStatsParamVo paramVo){
        if (!dataScopeHandler.checkParamPlayerId(paramVo.getPlayerId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        Long familyId = dataScopeHandler.getFamilyForBase();
        Long roomId = dataScopeHandler.getRoomForRoomOrPlayer();
        Long playerId = dataScopeHandler.getPlayerForBaseOrDefault(paramVo.getPlayerId());
        if (familyId == null) {
            return ResultVO.failure("未查询到签约关系");
        }
        if (playerId == null) {
            return ResultVO.failure("请选择主播");
        }
        if (ContextUtils.getContext().isPlayer() && paramVo.getSignTab() == null){
            return ResultVO.failure("请选择签约类型");
        }

        //特殊逻辑处理
        if (roomId == null && Objects.equals(SingStatusEnum.STOP.getValue(), paramVo.getSignTab())) {
            //如果是查询非签约，需要获取当前陪玩签约厅
            roomId = userFamilyHandler.getPlayerSignRoom(paramVo.getPlayerId());
        }

        PlayerCheckHourStatsReq req = PlayerCheckHourStatsReq.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(familyId)
                .roomId(roomId)
                .playerId(playerId)
                .startDay(dayStart)
                .endDay(dayEnd)
                .singStatus(SingStatusEnum.fromValue(paramVo.getSignTab()))
                .build();
        Result<PlayerCheckStatsSumBean> result = playerCheckInService.sum(req);
        if (RpcResult.isFail(result)) {
            log.warn("playerCheckInService sum fail. req={}, rCode={}", JsonUtil.dumps(req), result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(CheckConvert.I.playerStatsSumBean2Vo(result.target()));
    }

    /**
     * 主播打卡明细-小时统计
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.PLAYER, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/hour/stats")
    public ResultVO<List<PlayerCheckHourStatsDayVo>> hourStats(@Validated CPCHourStatsParamVo paramVo){
        if (!dataScopeHandler.checkParamPlayerId(paramVo.getPlayerId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        Long familyId = dataScopeHandler.getFamilyForBase();
        Long roomId = dataScopeHandler.getRoomForRoomOrPlayer();
        Long playerId = dataScopeHandler.getPlayerForBaseOrDefault(paramVo.getPlayerId());
        if (familyId == null) {
            return ResultVO.failure("未查询到签约关系");
        }
        if (playerId == null) {
            return ResultVO.failure("请选择主播");
        }
        if (ContextUtils.getContext().isPlayer() && paramVo.getSignTab() == null){
            return ResultVO.failure("请选择签约类型");
        }

        //特殊逻辑处理
        if (roomId == null && Objects.equals(SingStatusEnum.STOP.getValue(), paramVo.getSignTab())) {
            //如果是查询非签约，需要获取当前陪玩签约厅
            roomId = userFamilyHandler.getPlayerSignRoom(paramVo.getPlayerId());
        }

        PlayerCheckHourStatsReq req = PlayerCheckHourStatsReq.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(familyId)
                .roomId(roomId)
                .playerId(playerId)
                .startDay(dayStart)
                .endDay(dayEnd)
                .singStatus(SingStatusEnum.fromValue(paramVo.getSignTab()))
                .build();
        Result<List<PlayerCheckHourStatsDayBean>> result = playerCheckInService.hourStats(req);
        if (RpcResult.isFail(result)) {
            log.warn("playerCheckInService hostStats fail. req={}, rCode={}", JsonUtil.dumps(req), result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(CheckConvert.I.playerHourStatsDayBeans2Vos(result.target()));
    }

    /**
     * 主播打卡明细-小时统计-导出
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.PLAYER, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/hour/stats/export")
    public ResultVO<Void> hourStatsExport(@Validated CPCHourStatsParamVo paramVo){
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        Long familyId = dataScopeHandler.getFamilyForBase();
        Long roomId = dataScopeHandler.getRoomForRoomOrPlayer();
        Long playerId = dataScopeHandler.getPlayerForBaseOrDefault(paramVo.getPlayerId());
        if (familyId == null) {
            return ResultVO.failure("未查询到签约关系");
        }
        if (playerId == null) {
            return ResultVO.failure("请选择主播");
        }
        if (ContextUtils.getContext().isPlayer() && paramVo.getSignTab() == null){
            return ResultVO.failure("请选择签约类型");
        }

        //特殊逻辑处理
        if (roomId == null && Objects.equals(SingStatusEnum.STOP.getValue(), paramVo.getSignTab())) {
            //如果是查询非签约，需要获取当前陪玩签约厅
            roomId = userFamilyHandler.getPlayerSignRoom(paramVo.getPlayerId());
        }

        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.putCol("档期");
        dynamicColTable.putCol("数据类型");

        Long finalRoomId = roomId;
        return fileExportHandler.exportDynamicFile("主播打卡明细_小时统计", dynamicColTable, (sheet, pageNo, pageSize) -> {

            PlayerCheckHourStatsReq req = PlayerCheckHourStatsReq.builder()
                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                    .familyId(familyId)
                    .roomId(finalRoomId)
                    .playerId(playerId)
                    .startDay(dayStart)
                    .endDay(dayEnd)
                    .singStatus(SingStatusEnum.fromValue(paramVo.getSignTab()))
                    .build();
            Result<List<PlayerCheckHourStatsDayBean>> result = playerCheckInService.hourStats(req);
            if (RpcResult.isFail(result)) {
                log.warn("playerCheckInService hostStats fail. req={}, rCode={}", JsonUtil.dumps(req), result.rCode());
                return PageVO.empty();
            }
            List<PlayerCheckHourStatsDayBean> beanList = result.target();

            //档期开始时间-日期-值
            Table<Date, String, String> incomeTable = HashBasedTable.create();
            Table<Date, String, String> charmTable = HashBasedTable.create();
            Table<Date, String, String> remarkTable = HashBasedTable.create();

            //日合计
            Map<Date, BigDecimal> incomeSumMap = new HashMap<>();
            Map<Date, Long> charmSumMap = new HashMap<>();
            Map<Date, Integer> seatOrderSumMap = new HashMap<>();
            Map<Date, Integer> hostCntSumMap = new HashMap<>();

            for (PlayerCheckHourStatsDayBean bean : beanList) {
                Date day = bean.getDay();
                String dayStr = DateUtil.formatDate(day);
                List<PlayerCheckHourStatsBean> details = bean.getDetail();
                BigDecimal incomeSum = BigDecimal.ZERO;
                Long charmSum = 0L;
                Integer seatOrderSum = 0;
                Integer hostCntSum = 0;
                for (PlayerCheckHourStatsBean d : details) {
                    incomeTable.put(d.getStartTime(), dayStr, d.getIncome() == null ? "0":CalculateUtil.formatDecimal(d.getIncome()));
                    charmTable.put(d.getStartTime(), dayStr, d.getCharmValue() == null ? "0":String.valueOf(d.getCharmValue()));
                    remarkTable.put(d.getStartTime(), dayStr, d.getRemark() == null ? "":d.getRemark());

                    if (d.getIncome() != null) {
                        incomeSum = incomeSum.add(d.getIncome());
                    }
                    if (d.getCharm() != null) {
                        charmSum += d.getCharmValue();
                    }
                    if (!Objects.equals(CheckInStatus.UN_CHECKED.getValue(), d.getCheckInStatus())) {
                        seatOrderSum++;
                    }
                    if (Objects.equals(HostStatus.HOST.getValue(), d.getIsHost())) {
                        hostCntSum++;
                    }
                }
                incomeSumMap.put(day, incomeSum);
                charmSumMap.put(day, charmSum);
                seatOrderSumMap.put(day, seatOrderSum);
                hostCntSumMap.put(day, hostCntSum);
            }

            List<DynamicColTable.Row<Date>> statsList = new ArrayList<>();
            if (paramVo.getMetrics().contains(MetricsEnum.INCOME.getValue())) {
                statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(incomeTable, "收入"));
            }
            if (paramVo.getMetrics().contains(MetricsEnum.CHARM.getValue())) {
                statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(charmTable, "魅力值"));
            }
            statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(remarkTable, "备注"));

            //按照时间字段排序
            statsList.sort(Comparator.comparing(DynamicColTable.Row::getSortRow));

            List<DynamicColTable.Row<Date>> resultList = new ArrayList<>();
            DynamicColTable.Row<Date> incomeSum = new DynamicColTable.Row<>();
            incomeSum.putFreezeCol("合计");
            incomeSum.putFreezeCol("收入");
            for (Map.Entry<Date, BigDecimal> entry : incomeSumMap.entrySet()) {
                incomeSum.putCol(DateUtil.formatDate(entry.getKey()), CalculateUtil.formatDecimal(entry.getValue()));
            }
            resultList.add(incomeSum);

            DynamicColTable.Row<Date> charmSum = new DynamicColTable.Row<>();
            charmSum.putFreezeCol("合计");
            charmSum.putFreezeCol("魅力值");
            for (Map.Entry<Date, Long> entry : charmSumMap.entrySet()) {
                charmSum.putCol(DateUtil.formatDate(entry.getKey()), String.valueOf(entry.getValue()));
            }
            resultList.add(charmSum);

            DynamicColTable.Row<Date> seatOrderSum = new DynamicColTable.Row<>();
            seatOrderSum.putFreezeCol("合计");
            seatOrderSum.putFreezeCol("麦序");
            for (Map.Entry<Date, Integer> entry : seatOrderSumMap.entrySet()) {
                seatOrderSum.putCol(DateUtil.formatDate(entry.getKey()), String.valueOf(entry.getValue()));
            }
            resultList.add(seatOrderSum);

            DynamicColTable.Row<Date> hostCntSum = new DynamicColTable.Row<>();
            hostCntSum.putFreezeCol("合计");
            hostCntSum.putFreezeCol("主持档");
            for (Map.Entry<Date, Integer> entry : hostCntSumMap.entrySet()) {
                hostCntSum.putCol(DateUtil.formatDate(entry.getKey()), String.valueOf(entry.getValue()));
            }
            resultList.add(hostCntSum);
            resultList.addAll(statsList);

            return PageVO.of(0, resultList);
        });
    }

    /**
     * 主播打卡明细-日统计-列表查询
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.PLAYER, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/day/stats")
    public ResultVO<PageVO<PlayerCheckDayStatsVo>> dayStats(@Validated CPCDayStatsParamVo paramVo){
        if (!dataScopeHandler.checkParamPlayerId(paramVo.getPlayerId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        Long familyId = dataScopeHandler.getFamilyForBase();
        //家族长访问时，room为空
        Long roomId = dataScopeHandler.getRoomForRoomOrPlayer();
        Long playerId = dataScopeHandler.getPlayerForBaseOrDefault(paramVo.getPlayerId());
        if (familyId == null) {
            return ResultVO.failure("未查询到签约关系");
        }
        if (playerId == null) {
            return ResultVO.failure("请选择主播");
        }
        if (ContextUtils.getContext().isPlayer() && paramVo.getSignTab() == null){
            return ResultVO.failure("请选择签约类型");
        }

        //特殊逻辑处理
        if (roomId == null && Objects.equals(SingStatusEnum.STOP.getValue(), paramVo.getSignTab())) {
            //如果是查询非签约，需要获取当前陪玩签约厅
            roomId = userFamilyHandler.getPlayerSignRoom(paramVo.getPlayerId());
        }

        PlayerCheckDayStatsReq req = PlayerCheckDayStatsReq.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(familyId)
                .roomId(roomId)
                .playerId(playerId)
                .startDay(dayStart)
                .endDay(dayEnd)
                .singStatus(SingStatusEnum.fromValue(paramVo.getSignTab()))
                .build();
        Result<List<PlayerCheckDayStatsBean>> result = playerCheckInService.dayStats(req);
        if (RpcResult.isFail(result)) {
            log.warn("playerCheckInService dayStats fail. rCode={}, req={}", result.rCode(), JsonUtil.dumps(req));
            return ResultVO.failure();
        }
        List<PlayerCheckDayStatsBean> beanLit = result.target();
        if (CollectionUtils.isEmpty(beanLit)) {
            return ResultVO.success(PageVO.empty());
        }

        List<PlayerCheckDayStatsVo> voList = CheckConvert.I.playerDayStatsBeans2Vos(beanLit);
        return ResultVO.success(PageVO.of(voList.size(), voList));
    }

    /**
     * 主播打卡明细-日统计-导出
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.PLAYER, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/day/stats/export")
    public ResultVO<Void> dayStatsExport(@Validated CPCDayStatsParamVo paramVo){
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        Long familyId = dataScopeHandler.getFamilyForBase();
        Long roomId = dataScopeHandler.getRoomForRoomOrPlayer();
        Long playerId = dataScopeHandler.getPlayerForBaseOrDefault(paramVo.getPlayerId());
        if (familyId == null) {
            return ResultVO.failure("未查询到签约关系");
        }
        if (playerId == null) {
            return ResultVO.failure("请选择主播");
        }

        if (ContextUtils.getContext().isPlayer() && paramVo.getSignTab() == null){
            return ResultVO.failure("请选择签约类型");
        }

        //特殊逻辑处理
        if (roomId == null && Objects.equals(SingStatusEnum.STOP.getValue(), paramVo.getSignTab())) {
            //如果是查询非签约，需要获取当前陪玩签约厅
            roomId = userFamilyHandler.getPlayerSignRoom(paramVo.getPlayerId());
        }

        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.putCol("日期");

        Long finalRoomId = roomId;
        return fileExportHandler.exportDynamicFile("主播打卡明细_日统计", dynamicColTable, (sheet, pageNo, pageSize)->{
            PlayerCheckDayStatsReq req = PlayerCheckDayStatsReq.builder()
                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                    .familyId(familyId)
                    .roomId(finalRoomId)
                    .playerId(playerId)
                    .startDay(dayStart)
                    .endDay(dayEnd)
                    .singStatus(SingStatusEnum.fromValue(paramVo.getSignTab()))
                    .build();
            Result<List<PlayerCheckDayStatsBean>> result = playerCheckInService.dayStats(req);
            if (RpcResult.isFail(result)) {
                log.warn("playerCheckInService dayStats fail. rCode={}, req={}", result.rCode(), JsonUtil.dumps(req));
                return PageVO.empty();
            }
            List<PlayerCheckDayStatsBean> beanLit = result.target();

            List<DynamicColTable.Row<Date>> statsList = new ArrayList<>();
            for (PlayerCheckDayStatsBean bean : beanLit) {
                Date day = bean.getDay();
                String dayStr = DateUtil.formatDate(day);

                DynamicColTable.Row<Date> row = new DynamicColTable.Row<>();
                row.setSortRow(day);
                row.putFreezeCol(dayStr);

                if (paramVo.getMetrics().contains(MetricsEnum.INCOME.getValue())) {
                    row.putCol(MetricsEnum.INCOME.getName(), CheckConvert.I.convertIncome(bean.getIncome()));
                }
                if (paramVo.getMetrics().contains(MetricsEnum.CHARM.getValue())) {
                    row.putCol(MetricsEnum.CHARM.getName(), String.valueOf(bean.getCharm()));
                }
                if (paramVo.getMetrics().contains(MetricsEnum.SEAT_ORDER.getValue())) {
                    row.putCol(MetricsEnum.SEAT_ORDER.getName(), String.valueOf(bean.getSeatOrder()));
                }
                if (paramVo.getMetrics().contains(MetricsEnum.HOST_CNT.getValue())) {
                    row.putCol(MetricsEnum.HOST_CNT.getName(), String.valueOf(bean.getHostCnt()));
                }
                if (paramVo.getMetrics().contains(MetricsEnum.UP_GUEST_DUR.getValue())) {
                    row.putCol(MetricsEnum.UP_GUEST_DUR.getName(), String.valueOf(bean.getUpGuestDur()));
                }
                statsList.add(row);
            }
            statsList.sort(Comparator.comparing(DynamicColTable.Row::getSortRow));
            return PageVO.of(0, statsList);
        });
    }

}
