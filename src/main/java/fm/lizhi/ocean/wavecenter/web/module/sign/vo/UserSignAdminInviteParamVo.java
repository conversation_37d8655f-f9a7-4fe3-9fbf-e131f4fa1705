package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/10/5 16:03
 */
@Data
public class UserSignAdminInviteParamVo {

    /**
     * 签约记录ID
     */
    @NotNull(message = "签约记录ID")
    private Long contractId;

    /**
     * AGREE = 同意
     * REJECT = 拒绝
     */
    @NotBlank(message = "意见不可为空")
    private String status;

}
