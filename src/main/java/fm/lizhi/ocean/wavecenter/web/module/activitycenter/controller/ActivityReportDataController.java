package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityReportDataService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityReportDataConvert;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/activity/report")
@Slf4j
public class ActivityReportDataController {

    @Autowired
    private ActivityReportDataService activityReportDataService;

    @Autowired
    private FileExportHandler fileExportHandler;

    /**
     * 活动数据-数据汇总
     */
    @GetMapping("/summary")
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.PLAYER})
    public ResultVO<ActivityReportDataSummaryVO> getReportSummary(@RequestParam("activityId") Long activityId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<ActivityReportDataSummaryBean> result = activityReportDataService.getReportSummary(activityId, appId);

        if (RpcResult.isFail(result)) {
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success(ActivityReportDataConvert.I.toActivityReportDataSummaryVO(result.target()));
    }

    /**
     * 活动数据-数据明细（趋势图）
     */
    @GetMapping("/detail")
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.PLAYER})
    public ResultVO<List<ActivityReportDataDetailVO>> getReportDetail(@RequestParam("activityId") Long activityId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<List<ActivityReportDataDetailBean>> result = activityReportDataService.getReportDetail(activityId, appId);
        if (RpcResult.isFail(result)) {
            return ResultVO.failure(result.getMessage());
        }

        return ResultVO.success(ActivityReportDataConvert.I.toActivityReportDataDetailVO(result.target()));
    }



    /**
     * 活动数据-主播表现
     */
    @GetMapping("/player")
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.PLAYER})
    public ResultVO<PageVO<ActivityReportDataPlayerVO>> pageReportPlayer(@RequestParam("activityId") Long activityId,
                                                                         @RequestParam(required = false, name = "pageNo", defaultValue = "1") int pageNo,
                                                                         @RequestParam(required = false, name = "pageSize", defaultValue = "20")  int pageSize) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<PageBean<ActivityReportDataPlayerBean>> result = activityReportDataService.pageReportPlayer(activityId, appId, pageNo, pageSize);
        if (RpcResult.isFail(result)) {
            return ResultVO.failure(result.getMessage());
        }


        return ResultVO.success(PageVO.of(result.target().getTotal(), ActivityReportDataConvert.I.toActivityReportDataPlayerVO(result.target().getList())));
    }



    /**
     * 活动数据-用户送礼明细
     */
    @GetMapping("/giftDetail")
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.PLAYER})
    public ResultVO<PageVO<ActivityReportDataGiftVO>> pageReportGift(@RequestParam("activityId") Long activityId,
                                                                     @RequestParam(required = false, name = "pageNo", defaultValue = "1") int pageNo,
                                                                     @RequestParam(required = false, name = "pageSize", defaultValue = "20")  int pageSize) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<PageBean<ActivityReportDataGiftBean>> result = activityReportDataService.pageReportGift(activityId, appId, pageNo, pageSize);

        if (RpcResult.isFail(result)) {
            return ResultVO.failure(result.getMessage());
        }

        return ResultVO.success(PageVO.of(result.target().getTotal(), ActivityReportDataConvert.I.toActivityReportDataGiftVO(result.target().getList())));

    }

    /**
     * 活动数据-主播表现-导出
     */
    @GetMapping("/player/export")
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.PLAYER})
    public ResultVO<Void> exportReportPlayer(@RequestParam("activityId") Long activityId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String fileName = "活动数据_主播表现_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, ActivityReportDataPlayerExportVo.class,
                (pageNo, pageSize) -> {
                    Result<PageBean<ActivityReportDataPlayerBean>> result = activityReportDataService.pageReportPlayer(activityId, appId, pageNo, pageSize);
                    if (RpcResult.isFail(result)) {
                        log.error("exportReportPlayer error, activityId={}, appId={}, pageNo={}, pageSize={}, error={}",
                                activityId, appId, pageNo, pageSize, result.getMessage());
                        return PageVO.empty();
                    }

                    PageBean<ActivityReportDataPlayerBean> target = result.target();
                    return PageVO.of(target.getTotal(), ActivityReportDataConvert.I.convertActivityReportDataPlayerExportVos(target).getList());
                });
    }

    /**
     * 活动数据-用户送礼明细-导出
     */
    @GetMapping("/giftDetail/export")
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.PLAYER})
    public ResultVO<Void> exportReportGift(@RequestParam("activityId") Long activityId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String fileName = "活动数据_用户送礼详情_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, ActivityReportDataGiftExportVo.class,
                (pageNo, pageSize) -> {
                    Result<PageBean<ActivityReportDataGiftBean>> result = activityReportDataService.pageReportGift(activityId, appId, pageNo, pageSize);
                    if (RpcResult.isFail(result)) {
                        log.error("exportReportGift error, activityId={}, appId={}, pageNo={}, pageSize={}, error={}",
                                activityId, appId, pageNo, pageSize, result.getMessage());
                        return PageVO.empty();
                    }
                    PageBean<ActivityReportDataGiftBean> target = result.target();
                    return PageVO.of(target.getTotal(), ActivityReportDataConvert.I.convertActivityReportDataGiftExportVos(target).getList());
                });
    }
}
