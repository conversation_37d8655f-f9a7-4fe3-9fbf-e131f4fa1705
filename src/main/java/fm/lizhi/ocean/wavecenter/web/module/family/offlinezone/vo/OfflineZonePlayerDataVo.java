package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 离线区域主播数据VO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Accessors(chain = true)
public class OfflineZonePlayerDataVo {

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 周开始日期（时间戳）
     */
    private Long startWeekDate;

    /**
     * 周结束日期（时间戳）
     */
    private Long endWeekDate;

    /**
     * 主播ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 主播名称
     */
    private String userName;

    /**
     * 主播信息
     */
    private UserVo playerInfo;

    /**
     * 实名证件号码
     */
    private String idCardNumber;

    /**
     * 实名姓名
     */
    private String idName;

    /**
     * 公会ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 厅主ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long njId;

    /**
     * 厅主名称
     */
    private String njName;

    /**
     * 厅主信息
     */
    private UserVo njInfo;

    /**
     * 签约时间（时间戳）
     */
    private Long beginSignTime;

    /**
     * 主播分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 主播收入
     */
    private BigDecimal income;


    /**
     * 保护协议ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long protectionId;

    /**
     * 是否受跳槽保护
     */
    private Boolean protection;

    /**
     * 保护状态描述
     */
    private Integer protectionStatus;

    /**
     * 创建时间（时间戳）
     */
    private Long createTime;

    /**
     * 更新时间（时间戳）
     */
    private Long modifyTime;
}
