package fm.lizhi.ocean.wavecenter.web.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 泛型结果VO
 *
 * @param <T> 结果数据泛型
 */
@Data
public class ResultVO<T> {

    /**
     * 状态码
     */
    @NotNull
    @Getter(onMethod_ = {@JsonProperty("rCode")})
    private Integer rCode;

    /**
     * 提示Prompt
     */
    private Prompt prompt;

    /**
     * 结果数据
     */
    private T data;

    @Data
    @Accessors(chain = true)
    public static class Prompt {
        /**
         * 提示类型: 0toast，1弹窗，2Action
         */
        private Integer type;
        /**
         * 提示消息
         */
        private String msg;
    }

    public enum PromptType {
        TOAST(0),
        ALERT(1),
        /** 前端不会弹窗 */
        WARN(2);

        int type;

        PromptType(int type) {
            this.type = type;
        }

        public int getType() {
            return type;
        }
    }


    public ResultVO() {
        // 保留默认构造方法，json序列化用到
    }

    public ResultVO(Integer rCode, Prompt prompt, T data) {
        this.rCode = rCode;
        this.prompt = prompt;
        this.data = data;
    }

    public ResultVO(MsgCodes msgCode, T data) {
        this(msgCode.getCode(), new Prompt().setType(PromptType.TOAST.type).setMsg(msgCode.getMsg()), data);
    }

    public static <T> ResultVO<T> success() {
        return new ResultVO<>(MsgCodes.SUCCESS, null);
    }

    public static <T> ResultVO<T> success(T data) {
        return new ResultVO<>(MsgCodes.SUCCESS, data);
    }

    public static <T> ResultVO<T> failure(String msg) {
        Prompt prompt = new Prompt().setType(PromptType.TOAST.type).setMsg(msg);
        return new ResultVO<>(MsgCodes.FAIL.getCode(), prompt, null);
    }

    public static <T> ResultVO<T> paramError(String msg) {
        Prompt prompt = new Prompt().setType(PromptType.TOAST.type).setMsg(msg);
        return new ResultVO<>(MsgCodes.PARAM_ERROR.getCode(), prompt, null);
    }

    public static <T> ResultVO<T> warn(String msg) {
        Prompt prompt = new Prompt().setType(PromptType.WARN.type).setMsg(msg);
        return new ResultVO<>(MsgCodes.FAIL.getCode(), prompt, null);
    }

    public static <T> ResultVO<T> warn(IRCodes irCodes) {
        Prompt prompt = new Prompt().setType(PromptType.WARN.type).setMsg(irCodes.getMsg());
        return new ResultVO<>(irCodes.getCode(), prompt, null);
    }

    public static <T> ResultVO<T> failure(Integer rCode, String msg) {
        Prompt prompt = new Prompt().setType(PromptType.TOAST.type).setMsg(msg);
        return new ResultVO<>(rCode, prompt, null);
    }

    public static <T> ResultVO<T> failure(Integer rCode, Prompt prompt) {
        return new ResultVO<>(rCode, prompt, null);
    }

    public static <T> ResultVO<T> failure(IRCodes irCodes) {
        Prompt prompt = new Prompt().setType(PromptType.TOAST.type).setMsg(irCodes.getMsg());
        return new ResultVO<>(irCodes.getCode(), prompt, null);
    }

    public static <T> ResultVO<T> alertFailure(Integer rCode, String msg) {
        Prompt prompt = new Prompt().setType(PromptType.ALERT.type).setMsg(msg);
        return new ResultVO<>(rCode, prompt, null);
    }

    public static <T> ResultVO<T> failure(Integer rCode, String msg, T data) {
        Prompt prompt = new Prompt().setType(PromptType.TOAST.type).setMsg(msg);
        return new ResultVO<>(rCode, prompt, data);
    }

    public static <T> ResultVO<T> failure(MsgCodes msgCodes) {
        Prompt prompt = new Prompt().setType(PromptType.TOAST.type).setMsg(msgCodes.getMsg());
        return new ResultVO<>(msgCodes.getCode(), prompt, null);
    }

    public static <T> ResultVO<T> failure() {
        Prompt prompt = new Prompt().setType(PromptType.TOAST.type).setMsg(MsgCodes.FAIL.getMsg());
        return new ResultVO<>(MsgCodes.FAIL.getCode(), prompt, null);
    }

    public static <T> ResultVO<T> warn() {
        Prompt prompt = new Prompt().setType(PromptType.WARN.type).setMsg(MsgCodes.FAIL.getMsg());
        return new ResultVO<>(MsgCodes.FAIL.getCode(), prompt, null);
    }

    @JsonIgnore
    public boolean isOK() {
        return this.getRCode() == 0;
    }
}
