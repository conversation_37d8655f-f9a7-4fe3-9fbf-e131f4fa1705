package fm.lizhi.ocean.wavecenter.web.module.file.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.FileExportRecordBean;
import fm.lizhi.ocean.wavecenter.api.file.service.FileExportRecordService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.util.WorkbookUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Component
@Slf4j
public class FileExportHandler {

    @Autowired
    private FileExportRecordService fileExportRecordService;
    @Autowired
    private AsyncFileHandler asyncFileHandler;
    @Autowired
    private AppConfig appConfig;

    /**
     * 文件导出
     * @param fileName
     * @param head
     * @param dataQuery
     * @return
     * @param <T>
     */
    public <T> ResultVO<Void> exportFile(String fileName, Class<T> head, DataQuery<T> dataQuery){
        return exportFile(fileName, head, dataQuery, null);
    }

    // 修改后的方法签名（新增 includeFields 参数）
    public <T> ResultVO<Void> exportFile(String fileName, Class<T> head, DataQuery<T> dataQuery, Set<String> includeFields) {
        Optional<Long> recordIdOp = createFileTask(fileName);
        if (!recordIdOp.isPresent()) {
            return ResultVO.failure();
        }
        Long recordId = recordIdOp.get();
        asyncFileHandler.asyncExport(recordId, fileName, head, excelWriter -> {
            int pageNo = 1;
            int pageSize = appConfig.getExportPageSize();
            boolean lastPage = false;
            int writeDataCnt = 0;
            int sheetSize = appConfig.getExportSheetSize();

            Map<Integer, WriteSheet> sheetMap = new HashMap<>();
            while (!lastPage) {
                PageVO<T> pageVo = dataQuery.pageList(pageNo, pageSize);
                List<T> list = pageVo.getList();
                writeDataCnt += list.size();
                int sheetNo = writeDataCnt / sheetSize;
                WriteSheet writeSheet = sheetMap.get(sheetNo);
                if (writeSheet == null) {
                    ExcelWriterSheetBuilder builder = EasyExcel.writerSheet(sheetNo);
                    // 动态设置包含的字段
                    if (CollUtil.isNotEmpty(includeFields)) {
                        builder.includeColumnFieldNames(includeFields);
                    }
                    writeSheet = builder.build();
                    sheetMap.put(sheetNo, writeSheet);
                }
                excelWriter.write(list, writeSheet);
                if (pageNo * pageSize >= pageVo.getTotal()) {
                    lastPage = true;
                }
                pageNo++;
            }
        });
        return ResultVO.success();
    }

    /**
     * 文件导出-自动生成文件名
     * @param fileName
     * @param head
     * @param dataQuery
     * @return
     * @param <T>
     */
    public <T> ResultVO<Void> exportFile2(String fileName, Class<T> head, DataQuery<T> dataQuery){
        String name = genFileName(fileName);
        return exportFile(name, head, dataQuery);
    }

    /**
     * 时间统计文件导出
     * @param fileName
     * @param dataQuery
     * @return
     */
    public <T> ResultVO<Void> exportDynamicFile(String fileName, DynamicColTable dynamicColTable, SheetDataQuery<DynamicColTable.Row<T>> dataQuery){
        String name = genFileName(fileName);
        Optional<Long> recordIdOp = createFileTask(name);
        if (!recordIdOp.isPresent()) {
            return ResultVO.failure();
        }
        Long recordId = recordIdOp.get();
        asyncFileHandler.asyncExportDiy(recordId, name, excelWriterBuilder -> {
            ExcelWriter excelWriter = excelWriterBuilder.build();

            List<DynamicColTable.Sheet> sheets = dynamicColTable.getSheets();
            for (DynamicColTable.Sheet sheet : sheets) {
                int pageNo = 1;
                int pageSize = appConfig.getExportPageSize();
                boolean lastPage = false;

                List<String> heads = new ArrayList<>();
                Map<Long, WriteSheet> sheetMap = new HashMap<>();
                while (!lastPage) {
                    //分页查询
                    PageVO<DynamicColTable.Row<T>> pageVo = dataQuery.getList(sheet, pageNo, pageSize);
                    int total = pageVo.getTotal();
                    List<DynamicColTable.Row<T>> list = pageVo.getList();

                    //确认表头
                    if (pageNo == 1) {
                        heads.addAll(dynamicColTable.getFreezeCol());
                        if (CollectionUtils.isEmpty(list)) {
                            break;
                        }
                        DynamicColTable.Row row = list.get(0);
                        heads.addAll(row.getColNameList());
                    }

                    //创建excel的sheet对象
                    WriteSheet writeSheet = sheetMap.get(sheet.getId());
                    if (writeSheet == null) {
                        //sheetName有限制，这里转换一下，但是不影响sheet的数据
                        String safeSheetName = WorkbookUtil.createSafeSheetName(sheet.getName());
                        writeSheet = EasyExcel.writerSheet(safeSheetName).head(buildHeadList(heads)).build();
                        sheetMap.put(sheet.getId(), writeSheet);
                    }

                    //转换数据结构
                    List<List<String>> dataList = new ArrayList<>();
                    for (DynamicColTable.Row t : list) {
                        List<String> rowData = new ArrayList<>(t.getFreezeCol());
                        LinkedHashMap<String, String> valueMap = t.getColValueMap();
                        for (String userHead : heads) {
                            if (dynamicColTable.getFreezeCol().contains(userHead)) {
                                //过滤一下冻结列
                                continue;
                            }
                            rowData.add(valueMap.getOrDefault(userHead, ""));
                        }
                        dataList.add(rowData);
                    }

                    //数据写入
                    excelWriter.write(dataList, writeSheet);

                    if (pageNo * pageSize >= total) {
                        log.info("exportFile fileName={},pageNo={},pageSize={}, total={}", fileName, pageNo, pageSize, total);
                        lastPage = true;
                    }
                    pageNo++;
                }
            }
            excelWriter.finish();
        });
        return ResultVO.success();
    }

    /**
     * 生成文件名-拼接上时间
     * @param fileName
     * @return
     */
    public String genFileName(String fileName) {
        return fileName + "_"+ DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
    }

    /**
     * 创建文件下载任务
     * @param fileName
     * @return
     */
    private Optional<Long> createFileTask(String fileName){
        int appId = ContextUtils.getContext().getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        Result<FileExportRecordBean> result = fileExportRecordService.createTask(appId, userId, fileName);
        if (RpcResult.isFail(result)) {
            log.warn("createFileTask fail appId={}, userId={}, fileName={}, rCode={}", appId, userId, fileName, result.rCode());
            return Optional.empty();
        }
        return Optional.ofNullable(result.target().getId());
    }

    /**
     * 封装表头结构体
     * @param list
     * @return
     */
    private static List<List<String>> buildHeadList(List<String> list){
        return list.stream().map(head -> {
            List<String> h = new ArrayList<>(1);
            h.add(head);
            return h;
        }).collect(Collectors.toList());
    }

    /**
     * 数据查询接口
     * @param <T>
     */
    @FunctionalInterface
    public interface DataQuery<T>{
        PageVO<T> pageList(int pageNo, int pageSize);
    }

    @FunctionalInterface
    public interface SheetDataQuery<T>{
        PageVO<T> getList(DynamicColTable.Sheet sheet, int pageNo, int pageSize);
    }
}
