package fm.lizhi.ocean.wavecenter.web.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.commons.rome.push.api.RomePushService;
import fm.lizhi.ocean.wave.platform.api.platform.service.WaveCheckInManagementService;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.*;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyAwardService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityClassificationConfigService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityDecorateService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityRecommendCardConfigService;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonConfigService;
import fm.lizhi.ocean.wavecenter.api.common.service.FeedbackService;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.GuildDataService;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.PlayerDataService;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.RankDataService;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.RoomDataService;
import fm.lizhi.ocean.wavecenter.api.file.service.FileExportRecordService;
import fm.lizhi.ocean.wavecenter.api.gift.service.GiftService;
import fm.lizhi.ocean.wavecenter.api.grow.ability.service.AbilityPerformanceService;
import fm.lizhi.ocean.wavecenter.api.grow.level.service.FamilyLevelConfigService;
import fm.lizhi.ocean.wavecenter.api.grow.level.service.FamilyLevelService;
import fm.lizhi.ocean.wavecenter.api.home.service.GuildHomeService;
import fm.lizhi.ocean.wavecenter.api.home.service.QuickEntryService;
import fm.lizhi.ocean.wavecenter.api.home.service.RoomHomeService;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomeGuildService;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomePlayerService;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomeRoomService;
import fm.lizhi.ocean.wavecenter.api.live.service.*;
import fm.lizhi.ocean.wavecenter.api.message.service.WaveCenterMessageService;
import fm.lizhi.ocean.wavecenter.api.message.service.WcNoticeConfigService;
import fm.lizhi.ocean.wavecenter.api.permissions.service.RoleService;
import fm.lizhi.ocean.wavecenter.api.permissions.service.UserPermissionService;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.service.RecommendCardService;
import fm.lizhi.ocean.wavecenter.api.sign.service.*;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.api.user.service.UserLoginService;
import fm.lizhi.ocean.wavecenter.api.user.service.UserRoomService;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/3/25 20:13
 */
@Configuration
public class ServiceProvider {

    private final int timeout = 15000;
    private final int connections = 100;

    @Bean
    public UserPermissionService userPermissionService() {
        return new DubboClientBuilder<>(UserPermissionService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public UserFamilyService userFamilyService() {
        return new DubboClientBuilder<>(UserFamilyService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public UserCommonService userCommonService() {
        return new DubboClientBuilder<>(UserCommonService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public RoleService roleService() {
        return new DubboClientBuilder<>(RoleService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public UserLoginService userLoginService() {
        return new DubboClientBuilder<>(UserLoginService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public GuildDataService guildDataService() {
        return new DubboClientBuilder<>(GuildDataService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public RoomDataService roomDataService() {
        return new DubboClientBuilder<>(RoomDataService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public CommonConfigService commonConfigService() {
        return new DubboClientBuilder<>(CommonConfigService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public LiveSmsService liveSmsService() {
        return new DubboClientBuilder<>(LiveSmsService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public PlayerDataService playerDataService() {
        return new DubboClientBuilder<>(PlayerDataService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public FileExportRecordService fileExportRecordService() {
        return new DubboClientBuilder<>(FileExportRecordService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public RankDataService rankDataService() {
        return new DubboClientBuilder<>(RankDataService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }


    @Bean
    public IncomeGuildService incomeGuildService() {
        return new DubboClientBuilder<>(IncomeGuildService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public IncomeRoomService incomeRoomService() {
        return new DubboClientBuilder<>(IncomeRoomService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public IncomePlayerService incomePlayerService() {
        return new DubboClientBuilder<>(IncomePlayerService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    @ConditionalOnMissingBean
    public RomePushService romePushService() {
        return new DubboClientBuilder<>(RomePushService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }


    @Bean
    public LiveAuditService liveAuditService() {
        return new DubboClientBuilder<>(LiveAuditService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }


    @Bean
    public UserRoomService UserRoomService() {
        return new DubboClientBuilder<>(UserRoomService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public LiveGuildCheckInService liveGuildCheckInService() {
        return new DubboClientBuilder<>(LiveGuildCheckInService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public LivePlayerCheckInService livePlayerCheckInService() {
        return new DubboClientBuilder<>(LivePlayerCheckInService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }


    @Bean
    public LiveRoomCheckInService liveRoomCheckInService() {
        return new DubboClientBuilder<>(LiveRoomCheckInService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public GuildManageService guildManageService() {
        return new DubboClientBuilder<>(GuildManageService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public FeedbackService feedbackService() {
        return new DubboClientBuilder<>(FeedbackService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public LiveService liveService() {
        return new DubboClientBuilder<>(LiveService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public QueryFlowService queryFlowService(){
        return new DubboClientBuilder<>(QueryFlowService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public OperateFlowService operateFlowService(){
        return new DubboClientBuilder<>(OperateFlowService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public SignUserService signUserService(){
        return new DubboClientBuilder<>(SignUserService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public SignAdminService signAdminService() {
        return new DubboClientBuilder<>(SignAdminService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public SignPlayerService signPlayerService(){
        return new DubboClientBuilder<>(SignPlayerService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public SignFamilyService signFamilyService(){
        return new DubboClientBuilder<>(SignFamilyService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public SignCommonService signCommonService(){
        return new DubboClientBuilder<>(SignCommonService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public WaveCenterMessageService waveCenterMessageService(){
        return new DubboClientBuilder<>(WaveCenterMessageService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityApplyService activityApplyService() {
        return new DubboClientBuilder<>(ActivityApplyService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityClassificationConfigService activityClassificationConfigService() {
        return new DubboClientBuilder<>(ActivityClassificationConfigService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityClassificationService activityClassificationService() {
        return new DubboClientBuilder<>(ActivityClassificationService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityReportDataService activityReportDataService() {
        return new DubboClientBuilder<>(ActivityReportDataService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityOfficialSeatTimeService activityOfficialSeatTimeService() {
        return new DubboClientBuilder<>(ActivityOfficialSeatTimeService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityDecorateService activityDecorateService() {
        return new DubboClientBuilder<>(ActivityDecorateService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityTemplateService activityTemplateService() {
        return new DubboClientBuilder<>(ActivityTemplateService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityCommonService activityCommonService() {
        return new DubboClientBuilder<>(ActivityCommonService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public GuildHomeService guildHomeService() {
        return new DubboClientBuilder<>(GuildHomeService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public RoomHomeService roomHomeService() {
        return new DubboClientBuilder<>(RoomHomeService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public QuickEntryService quickEntryService() {
        return new DubboClientBuilder<>(QuickEntryService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public WaveCheckInManagementService waveCheckInManagementService() {
        return new DubboClientBuilder<>(WaveCheckInManagementService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public WaveCheckInDataService waveCheckInDataService() {
        return new DubboClientBuilder<>(WaveCheckInDataService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }
    @Bean
    public ActivityOperateService activityOperateService() {
        return new DubboClientBuilder<>(ActivityOperateService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityNoticeService activityNoticeService() {
        return new DubboClientBuilder<>(ActivityNoticeService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityLevelService activityLevelService() {
        return new DubboClientBuilder<>(ActivityLevelService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public FamilyLevelService familyLevelService() {
        return new DubboClientBuilder<>(FamilyLevelService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public FamilyLevelConfigService familyLevelConfigService() {
        return new DubboClientBuilder<>(FamilyLevelConfigService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public OfflineZoneDataMonitorService offlineZoneDataMonitorService() {
        return new DubboClientBuilder<>(OfflineZoneDataMonitorService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public OfflineZoneRoomDataService offlineZoneRoomDataService() {
        return new DubboClientBuilder<>(OfflineZoneRoomDataService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public OfflineZonePlayerDataService offlineZonePlayerDataService() {
        return new DubboClientBuilder<>(OfflineZonePlayerDataService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public OfflineZoneProtectionService offlineZoneProtectionService() {
        return new DubboClientBuilder<>(OfflineZoneProtectionService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public OfflineZoneLearningClassService offlineZoneLearningClassService() {
        return new DubboClientBuilder<>(OfflineZoneLearningClassService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public OfflineZoneLevelRightService offlineZoneLevelRightService() {
        return new DubboClientBuilder<>(OfflineZoneLevelRightService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public OfflineZoneLevelConfigService offlineZoneLevelConfigService() {
        return new DubboClientBuilder<>(OfflineZoneLevelConfigService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public OfflineZoneAdPositionService offlineZoneAdPositionService() {
        return new DubboClientBuilder<>(OfflineZoneAdPositionService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public RecommendCardService recommendCardService() {
        return new DubboClientBuilder<>(RecommendCardService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public FamilyAwardService familyAwardService() {
        return new DubboClientBuilder<>(FamilyAwardService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityResourceTimeService activityResourceTimeService() {
        return new DubboClientBuilder<>(ActivityResourceTimeService.class)
               .connections(connections)
               .timeoutInMillis(timeout)
               .build();
    }

    @Bean
    public WcNoticeConfigService wcNoticeConfigService() {
        return new DubboClientBuilder<>(WcNoticeConfigService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public AbilityPerformanceService abilityPerformanceService() {
        return new DubboClientBuilder<>(AbilityPerformanceService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public ActivityRecommendCardConfigService activityRecommendCardConfigService() {
        return new DubboClientBuilder<>(ActivityRecommendCardConfigService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }

    @Bean
    public GiftService giftService() {
        return new DubboClientBuilder<>(GiftService.class)
                .connections(connections)
                .timeoutInMillis(timeout)
                .build();
    }
}
