package fm.lizhi.ocean.wavecenter.web.module.user.controller;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.QueryGuildPlayerBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestGetAllGuildRoomsV2;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestSearchUser;
import fm.lizhi.ocean.wavecenter.api.user.response.ResponseSearchUser;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10 11:21
 */
@RestController
@RequestMapping("/user/common")
public class UserCommonController {

    private static final Logger log = LoggerFactory.getLogger(UserCommonController.class);
    @Autowired
    private UserCommonService userCommonService;

    /**
     * 查询厅下所有主播
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @GetMapping("/room/players")
    public ResultVO<PageVO<PlayerSignVo>> getRoomPlayer(@Validated GetRoomPlayerParamVo paramVo){
        //访问的用户ID就是厅主ID
        long userId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        //查询DC
        Result<PageBean<PlayerSignBean>> result = userCommonService.getAllRoomPlayers(appId, userId, paramVo.getPageNo(), paramVo.getPageSize());
        if (RpcResult.isFail(result)) {
            return ResultVO.failure("获取数据失败");
        }

        //结果封装
        List<PlayerSignBean> beanList = result.target().getList();
        int total = result.target().getTotal();
        List<PlayerSignVo> voList = UserCommonConvert.I.playerSignBeans2Vos(beanList);
        return ResultVO.success(PageVO.of(total, voList));
    }

    /**
     * 获取公会下所有厅, 包括解约数据
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/guild/rooms")
    public ResultVO<PageVO<RoomSignVo>> getGuildRooms(@Validated GetGuildRoomsParamVo paramVo){
        //查询登录用户的家族ID
        Integer appId = ContextUtils.getBusinessEvnEnum().appId();
        Long familyId = ContextUtils.getContext().getSubjectId();

        Result<PageBean<RoomSignBean>> result = userCommonService.getAllGuildRoomsV2(new RequestGetAllGuildRoomsV2()
                .setAppId(appId)
                .setFamilyId(familyId)
                .setRoomIds(ContextUtils.getContext().getRoomResource())
                .setPageNo(paramVo.getPageNo())
                .setPageSize(paramVo.getPageSize())
        );
        if (RpcResult.isFail(result)) {
            log.error("getAllGuildRoomsV2 fail. rCode={}", result.rCode());
            return ResultVO.failure("获取数据失败");
        }

        //结果转换
        List<RoomSignBean> list = result.target().getList();
        int total = result.target().getTotal();
        List<RoomSignVo> voList = UserCommonConvert.I.roomSignBeans2Vos(list);
        return ResultVO.success(PageVO.of(total, voList));
    }

    /**
     * 获取公会下所有主播信息，包含解约+签约 数据
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/guild/players")
    public ResultVO<PageVO<PlayerSignVo>> getGuildPlayer(@Validated GetGuildPlayerParamVo paramVo){
        //查询登录用户的家族ID
        Integer appId = ContextUtils.getBusinessEvnEnum().appId();
        Long familyId = ContextUtils.getContext().getSubjectId();
        QueryGuildPlayerBean guildPlayerBean = QueryGuildPlayerBean.builder()
                .appId(appId)
                .familyId(familyId)
                .status(paramVo.getStatus())
                .roomIds(ContextUtils.getContext().getRoomResource())
                .build();
        Result<PageBean<PlayerSignBean>> result = userCommonService.getAllGuildPlayer(guildPlayerBean, paramVo.getPageNo(), paramVo.getPageSize());
        if (RpcResult.isFail(result)) {
            return ResultVO.failure("获取数据失败");
        }
        //结果封装
        List<PlayerSignBean> beanList = result.target().getList();
        int total = result.target().getTotal();
        List<PlayerSignVo> voList = UserCommonConvert.I.playerSignBeans2Vos(beanList);
        return ResultVO.success(PageVO.of(total, voList));
    }

    /**
     * 搜索用户
     * @param param
     * @return
     */
    @VerifyUserToken
    @GetMapping("searchUser")
    public ResultVO<SearchUserResultVo> searchUser(SearchUserParamVo param){
        if (StringUtils.isBlank(param.getBand())) {
            return ResultVO.failure(MsgCodes.PARAM_ERROR);
        }

        Result<ResponseSearchUser> result = userCommonService.searchUser(RequestSearchUser.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .band(param.getBand())
                .build());
        if (result.rCode() == UserCommonService.USER_NOT_FOUND || result.rCode() == UserCommonService.USER_IS_UGC) {
            return ResultVO.success();
        }

        if (RpcResult.isFail(result)) {
            log.warn("searchUser fail. param={},rCode={}", JsonUtil.dumps(param), result.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }

        return ResultVO.success(UserCommonConvert.I.searchUserResultBean2Vo(result.target()));
    }

}
