package fm.lizhi.ocean.wavecenter.web.module.sign.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignFlowStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.service.OperateFlowService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/10/9 10:58
 */
@Slf4j
@RestController
@RequestMapping("/sign/operate/flow")
public class OperateFlowController {

    @Autowired
    private OperateFlowService operateFlowService;

    /**
     * 修改流程状态
     * @param flowId 流程ID
     * @param status 状态 CLOSED:已关闭
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER, RoleEnum.USER}, onlySelfLogin = true)
    @PostMapping("changeStatus/{flowId}/{status}")
    public ResultVO<Void> changeStatus(@PathVariable("flowId")Long flowId, @PathVariable("status") String status){
        SignFlowStatusEnum statusEnum = SignFlowStatusEnum.form(status);
        if (statusEnum == null) {
            return ResultVO.failure("状态错误");
        }
        Result<Void> result = operateFlowService.changeStatus(ContextUtils.getBusinessEvnEnum().getAppId(), flowId, statusEnum);
        if (RpcResult.isFail(result)) {
            log.error("changeStatus fail. flowId={},status={},rCode={}", flowId, status, result.rCode());
            return ResultVO.failure("状态变更错误");
        }
        return ResultVO.success();
    }

}
