package fm.lizhi.ocean.wavecenter.web.module.home.convert;

import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomMsgAnalysisPerformance;
import fm.lizhi.ocean.wavecenter.web.module.home.result.RoomKeyDataSummaryResult;
import fm.lizhi.ocean.wavecenter.web.module.home.result.RoomKeyDataTrendChartResult;
import fm.lizhi.ocean.wavecenter.web.module.home.result.RoomMsgAnalysisPerformanceResult;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RoomHomeConvert {

    RoomHomeConvert I = Mappers.getMapper(RoomHomeConvert.class);


    RoomKeyDataSummaryResult toRoomKeyDataSummaryResult(ResponseRoomKeyDataSummary target);

    List<RoomKeyDataTrendChartResult> toResponseRoomKeyDataTrendCharts(List<ResponseRoomKeyDataTrendChart> target);

    RoomMsgAnalysisPerformanceResult toRoomMsgAnalysisPerformanceResult(ResponseRoomMsgAnalysisPerformance target);

}
