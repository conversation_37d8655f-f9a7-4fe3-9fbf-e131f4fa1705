package fm.lizhi.ocean.wavecenter.web.module.live.consumer;
import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import fm.lizhi.common.kafka.ioc.spring.EnableKafkaClients;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Slf4j
@Configuration
@EnableKafkaClients(basePackages = "fm.lizhi.ocean.wavecenter.web.module.live.consumer")
public class AuditKafkaClientConfiguration {


    @Bean("publicKafkaTemplate")
    @ConditionalOnMissingBean(name = "publicKafkaTemplate")
    public KafkaTemplate publicKafkaTemplate() {
        return new KafkaTemplate("public-kafka250-bootstrap-server");
    }


}
