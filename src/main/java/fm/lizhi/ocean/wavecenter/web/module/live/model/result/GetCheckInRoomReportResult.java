package fm.lizhi.ocean.wavecenter.web.module.live.model.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserRecordSumBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserReportSumBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserSumBean;
import lombok.Data;

import java.util.List;

/**
 * 获取打卡报告结果
 */
@Data
public class GetCheckInRoomReportResult {

    /**
     * 麦序福利厅明细统计列表
     */
    private List<ReportData> list;

    /**
     * 房间信息
     */
    private User roomInfo;

    @Data
    public static class ReportData {

        /**
         * 麦序福利主播信息
         */
        private User player;

        /**
         * 打卡汇总
         */
        private WaveCheckInUserReportSumBean sum;
    }

    @Data
    public static class User {

        /**
         * id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 昵称
         */
        private String name;

        /**
         * 波段号
         */
        private String band;

        /**
         * 头像
         */
        private String photo;
    }
}
