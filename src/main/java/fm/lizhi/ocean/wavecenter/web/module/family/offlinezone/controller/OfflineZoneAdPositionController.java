package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListAdPositionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListAdPosition;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneAdPositionService;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert.OfflineZoneAdPositionConvert;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result.ListAdPositionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 线下专区广告展位控制器
 */
@RestController
@RequestMapping("/offline/adPosition")
@Slf4j
public class OfflineZoneAdPositionController {

    @Autowired
    private OfflineZoneAdPositionConvert offlineZoneAdPositionConvert;

    @Autowired
    private OfflineZoneAdPositionService offlineZoneAdPositionService;

    /**
     * 查询广告展位列表
     *
     * @return 广告展位列表结果
     */
    @VerifyUserToken
    @GetMapping("/list")
    public ResultVO<List<ListAdPositionResult>> listAdPosition() {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        RequestListAdPosition request = offlineZoneAdPositionConvert.toRequestListAdPosition(appId);
        Result<List<ListAdPositionBean>> result = offlineZoneAdPositionService.listAdPosition(request);
        if (RpcResult.isFail(result)) {
            int rCode = result.rCode();
            if (rCode == CommonService.PARAM_ERROR) {
                String message = StringUtils.defaultIfBlank(result.getMessage(), MsgCodes.PARAM_ERROR.getMsg());
                log.info("listAdPosition param invalid, request={}, rCode={}, message={}", request, rCode, message);
                return ResultVO.failure(MsgCodes.PARAM_ERROR.getCode(), message);
            } else {
                log.info("listAdPosition fail, request={}, rCode={}, message={}", request, rCode, result.getMessage());
                return ResultVO.failure(MsgCodes.FAIL);
            }
        }
        List<ListAdPositionBean> beans = result.target();
        List<ListAdPositionResult> results = offlineZoneAdPositionConvert.toListAdPositionResults(beans);
        log.debug("listAdPosition success, request={}, results={}", request, results);
        return ResultVO.success(results);
    }
}
