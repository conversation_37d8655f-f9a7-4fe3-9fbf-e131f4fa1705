package fm.lizhi.ocean.wavecenter.web.module.live.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import fm.lizhi.ocean.wavecenter.web.common.RateSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 厅私信分析数据
 * <AUTHOR>
 * @date 2024/4/20 15:41
 */
@Data
public class RoomSmsStatVo {

    private UserVo roomInfo;

    /**
     * 厅签约主播数 roomCnt
     */
    private Integer signPlayerCnt;

    /**
     * 私信用户数-私信人数  chatCnt
     */
    private Integer chatUserCnt;

    /**
     * 私信回复人数  replyChatCnt
     */
    private Integer replyChatUserCnt;

    /**
     * 私信回复率
     */
    @JsonSerialize(using = RateSerializer.class)
    private BigDecimal replyChatRate;

    /**
     * 私信进房人数 chatEnterRoomCnt
     */
    private Integer chatEnterRoomUserCnt;

    /**
     * 私信进房率
     */
    @JsonSerialize(using = RateSerializer.class)
    private BigDecimal chatEnterRoomRate;

    /**
     * 邀请人数 inviteCnt
     */
    private Integer inviteUserCnt;

    /**
     * 邀请进房人数 inviteEnterRoomCnt
     */
    private Integer inviteEnterRoomUserCnt;

    /**
     * 邀请付费人数 inviteGiftCnt
     */
    private Integer inviteGiftUserCnt;

    /**
     * 邀请进房率
     */
    @JsonSerialize(using = RateSerializer.class)
    private BigDecimal inviteEnterRoomRate;

    /**
     * 邀请付费率
     */
    @JsonSerialize(using = RateSerializer.class)
    private BigDecimal inviteGiftRate;

    /**
     * 私信付费人数-私信送礼人数 chatGiftCnt
     */
    private Integer chatGiftUserCnt;


    /**
     * 私信付费率-私信送礼率
     */
    @JsonSerialize(using = RateSerializer.class)
    private BigDecimal chatGiftRate;

    /**
     * 私信主播数
     */
    private Integer chatPlayerCnt;


}
