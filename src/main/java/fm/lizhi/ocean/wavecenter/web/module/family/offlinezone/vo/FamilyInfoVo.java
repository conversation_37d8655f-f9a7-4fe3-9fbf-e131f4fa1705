package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 公会信息VO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class FamilyInfoVo {


    /**
     * 认证类型：1=认证通过，2=未认证
     */
    private Integer authType;

    /**
     * 认证企业
     */
    private String authCompany;

    /**
     * 家族长波段号
     */
    private String familyUserBand;

    /**
     * 家族长名称
     */
    private String familyUserName;

    /**
     * 公会头像url
     */
    private String familyPhotoUrl;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 公会ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 公会名称
     */
    private String name;
}
