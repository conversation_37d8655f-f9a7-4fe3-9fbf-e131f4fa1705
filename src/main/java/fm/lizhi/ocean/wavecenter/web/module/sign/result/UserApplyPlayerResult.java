package fm.lizhi.ocean.wavecenter.web.module.sign.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/12/25 14:36
 */
@Data
@Accessors(chain = true)
public class UserApplyPlayerResult {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

}
