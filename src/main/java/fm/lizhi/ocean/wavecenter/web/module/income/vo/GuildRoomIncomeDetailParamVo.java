package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/4/27 20:24
 */
@Data
public class GuildRoomIncomeDetailParamVo {

    /**
     * 开始时间
     * yyyy-MM-dd
     */
    @NotBlank(message = "开始时间为空")
    private String startDate;

    @NotBlank(message = "结束时间为空")
    private String endDate;

    private Integer pageNo = 1;

    private Integer pageSize = 10;

    private Long roomId;

    private Long flushTime = System.currentTimeMillis();

}
