package fm.lizhi.ocean.wavecenter.web.module.sign.convert;

import fm.lizhi.ocean.wavecenter.api.sign.bean.GuildFullInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.SignPlayerInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponsePlayerHallInfo;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInfoBean;
import fm.lizhi.ocean.wavecenter.api.user.constants.SingStatusEnum;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.GuildFullInfoVo;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.GuildSignRoomVo;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.PlayerHallInfoVO;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.SignPlayerExcelVo;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.SignPlayerVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserInfoVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13 20:10
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface GuildManageConvert {

    GuildManageConvert I = Mappers.getMapper(GuildManageConvert.class);

    GuildSignRoomVo roomSignInfoBean2Vo(RoomSignInfoBean bean);

    List<GuildSignRoomVo> roomSignInfoBeans2Vos(List<RoomSignInfoBean> beans);

    @Mappings({
            @Mapping(source = "settle", target = "settle", qualifiedByName = "formatSettle")
    })
    SignPlayerVo signPlayerInfoBean2Vo(SignPlayerInfoBean bean);

    List<SignPlayerVo> signPlayerInfoBeans2Vos(List<SignPlayerInfoBean> beans);

    UserVo userBean2Vo(UserBean bean);

    UserInfoVo userInfoBean2Vo(UserInfoBean bean);

    @Named("formatSettle")
    default String formatSettle(Integer settle){
        if (settle == null) {
            return null;
        }
        return settle+"%";
    }

    GuildFullInfoVo fullInfoBean2Vo(GuildFullInfoBean bean);

    PlayerHallInfoVO playerHallInfoResp2Vo(ResponsePlayerHallInfo resp);

    @Named("formatSignStatus")
    default String formatSignStatus(Integer signStatus) {
        if (signStatus == null) {
            return "未知状态";
        }
        SingStatusEnum status = SingStatusEnum.fromValue(signStatus);
        if (status == null) {
            return "未知状态";
        }
        return status == SingStatusEnum.SING ? "签约中" : "已解约";
    }

    @Mappings({
        @Mapping(source = "roomInfo.band", target = "roomUserId"),
        @Mapping(source = "roomInfo.name", target = "roomUserName"),
        @Mapping(source = "playerInfo.band", target = "njId"),
        @Mapping(source = "playerInfo.name", target = "njName"),
        @Mapping(source = "signStatus", target = "signStatus", qualifiedByName = "formatSignStatus"),
        @Mapping(source = "signDate", target = "signDate")
    })
    SignPlayerExcelVo signPlayerInfoBean2ExcelVo(SignPlayerInfoBean bean);

    List<SignPlayerExcelVo> signPlayerInfoBeans2ExcelVos(List<SignPlayerInfoBean> list);

}
