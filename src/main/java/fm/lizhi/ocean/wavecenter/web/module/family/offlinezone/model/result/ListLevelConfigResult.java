package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result;

import com.ctrip.framework.apollo.core.enums.Env;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.common.serializer.ListLongToListStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * 等级配置列表结果
 */
@Data
public class ListLevelConfigResult {

    /**
     * 等级ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 等级key, 用于前端做UI关联
     */
    private String levelKey;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 等级顺序（数字越高等级越高）
     */
    private Integer levelOrder;

    /**
     * 部署环境
     *
     * @see Env#name()
     */
    private String deployEnv;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 创建时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * 修改时间, 毫秒时间戳
     */
    private Long modifyTime;

    /**
     * 等级权益ID列表
     */
    @JsonSerialize(using = ListLongToListStringSerializer.class)
    private List<Long> rightIds;
}
