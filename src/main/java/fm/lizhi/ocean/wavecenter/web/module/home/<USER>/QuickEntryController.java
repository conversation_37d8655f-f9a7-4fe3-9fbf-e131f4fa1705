package fm.lizhi.ocean.wavecenter.web.module.home.controller;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.home.bean.QuickEntryBean;
import fm.lizhi.ocean.wavecenter.api.home.service.QuickEntryService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.home.vo.QuickEntryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 快捷入口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/home")
@Slf4j
public class QuickEntryController {

    @Autowired
    private QuickEntryService quickEntryService;


    /**
     * 快捷入口
     */
    @GetMapping("/quickEntry")
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    public ResultVO<List<QuickEntryVO>> quickEntry() {
        String roleCode = ContextUtils.getContext().getRoleCode();
        if (StrUtil.isEmpty(roleCode)){
            log.error("quick entry is fail. roleCode is null.");
            return ResultVO.failure();
        }

        if (ContextUtils.getContext().isFamilyAdmin()) {
            roleCode = RoleEnum.FAMILY.getRoleCode();
        }

        Result<List<QuickEntryBean>> result = quickEntryService.getQuickEntryList(roleCode);
        if (RpcResult.isFail(result)){
            log.error("quick entry is fail. roleCode is {}. rCode:{}", roleCode, result.rCode());
            return ResultVO.failure();
        }

        return ResultVO.success(result.target().stream().map(
                e -> new QuickEntryVO().setLinkType(e.getLinkType())
        ).collect(Collectors.toList()));
    }

}
