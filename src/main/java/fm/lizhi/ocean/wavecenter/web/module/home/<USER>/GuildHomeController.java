package fm.lizhi.ocean.wavecenter.web.module.home.controller;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildMarketMonitorSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildMarketMonitorTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildMarketMonitorSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildMarketMonitorTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.service.GuildHomeService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.home.convert.GuildHomeConvert;
import fm.lizhi.ocean.wavecenter.web.module.home.result.GuildKeyDataSummaryResult;
import fm.lizhi.ocean.wavecenter.web.module.home.result.GuildKeyDataTrendChartResult;
import fm.lizhi.ocean.wavecenter.web.module.home.result.GuildMarketMonitorSummaryResult;
import fm.lizhi.ocean.wavecenter.web.module.home.result.GuildMarketMonitorTrendChartResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公会首页
 * <AUTHOR>
 */
@RestController
@RequestMapping("/home/<USER>")
@Slf4j
public class GuildHomeController {

    @Autowired
    private GuildHomeService guildHomeService;


    /**
     * 获取公会关键数据汇总
     */
    @GetMapping("/keyData/summary")
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    public ResultVO<GuildKeyDataSummaryResult> getGuildKeyDataSummary(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {

        Long familyId = ContextUtils.getContext().getSubjectId();
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        List<Long> roomResource = ContextUtils.getContext().getRoomResource();
        log.info("getGuildKeyDataSummary appId={}, familyId={}, roomResource={}", appId, familyId, JsonUtil.dumps(roomResource));

        RequestGetGuildKeyDataSummary request = new RequestGetGuildKeyDataSummary()
                .setFamilyId(familyId)
                .setUserId(userId)
                .setAppId(appId)
                .setRoomIds(roomResource)
                .setStartDate(startTime)
                .setEndDate(endTime);
        Result<ResponseGuildKeyDataSummary> result = guildHomeService.getKeyDataSummary(request);

        if (RpcResult.isFail(result)) {
            log.error("getGuildKeyDataSummary fail, appId={}, familyId={}, rCode={}", appId, familyId, result.rCode());
            return ResultVO.failure();
        }

        return ResultVO.success(GuildHomeConvert.I.toGuildKeyDataSummaryResult(result.target()));
    }


    /**
     * 获取关键数据趋势图
     * @return
     */
    @GetMapping("/keyData/trendChart")
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    public ResultVO<List<GuildKeyDataTrendChartResult>> getKeyDataTrendChart(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {

        Long familyId = ContextUtils.getContext().getSubjectId();
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        List<Long> roomResource = ContextUtils.getContext().getRoomResource();
        log.info("getKeyDataTrendChart appId={}, familyId={}, roomResource={}", appId, familyId, JsonUtil.dumps(roomResource));

        RequestGetGuildKeyDataTrendChart request = new RequestGetGuildKeyDataTrendChart()
              .setFamilyId(familyId)
              .setUserId(userId)
              .setAppId(appId)
              .setRoomIds(roomResource)
              .setStartDate(startTime)
              .setEndDate(endTime);

        Result<List<ResponseGuildKeyDataTrendChart>> result = guildHomeService.getKeyDataTrendChart(request);
        if (RpcResult.isFail(result)) {
            log.error("getKeyDataTrendChart fail, appId={}, familyId={}, rCode={}", appId, familyId, result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(GuildHomeConvert.I.toGuildKeyDataTrendChartResult(result.target()));
    }


    /**
     * 公会大盘监控
     */
    @GetMapping("/marketMonitor/summary")
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    public ResultVO<GuildMarketMonitorSummaryResult> getGuildMarketMonitorSummary(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        Long familyId = ContextUtils.getContext().getSubjectId();
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        List<Long> roomResource = ContextUtils.getContext().getRoomResource();
        log.info("getGuildMarketMonitorSummary appId={}, familyId={}, roomResource={}", appId, familyId, JsonUtil.dumps(roomResource));

        RequestGetGuildMarketMonitorSummary request = new RequestGetGuildMarketMonitorSummary()
                .setFamilyId(familyId)
                .setUserId(userId)
                .setAppId(appId)
                .setRoomIds(roomResource)
                .setStartDate(startTime)
                .setEndDate(endTime);

        Result<ResponseGuildMarketMonitorSummary> result = guildHomeService.getMarketMonitorSummary(request);
        if (RpcResult.isFail(result)) {
            log.error("getGuildMarketMonitorSummary fail, appId={}, familyId={}, rCode={}", appId, familyId, result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(GuildHomeConvert.I.toGuildMarketMonitorSummaryResult(result.target()));

    }


    /**
     * 公会大盘趋势
     */
    @GetMapping("/marketMonitor/trendChart")
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    public ResultVO<List<GuildMarketMonitorTrendChartResult>> getGuildMarketMonitorTrendChart(@RequestParam("startTime") String startTime,
                                                                                              @RequestParam("endTime") String endTime,
                                                                                              @RequestParam("type") int type) {
        Long familyId = ContextUtils.getContext().getSubjectId();
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        List<Long> roomResource = ContextUtils.getContext().getRoomResource();
        log.info("getGuildMarketMonitorTrendChart appId={}, familyId={}, roomResource={}", appId, familyId, JsonUtil.dumps(roomResource));

        RequestGetGuildMarketMonitorTrendChart request = new RequestGetGuildMarketMonitorTrendChart()
                .setAppId(appId)
                .setFamilyId(familyId)
                .setUserId(userId)
                .setStartDate(startTime)
                .setEndDate(endTime)
                .setType(type)
                .setRoomIds(roomResource)
                ;

        Result<List<ResponseGuildMarketMonitorTrendChart>> result = guildHomeService.getMarketMonitorTrendChart(request);
        if (RpcResult.isFail(result)) {
            log.error("getGuildMarketMonitorTrendChart fail, appId={}, familyId={}, rCode={}", appId, familyId, result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(GuildHomeConvert.I.toGuildMarketMonitorTrendChartResults(result.target()));

    }


}
