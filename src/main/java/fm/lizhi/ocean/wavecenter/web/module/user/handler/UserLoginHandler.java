package fm.lizhi.ocean.wavecenter.web.module.user.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.GetUserPermissionBean;
import fm.lizhi.ocean.wavecenter.api.permissions.service.UserPermissionService;
import fm.lizhi.ocean.wavecenter.api.user.bean.LoginRoleInfoBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserRoleInfoBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.api.user.service.UserLoginService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.util.WcAssert;
import fm.lizhi.ocean.wavecenter.web.exception.AssertException;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.PermissionInfoVo;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.LoginUserInfoVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserRoleInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/18 12:41
 */
@Slf4j
@Component
public class UserLoginHandler {

    @Autowired
    private UserCommonService userCommonService;
    @Autowired
    private UserPermissionService userPermissionService;
    @Autowired
    private UserLoginService userLoginService;
    @Autowired
    private UserFamilyHandler userFamilyHandler;

    public LoginUserInfoVo getLoginUserInfo(int appId, long userId, String accessToken) {
        Result<UserRoleInfoBean> userRoleInfoRes = userCommonService.getUserRoleInfo(appId, userId);
        WcAssert.isTrue(RpcResult.isSuccess(userRoleInfoRes), MsgCodes.USER_NOT_FOUND);
        UserRoleInfoBean roleInfoBean = userRoleInfoRes.target();
        String deviceId = ContextUtils.getContext().getHeader().getDeviceId();

        Result<GetUserPermissionBean> permissionRes = userPermissionService.getUserPermission(appId, userId, deviceId);
        WcAssert.isTrue(RpcResult.isSuccess(permissionRes), MsgCodes.USER_NOT_FOUND);

        GetUserPermissionBean bean = permissionRes.target();
        PermissionInfoVo permissionInfoVo = new PermissionInfoVo();
        permissionInfoVo.setMenu(bean.getMenu());
        permissionInfoVo.setWriteComponents(bean.getWriteComponents());
        permissionInfoVo.setReadComponents(bean.getReadComponents());

        UserRoleInfoVo userInfo = UserCommonConvert.I.userRoleInfoBean2Vo(roleInfoBean);
        Optional<Long> userFamilyLevel = userFamilyHandler.getUserFamilyLevel(userId);
        if (userFamilyLevel.isPresent()) {
            userInfo.setFamilyLevelId(userFamilyLevel.get());
        }

        LoginUserInfoVo loginUserInfoVo = new LoginUserInfoVo();
        loginUserInfoVo.setUserInfo(userInfo);
        loginUserInfoVo.setPermissionInfo(permissionInfoVo);

        if (StringUtils.isNotBlank(accessToken)) {
            Result<LoginRoleInfoBean> response = userLoginService.getUserLoginRoleInfo(accessToken);
            int rCode = response.rCode();
            if (rCode == UserLoginService.ROLE_AUTH_NOT_EXIST) {
                throw new AssertException(MsgCodes.CHOSE_DATA_SCOPE);
            }
            WcAssert.isTrue(RpcResult.isSuccess(response), MsgCodes.ACCESS_TOKEN_EXPIRED);
            LoginRoleInfoBean loginRoleInfo = response.target();
            loginUserInfoVo.setLoginType(loginRoleInfo.getLoginType());
            loginUserInfoVo.setSubjectUserInfo(UserCommonConvert.I.loginRoleInfoBean2Vo(loginRoleInfo));
        }

        log.info("getLoginUserInfo: {}", JsonUtil.dumps(loginUserInfoVo));
        return loginUserInfoVo;
    }

}
