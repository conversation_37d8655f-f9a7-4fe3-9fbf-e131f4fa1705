package fm.lizhi.ocean.wavecenter.web.module.file;


import java.io.*;

public class FileUtils {

    private static final String DIC = "./temp";


    public static File inputStreamToFile(InputStream inputStream, String FileName) throws IOException {
        File myFilePath = new File(DIC);
        if (!myFilePath.exists()) {
            myFilePath.mkdir();
        }
        File tempFile = new File(DIC, FileName);
        OutputStream outputStream = new FileOutputStream(tempFile, true);
        FileInputStream fis = null;
        try {
            byte buffer[] = new byte[1024];
            int len = 0;
            while ((len = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
            return tempFile;
        } catch (Exception e) {
            e.getStackTrace();
            throw e;
        } finally {
            outputStream.close();
            if (inputStream != null) {
                inputStream.close();
            }
        }

    }
}
