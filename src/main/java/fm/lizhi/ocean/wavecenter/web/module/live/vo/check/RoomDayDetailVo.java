package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class RoomDayDetailVo implements IDetailEle{

    /**
     * 日的值 yy-mm-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date time;


    private String income;

    private int charm;

    /**
     * 麦序
     */
    private int seatOrder;

    /**
     * 主持档
     */
    private Integer hostCnt;

    @Override
    public Date foundTime() {
        return this.time;
    }

    @Override
    public String foundIncome() {
        return this.income;
    }

    @Override
    public Integer foundCharm() {
        return this.charm;
    }

    @Override
    public Integer foundSeatOrder() {
        return this.seatOrder;
    }

    @Override
    public Integer foundHostCnt() {
        return this.hostCnt;
    }

    @Override
    public Integer foundCheckPlayerNumber() {
        return null;
    }
}
