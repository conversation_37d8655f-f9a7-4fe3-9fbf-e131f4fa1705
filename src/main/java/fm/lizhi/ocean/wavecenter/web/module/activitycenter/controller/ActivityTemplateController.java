package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateHotPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageHotActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityTemplateService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityTemplateConverter;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.PageGeneralActivityTemplateParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.PageHotActivityTemplateParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.GetGeneralActivityTemplateResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.PageGeneralActivityTemplateResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.PageHotActivityTemplateResult;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 活动模板控制器
 */
@RestController
@RequestMapping("/activity/template")
@Slf4j
public class ActivityTemplateController {

    @Autowired
    private ActivityTemplateConverter activityTemplateConverter;

    @Autowired
    private ActivityTemplateService activityTemplateService;

    @Autowired
    private DataScopeHandler dataScopeHandler;

    /**
     * 分页查询热门活动模板
     *
     * @param param 请求参数
     * @return 响应结果
     */
    @GetMapping("/pageHot")
    @VerifyUserToken
    public ResultVO<PageVO<PageHotActivityTemplateResult>> pageHot(PageHotActivityTemplateParam param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long userId = dataScopeHandler.getRoomOrPlayer();
        log.info("page hot activity template, param: {}, appId: {}", param, appId);

        RequestPageHotActivityTemplate req = activityTemplateConverter
                .toRequestPageHotActivityTemplate(param, appId, userId);
        Result<PageBean<ActivityTemplateHotPageBean>> result = activityTemplateService.pageHotTemplate(req);
        if (RpcResult.isFail(result)) {
            log.warn("pageHotTemplate fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        PageVO<PageHotActivityTemplateResult> pageVO = activityTemplateConverter
                .toPageVOPageHotActivityTemplateResult(result.target());
        return ResultVO.success(pageVO);
    }

    /**
     * 分页查询通用活动模板
     *
     * @param param 请求参数
     * @return 响应结果
     */
    @GetMapping("/page")
    @VerifyUserToken
    public ResultVO<PageVO<PageGeneralActivityTemplateResult>> page(PageGeneralActivityTemplateParam param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long userId = dataScopeHandler.getRoomOrPlayer();
        log.info("page general activity template, param: {}, appId: {}", param, appId);
        RequestPageGeneralActivityTemplate req = activityTemplateConverter
                .toRequestPageGeneralActivityTemplate(param, appId, userId);
        Result<PageBean<ActivityTemplateGeneralPageBean>> result = activityTemplateService.pageGeneralTemplate(req);
        if (RpcResult.isFail(result)) {
            log.warn("pageGeneralTemplate fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        PageVO<PageGeneralActivityTemplateResult> pageVO = activityTemplateConverter
                .toPageVOPageGeneralActivityTemplateResult(result.target());
        return ResultVO.success(pageVO);
    }

    /**
     * 获取通用活动模板详情
     *
     * @param id 模板id
     * @return 响应结果
     */
    @GetMapping("/get")
    @VerifyUserToken
    public ResultVO<GetGeneralActivityTemplateResult> get(@RequestParam long id) {
        Result<ResponseGetGeneralActivityTemplate> result = activityTemplateService.getGeneralTemplate(id);
        if (RpcResult.isFail(result)) {
            log.warn("getGeneralTemplate fail, id: {}, rCode: {}, message: {}", id, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        GetGeneralActivityTemplateResult templateResult = activityTemplateConverter
                .toGetGeneralActivityTemplateResult(result.target());
        return ResultVO.success(templateResult);
    }
}
