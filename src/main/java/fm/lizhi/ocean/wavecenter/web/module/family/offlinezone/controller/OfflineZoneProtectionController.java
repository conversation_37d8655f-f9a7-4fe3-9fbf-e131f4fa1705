package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetProtectionSupportInfo;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestPlayerHandleAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestSubmitAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionDetailResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionSupportInfoResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneProtectionService;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert.OfflineZoneConvert;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param.OfflineZoneProtectionPlayerConfirmParam;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param.OfflineZoneProtectionSubmitParam;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param.OfflineZoneProtectionSupportInfoParam;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.OfflineZoneProtectionDetailVo;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.OfflineZoneProtectionSupportInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneProtectionService.GET_SUPPORT_INFO_PLAYER_NOT_FOUND;

/**
 * 离线区域跳槽保护Controller
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@RestController
@RequestMapping("offline/protection")
public class OfflineZoneProtectionController {

    @Autowired
    private OfflineZoneProtectionService offlineZoneProtectionService;
    @Autowired
    private OfflineZoneConvert offlineZoneConvert;

    /**
     * 跳槽保护-主播认证协议支撑信息
     * @param param 请求参数
     * @return 支撑信息
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN, RoleEnum.PLAYER})
    @VerifyUserToken
    @GetMapping("supportInfo")
    public ResultVO<OfflineZoneProtectionSupportInfoVo> supportInfo(@Validated OfflineZoneProtectionSupportInfoParam param) {
        // 构建请求参数
        RequestGetProtectionSupportInfo request = offlineZoneConvert.buildProtectionSupportInfoRequest(
                ContextUtils.getBusinessEvnEnum().getAppId(), param);

        // 调用Service获取支撑信息
        Result<ProtectionSupportInfoResponse> result = offlineZoneProtectionService.getSupportInfo(request);

        if (result.rCode() == GET_SUPPORT_INFO_PLAYER_NOT_FOUND){
            log.warn("getSupportInfo,error,param={},rCode={}", param, result.rCode());
            return ResultVO.failure("主播不存在");
        }
        
        if (RpcResult.isFail(result)) {
            log.error("getSupportInfo,error,param={},rCode={}", param, result.rCode());
            return ResultVO.failure();
        }

        // 转换并返回结果
        return ResultVO.success(offlineZoneConvert.protectionSupportInfoResponse2Vo(result.target()));
    }

    /**
     * 跳槽保护-主播确认
     * @param param 请求参数
     * @return 操作结果
     */
    @PermissionCheck(passRole = {RoleEnum.PLAYER, RoleEnum.USER})
    @VerifyUserToken
    @PostMapping("playerConfirm")
    public ResultVO<Void> playerConfirm(@Validated @RequestBody OfflineZoneProtectionPlayerConfirmParam param) {
        // 获取当前主播ID
        Long playerId = ContextUtils.getContext().getSubjectId();
        
        // 构建请求参数
        RequestPlayerHandleAgreement request = offlineZoneConvert.buildPlayerHandleAgreementRequest(
                ContextUtils.getBusinessEvnEnum().getAppId(), playerId, param);

        // 调用Service处理协议
        Result<Void> result = offlineZoneProtectionService.playerHandleAgreement(request);

        if (RpcResult.isFail(result)) {
            log.error("playerHandleAgreement,error,param={},playerId={},rCode={}", param, playerId, result.rCode());
            return handlePlayerConfirmError(result.rCode());
        }

        return ResultVO.success();
    }

    /**
     * 跳槽保护-提交协议
     * @param param 请求参数
     * @return 操作结果
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN, RoleEnum.ROOM})
    @VerifyUserToken
    @PostMapping("submitAgreement")
    public ResultVO<Void> submitAgreement(@Validated @RequestBody OfflineZoneProtectionSubmitParam param) {
        // 获取当前用户ID作为上传用户
        Long uploadUserId = ContextUtils.getContext().getSubjectId();
        
        // 构建请求参数
        RequestSubmitAgreement request = offlineZoneConvert.buildSubmitAgreementRequest(
                ContextUtils.getBusinessEvnEnum().getAppId(), uploadUserId, param);

        // 调用Service提交协议
        Result<Void> result = offlineZoneProtectionService.submitAgreement(request);

        if (RpcResult.isFail(result)) {
            log.error("submitAgreement,error,param={},uploadUserId={},rCode={}", param, uploadUserId, result.rCode());
            return handleSubmitAgreementError(result.rCode());
        }

        return ResultVO.success();
    }

    /**
     * 跳槽保护-获取协议内容
     * @param id 协议ID
     * @return 协议详情
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN, RoleEnum.PLAYER})
    @VerifyUserToken
    @GetMapping("getDetail")
    public ResultVO<OfflineZoneProtectionDetailVo> getDetail(@RequestParam("id") Long id) {
        // 调用Service获取协议详情
        Result<ProtectionDetailResponse> result = offlineZoneProtectionService.getDetail(id);
        
        if (RpcResult.isFail(result)) {
            log.error("getDetail,error,id={},rCode={}", id, result.rCode());
            return ResultVO.failure();
        }

        // 转换并返回结果
        return ResultVO.success(offlineZoneConvert.protectionDetailResponse2Vo(result.target()));
    }

    /**
     * 处理主播确认协议的错误
     */
    private ResultVO<Void> handlePlayerConfirmError(int rCode) {
        switch (rCode) {
            case OfflineZoneProtectionService.PLAYER_HANDLE_PARAM_ERROR:
                return ResultVO.failure("参数错误，请检查输入信息");
            case OfflineZoneProtectionService.PLAYER_HANDLE_PROTECTION_NOT_FOUND:
                return ResultVO.failure("协议不存在，请确认协议ID是否正确");
            case OfflineZoneProtectionService.PLAYER_HANDLE_ALREADY_PROCESSED:
                return ResultVO.failure("协议已处理，无法重复操作");
            case OfflineZoneProtectionService.PLAYER_HANDLE_EXPIRED:
                return ResultVO.failure("协议已过期，无法进行处理");
            case OfflineZoneProtectionService.PLAYER_HANDLE_FAIL:
                return ResultVO.failure("处理失败，请稍后重试");
            default:
                return ResultVO.failure("操作失败，请联系客服");
        }
    }

    /**
     * 处理提交协议的错误
     */
    private ResultVO<Void> handleSubmitAgreementError(int rCode) {
        switch (rCode) {
            case OfflineZoneProtectionService.SUBMIT_AGREEMENT_PARAM_ERROR:
                return ResultVO.failure("参数错误，请检查输入信息");
            case OfflineZoneProtectionService.SUBMIT_AGREEMENT_EXIST:
                return ResultVO.failure("协议已存在，请勿重复提交");
            case OfflineZoneProtectionService.SUBMIT_AGREEMENT_EXPIRED:
                return ResultVO.failure("协议已过期，无法提交");
            case OfflineZoneProtectionService.SUBMIT_AGREEMENT_NOT_EXIST:
                return ResultVO.failure("协议不存在，请确认协议信息");
            case OfflineZoneProtectionService.SUBMIT_AGREEMENT_FAIL:
                return ResultVO.failure("保存协议失败，请稍后重试");
            default:
                return ResultVO.failure("提交失败，请联系客服");
        }
    }
}
