package fm.lizhi.ocean.wavecenter.web.module.file.handler;


import cn.hutool.core.date.DateUtil;
import fm.lizhi.common.romefs.javasdk.config.RomeFsConfig;
import fm.lizhi.common.romefs.javasdk.service.PutObjectService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.context.ServiceContext;
import fm.lizhi.ocean.wavecenter.web.common.util.EnvUtils;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.file.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.util.Date;
import java.util.UUID;

@Slf4j
@Component
public class RomefsUploadHandler {

    @Autowired
    private AppConfig appConfig;

    /**
     * 上传文件到罗马
     * @param path 文件的访问路径
     * @return
     */
    public String uploadFileToRome(String path){
        try {
            File file = new File(path);
            return uploadLocalFileToRome(file);
        }catch (Exception e){
            log.error("uploadFileToRome path={}",path,e);
        }
        return "";
    }

    /**
     * 使用读流 进行上传文件
     * @param inputStream
     * @param fileName
     * @return
     */
    public String uploadFileToRome(InputStream inputStream, String fileName){
        try {
            File file = FileUtils.inputStreamToFile(inputStream, fileName);
            return uploadLocalFileToRome(file);
        }catch (Exception e){
            log.error("uploadFileToRome fileName={}",fileName,e);
        }
        return "";
    }

    /**
     * 使用罗马上传组件，上传本地的文件
     *
     * 目前只有国内支持罗马上传
     * @param file 本地文件
     * @return
     */
    public String uploadLocalFileToRome(File file){
        String fileName = file.getName();
        log.info("uploadFile start. fileName = {}",fileName);
        long startTime = System.currentTimeMillis();
        String path = "";
        try {
            RomeFsConfig.RomeFsConfigBuild builder = RomeFsConfig.builder()
                    .appId(1)// appId
                    .deviceId("web-ocean-wavecenter")//设备id
                    .address(appConfig.getRomeFsCdn())// RomeFS内网域名
                    .customFileName(true);
            if (!EnvUtils.isOffice()){
                builder.hostApp("ocean");
            }

            RomeFsConfig config = builder.build();
            PutObjectService putobjectService = new PutObjectService(config);

            String accessModifier = "public";
            if (EnvUtils.isOffice()) {
                accessModifier = "private";
            }

            //上传文件
            path = putobjectService.putObject(accessModifier
                    , ""
                    , getFileRootPath() + UUID.randomUUID() + "/" + fileName
                    , file);
            return path;
        } catch (Exception e) {
            log.error("uploadFile uploadWithType fileName={}",fileName, e);
        } finally {
            if (!file.delete()) {
                log.error("uploadFile uploadWithType file final delete false fileName={}",fileName);
            }
            log.info("uploadFile path={},cost={}",path,(System.currentTimeMillis()-startTime));
        }
        return "";
    }

    private String getFileRootPath(){
        String datePath = DateUtil.format(new Date(), "yyyy/MM/dd");
        String businessName = "unknow";
        ServiceContext context = ContextUtils.getContext();
        if (context != null) {
            BusinessEvnEnum businessEvnEnum = context.getBusinessEvnEnum();
            if (businessEvnEnum != null) {
                businessName = businessEvnEnum.getBusinessEnv();
            }
        }

        return "/wavecenter/"+businessName+"/"+datePath+"/";
    }

}
