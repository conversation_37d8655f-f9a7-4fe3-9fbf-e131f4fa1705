package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivitySimpleClassificationBean;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 *
 * 活动分类
 *
 * <AUTHOR>
 * @date 2024-10-10 06:34:31
 */
@Data
@Builder
public class ActivityClassificationVO {

    /**
     * 大类 ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 大类名称
     */
    private String name;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 大类类型
     */
    private Integer type;


    private List<ActivitySimpleClassificationVO> classList;
}