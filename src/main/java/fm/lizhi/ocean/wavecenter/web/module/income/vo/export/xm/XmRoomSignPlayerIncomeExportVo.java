package fm.lizhi.ocean.wavecenter.web.module.income.vo.export.xm;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/24 17:34
 */
@Data
public class XmRoomSignPlayerIncomeExportVo {

    @ExcelProperty(value = "签约主播")
    private String playerName;

    @ExcelProperty(value = "签约主播ID")
    private String playerBand;

    @ExcelProperty(value = "签约厅收礼魅力值")
    private Integer charm;

    /**
     * 签约厅收礼
     */
    @ExcelProperty(value = "签约厅收礼钻")
    private String signHallIncome;


    @ExcelProperty(value = "官方厅收礼收入字段")
    private String officialHallIncome;

    @ExcelProperty(value = "考核收入字段")
    private String checkIncome;

}
