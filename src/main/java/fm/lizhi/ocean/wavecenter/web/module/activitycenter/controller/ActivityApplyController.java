package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestActivityApplyBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryActivityListBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseQueryActivityListBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityInfoDetail;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityApplyConvert;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.DeleteActivityApplyParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.GetActivityInfoDetailResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import scala.App;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity")
@Slf4j
public class ActivityApplyController {

    @Autowired
    private ActivityApplyService activityApplyService;

    @Autowired
    private UserFamilyService userFamilyService;

    @Autowired
    private AppConfig appConfig;

    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER})
    @PostMapping("/apply")
    public ResultVO<String> activityApply(@RequestBody ActivityApplyParamVO paramVo) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        if (!appConfig.isForceCheckApplyUserWechatSwitch() && paramVo.getContactNumber() == null) {
            log.info("forceCheckApplyUserWechatSwitch is false, set contactNumber to empty，name={}", paramVo.getName());
            paramVo.setContactNumber("");
        }
        RequestActivityApplyBean applyParamBean = ActivityApplyConvert.I.activityApplyParamVO2Bean(paramVo);
        applyParamBean.setAppId(appId);
        applyParamBean.setNjId(ContextUtils.getContext().getSubjectId());

        if (ContextUtils.getContext().isPlayer()) {
            Result<Long> userNj = userFamilyService.getUserNj(appId, ContextUtils.getContext().getUserId());
            if (RpcResult.isFail(userNj)) {
                return ResultVO.failure("提报失败，请刷新重试");
            }
            Long njId = userNj.target();
            if (njId == null || njId == 0) {
                return ResultVO.failure("您未签约厅，无法提报活动");
            }
            applyParamBean.setNjId(njId);
        }

        Result<Void> result = activityApplyService.activityApply(applyParamBean);
        if (RpcResult.isFail(result)) {
            if (result.rCode() <= GeneralRCode.GENERAL_RCODE_SERVICE_PROCESS_TIMEOUT) {
                return ResultVO.failure();
            }
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success();
    }

    /**
     * 修改活动提报,修改前端也是整体提交，参数与申请一致，因此共用一个param
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER})
    @PostMapping("/modify")
    public ResultVO<String> activityModify(@RequestBody ActivityApplyParamVO modifyParamVo) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        RequestActivityApplyBean bean = ActivityApplyConvert.I.activityApplyParamVO2Bean(modifyParamVo);
        bean.setAppId(appId);
        bean.setNjId(ContextUtils.getContext().getSubjectId());
        if (ContextUtils.getContext().isPlayer()) {
            Result<Long> userNj = userFamilyService.getUserNj(appId, ContextUtils.getContext().getUserId());
            if (RpcResult.isFail(userNj)) {
                return ResultVO.failure("提报失败，请刷新重试");
            }
            Long njId = userNj.target();
            if (njId == null || njId == 0) {
                return ResultVO.failure("您未签约厅，无法提报活动");
            }
            bean.setNjId(njId);
        }
        Result<Void> result = activityApplyService.activityModify(bean);
        if (RpcResult.isFail(result)) {
            return ResultVO.failure(result.rCode(), Optional.ofNullable(result.getMessage()).orElse("修改活动失败"));
        }
        return ResultVO.success();
    }

    @VerifyUserToken
    @GetMapping("/query")
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.PLAYER})
    public ResultVO<QueryActivityListVO> queryActivityList(@Validated QueryActivityListParamVO paramVo) {
        ResultVO<Void> checked = checkAndUpdateQueryTimeParam(paramVo);
        if (!checked.isOK()) {
            return ResultVO.failure(checked.getPrompt().getMsg());
        }

        RequestQueryActivityListBean bean = ActivityApplyConvert.I.queryActivityListParamVO2Bean(paramVo);
        PageParamBean paramBean = PageParamBean.builder().pageNo(paramVo.getPageNo()).pageSize(paramVo.getPageSize()).build();
        bean.setPageParam(paramBean);
        if (ContextUtils.getContext().isRoom()) {
            bean.setNjId(ContextUtils.getContext().getSubjectId());
        }

        if (ContextUtils.getContext().isPlayer()) {
            Result<UserInFamilyBean> userInFamilyRes = userFamilyService.getUserInFamily(ContextUtils.getBusinessEvnEnum().getAppId(), ContextUtils.getContext().getUserId());
            if (RpcResult.isFail(userInFamilyRes)) {
                return ResultVO.failure();
            }

            bean.setNjId(userInFamilyRes.target().getNjId());
        }
        Result<ResponseQueryActivityListBean> result = activityApplyService.queryActivityList(bean);
        if (RpcResult.isFail(result)) {
            log.info("query activity list fail, rCode: {}, message: {}, param: {}", result.rCode(), result.getMessage(), bean);
            return ResultVO.alertFailure(result.rCode(), result.getMessage());
        }
        log.info("query activity list success, total: {}, list size: {}, param: {}", result.target().getTotal(), result.target().getList().size(), bean);

        List<ActivitySimpleInfoVO> activitySimpleInfoVOS = ActivityApplyConvert.I.activitySimpleInfoBeanList2VOList(result.target().getList());
        QueryActivityListVO list = new QueryActivityListVO()
                .setTotal(result.target().getTotal())
                .setList(activitySimpleInfoVOS);
        return ResultVO.success(list);
    }


    /**
     * 检查并修改查询时间参数
     *
     * @param paramVo 查询参数
     * @return 结果
     */
    private ResultVO<Void> checkAndUpdateQueryTimeParam(QueryActivityListParamVO paramVo) {
        if ((paramVo.getMinStartTime() == null && paramVo.getMaxStartTime() != null) || paramVo.getMinStartTime() != null && paramVo.getMaxStartTime() == null) {
            return ResultVO.failure("开始结束时间不可存在一个为空");
        }

        if ((paramVo.getMinStartTime() != null) && paramVo.getMinStartTime() >= paramVo.getMaxStartTime()) {
            return ResultVO.failure("开始时间不能大于结束时间");
        }
        return ResultVO.success();
    }

    /**
     * 获取活动详情
     */
    @GetMapping("/info/detail")
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.PLAYER})
    @VerifyUserToken
    public ResultVO<GetActivityInfoDetailResult> getActivityInfoDetail(@RequestParam("id") Long id) {
        if (id == null || id == 0) {
            return ResultVO.failure();
        }
        Result<ResponseActivityInfoDetail> result = activityApplyService.queryActivityInfoDetail(id, ContextUtils.getBusinessEvnEnum().getAppId());
        if (RpcResult.isFail(result)) {
            log.warn("get activity info detail fail: {}, rCode:{}, id:{}", result.getMessage(), result.rCode(), id);
            return ResultVO.failure();
        }

        GetActivityInfoDetailResult vo = ActivityApplyConvert.I.responseActivityInfoDetail2ActivityInfoDetailVO(result.target());
        return ResultVO.success(vo);
    }


    /**
     * 查询活动流量资源审批结果
     */
    @GetMapping("/flowResource/auditResult")
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.PLAYER})
    @VerifyUserToken
    public ResultVO<List<ActivityFlowResourceDetailVO>> getFlowResourceAuditResult(@RequestParam("activityId") Long activityId) {
        if (activityId == null || activityId == 0) {
            return ResultVO.failure();
        }
        Result<List<ActivityFlowResourceDetailBean>> result = activityApplyService.getFlowResourceAuditResult(activityId, ContextUtils.getBusinessEvnEnum().getAppId());
        if (RpcResult.isFail(result)) {
            log.warn("get flow resource audit result fail: {}, rCode:{}, activityId:{}", result.getMessage(), result.rCode(), activityId);
            return ResultVO.failure();
        }
        List<ActivityFlowResourceDetailVO> vos = ActivityApplyConvert.I.activityFlowResourceDetailBean2VO(result.target());
        return ResultVO.success(vos);
    }

    /**
     * 删除活动提报
     */
    @VerifyUserToken
    @PostMapping("/apply/delete")
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER})
    public ResultVO<Void> deleteActivityApply(@RequestBody @Validated DeleteActivityApplyParam param) {
        Result<Void> result = activityApplyService.deleteActivityApply(param.getId(), ContextUtils.getBusinessEvnEnum().getAppId());
        if (RpcResult.isFail(result)) {
            log.warn("delete activity apply fail: {}", result.getMessage());
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success();
    }
}
