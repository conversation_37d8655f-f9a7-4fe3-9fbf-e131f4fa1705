package fm.lizhi.ocean.wavecenter.web.common.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/16
 */
public class ContextUtils {

    private static final TransmittableThreadLocal<ServiceContext> contextThreadLocal = new TransmittableThreadLocal<>();

    public static void setContext(ServiceContext ctx) {
        contextThreadLocal.set(ctx);
    }

    /**
     * 维护线程上下文的BusinessEvnEnum, 泛化调用使用
     */
    public static void setBusinessEvnEnum(BusinessEvnEnum businessEvnEnum) {
        ServiceContext context = ContextUtils.getContext();
        if (context == null || context.getBusinessEvnEnum() == null) {
            ServiceContext serviceContext = new ServiceContext(businessEvnEnum);
            contextThreadLocal.set(serviceContext);
        }
    }

    public static void clearContext() {
        contextThreadLocal.remove();
    }

    public static ServiceContext getContext() {
        return contextThreadLocal.get();
    }

    public static BusinessEvnEnum getBusinessEvnEnum() {
        ServiceContext ctx = contextThreadLocal.get();
        return ctx.getBusinessEvnEnum();
    }

    /**
     * 获取运行环境
     *
     * @return 运行环境
     */
    public static Env getEnv() {
        return ConfigUtils.getEnv();
    }
}
