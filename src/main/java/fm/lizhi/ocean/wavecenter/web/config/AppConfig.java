package fm.lizhi.ocean.wavecenter.web.config;

import fm.lizhi.ocean.wavecenter.web.common.util.EnvUtils;
import lombok.Data;

/**
 * 应用配置，对应namespace为"application"
 *
 * <AUTHOR> 由脚手架生成
 */
@Data
public class AppConfig {

    private Integer serverPort;


    /**
     * 罗马上传的cdn
     */
    private String romeFsCdn = "http://romefs.yfxn.lizhi.fm/";

    /**
     * 罗马下载的cdn
     */
    private String romeFsDownloadCdn = "http://romefs.yfxn.lizhi.fm/";

    /**
     * 文件导出每页数据大小
     */
    private int exportPageSize = 200;

    /**
     * 文件导出sheet页大小
     */
    private int exportSheetSize = 1000000;

    /**
     * 验证码redis地址
     */
    private String smsCodeRedisHost = EnvUtils.isOffice() ? "redis.ops.lizhi.fm" : EnvUtils.isPre() ? "***************" : "";

    /**
     * 验证码redis端口
     */
    private int smsCodeRedisPort = EnvUtils.isOffice() ? 6379 : EnvUtils.isPre() ? 6415 : 0;


    /**
     * 审核展示 的op 以逗号分开
     */
    private String auditOp = "10,11,12,14,151,153,158,160,170,171,172,173";

    /**
     *
     * 线上：public-kafka250-bootstrap-server
     * 审核kafka消费地址
     */
    private String auditKafkaAddress = "kafka-bootstrap-server";

    /**
     * 审核同步的违规记录的app标识
     */
    private String auditRecordSaveApp = "ppyw";

    /**
     * 黑叶临时数据
     */
    private String hySettlementRatio = "男厅&女厅  (58-X)% - (65-X)%\n" +
            "交友&点唱  (60-X)% - (67-X)%";

    /**
     * 飞书应用ID
     */
    private String larkAppId = "********************";

    /**
     * 飞书应用密钥
     */
    private String larkAppSecret = "pVtQObMRXktrfjZ6IX9AOhGWQQcCMLIf";

    /**
     * 测试环境：https://lizhi2021.feishu.cn/wiki/VBjxwHez0iSNnnktYfycLjAWnKf?table=tblhy7BWbLEDrt2M&view=vewPOC8Ksu
     * 线上：https://lizhi2021.feishu.cn/wiki/RjWtweByti6f17kbUnDcQVmnnEh?table=tblICUUlRJ5LN2m9&view=vewPOC8Ksu
     */
    private String feedbackTableId = "tblhy7BWbLEDrt2M";

    /**
     * https://open.feishu.cn/document/server-docs/docs/wiki-v2/space-node/get_node?appId=********************
     */
    private String feedbackAppToken = "UkGpbJb9laHVOFsXE72cRqcKnBf";

    /**
     * 榜单最大查询天数范围
     */
    private int maxRankQueryDayRange = 30;

    /**
     * 活动列表最大页条目
     */
    private int maxActivityListPageSize = 20;

    /**
     * 活动中心运营联系方式
     */
    private String operationContacts = "{\"10919088\":[{\"nickname\":\"PP酱\",\"contact\":\"ppyw-3\"}],\"9637128\":[{\"nickname\":\"猫(活动x内容)\",\"contact\":\"Lizhifans\"},{\"nickname\":\"粒粒橙子（内容)\",\"contact\":\"xiaoximi-LLC\"}],\"57333013\":[{\"nickname\":\"伴伴\",\"contact\":\"xpbxm666\"},{\"nickname\":\"小陪伴内容酱\",\"contact\":\"xpb101\"}]}";

    /**
     * 默认查询时间范围
     */
    private Long defaultQueryTimeRang = 7 * 24 * 60 * 60 * 1000L;

    /**
     * 测试服务时间
     */
    private String testServerTimeMillis = "";

    /**
     * 打卡报告访问按设备号限流
     */
    private boolean checkInReportLimitSwitch = true;
    private boolean checkInReportVerifySignSwitch = true;
    private long checkInReportLimitMaxCountPerMin = 100;
    /**
     * 打卡报告url md5 盐值
     */
    private String checkInMD5SaltValue = "432346Ebuf#*(BF#$^GFB";

    /**
     * 上新配置
     */
    private String versionGuideConfig = "[{\"key\":\"income/member\",\"stopExposeVersion\":\"1.6.0\",\"featureVersion\":1},{\"key\":\"income\",\"stopExposeVersion\":\"1.6.0\",\"featureVersion\":1}]";

    /**
     * 强制校验报名人微信开关
     */
    private boolean forceCheckApplyUserWechatSwitch = true;

 }
