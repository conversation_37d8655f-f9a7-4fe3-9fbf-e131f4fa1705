package fm.lizhi.ocean.wavecenter.web.module.resource.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 推荐卡使用记录
 * <AUTHOR>
 * @date 2025/3/14 18:15
 */
@Data
@Accessors(chain = true)
public class RecommendCardUseRecordVO {

    /**
     * 使用记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 厅主信息
     */
    private UserVo room;

    /**
     * 使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date useTime;

    /**
     * 推荐时间段
     */
    private String recommendationTime;

    /**
     * 使用数量
     */
    private Integer useNum;

    /**
     * 推荐位置
     */
    private String position;

    /**
     * 推荐类型 仅黑叶
     */
    private String category;

    /**
     * 曝光百分比 乘以100后
     */
    private String exposureRate;

}
