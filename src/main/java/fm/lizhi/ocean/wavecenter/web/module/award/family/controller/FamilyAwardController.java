package fm.lizhi.ocean.wavecenter.web.module.award.family.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyAwardService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.award.family.convert.FamilyAwardConvert;
import fm.lizhi.ocean.wavecenter.web.module.award.family.param.GetFamilyWeekAwardV1Param;
import fm.lizhi.ocean.wavecenter.web.module.award.family.param.GetFamilyWeekAwardV2Param;
import fm.lizhi.ocean.wavecenter.web.module.award.family.vo.GetFamilyWeekAwardV1VO;
import fm.lizhi.ocean.wavecenter.web.module.award.family.vo.GetFamilyWeekAwardV2VO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/award/family")
public class FamilyAwardController {

    @Autowired
    private FamilyAwardService familyAwardService;

    /**
     * 获取公会周奖励V1(陪伴/西米)
     *
     * @param param 请求参数
     * @return 响应结果
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY})
    @VerifyUserToken
    @GetMapping("/getWeekAwardV1")
    public ResultVO<GetFamilyWeekAwardV1VO> getWeekAwardV1(@Validated GetFamilyWeekAwardV1Param param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();
        RequestGetFamilyWeekAwardV1 request = FamilyAwardConvert.I.toRequestGetFamilyWeekAwardV1(param, appId, familyId);
        Result<ResponseGetFamilyWeekAwardV1> result = familyAwardService.getFamilyWeekAwardV1(request);
        if (RpcResult.isFail(result)) {
            log.error("getFamilyWeekAwardV1 fail. request={}, rCode={}, message={}", request, result.rCode(), result.getMessage());
            return ResultVO.failure("公会周奖励加载失败");
        }
        ResponseGetFamilyWeekAwardV1 response = result.target();
        GetFamilyWeekAwardV1VO VO = FamilyAwardConvert.I.toGetFamilyWeekAwardV1VO(response);
        return ResultVO.success(VO);
    }

    /**
     * 获取公会周奖励V1(PP)
     *
     * @param param 请求参数
     * @return 响应结果
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY})
    @VerifyUserToken
    @GetMapping("/getWeekAwardV2")
    public ResultVO<GetFamilyWeekAwardV2VO> getWeekAwardV2(@Validated GetFamilyWeekAwardV2Param param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();
        RequestGetFamilyWeekAwardV2 request = FamilyAwardConvert.I.toRequestGetFamilyWeekAwardV2(param, appId, familyId);
        Result<ResponseGetFamilyWeekAwardV2> result = familyAwardService.getFamilyWeekAwardV2(request);
        if (RpcResult.isFail(result)) {
            log.error("getFamilyWeekAwardV2 fail. request={}, rCode={}, message={}", request, result.rCode(), result.getMessage());
            return ResultVO.failure("公会周奖励加载失败");
        }
        ResponseGetFamilyWeekAwardV2 response = result.target();
        GetFamilyWeekAwardV2VO VO = FamilyAwardConvert.I.toGetFamilyWeekAwardV2VO(response);
        return ResultVO.success(VO);
    }
}
