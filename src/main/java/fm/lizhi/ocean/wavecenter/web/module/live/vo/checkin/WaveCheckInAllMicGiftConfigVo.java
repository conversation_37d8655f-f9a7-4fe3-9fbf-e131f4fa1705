package fm.lizhi.ocean.wavecenter.web.module.live.vo.checkin;

import lombok.Data;

import java.util.List;

/**
 * 全麦配置
 * <AUTHOR>
 */
@Data
public class WaveCheckInAllMicGiftConfigVo {


    /**
     * 是否开启全麦奖励规则
     */
    private Boolean enabled;

    /**
     * 有效人数.
     */
    private Integer effectiveCount;

    /**
     * 是否包含房主（大头号）
     */
    private Boolean containNj;

    /**
     * 计算类型.
     * 1=梯度计算
     * 2=倍率计算
     */
    private Integer calcType;

    /**
     * 奖励阶梯列表
     */
    private List<WaveCheckInAllMicGiftConfigLadderVo> ladders;


}