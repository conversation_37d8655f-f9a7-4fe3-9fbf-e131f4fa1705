package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceGiveStatusEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivityFlowResourceDetailVO {

    /**
     * 资源额外信息
     */
    private FlowResourceExtraVO extra;

    /**
     * 审核状态，0：待审核，1：不发放，2：可发放
     */
    private Integer resourceAuditStatus;

    /**
     * 发放状态，0：未发放，1：发放失败，2：发放成功
     *
     * @see ActivityResourceGiveStatusEnum
     */
    private Integer giveStatus;

    /**
     * 流量资源图片地址
     */
    private String resourceImageUrl;

    /**
     * 资源配置ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long resourceConfigId;

    /**
     * 资源在app上的图片信息
     */
    private String imageUrl;

    /**
     * 状态，0：禁用，1：启用
     */
    private Integer status;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源配置类型，1：自动，2：手动
     */
    private Integer deployType;

    /**
     * 资源code，只有自动配置的资源有
     */
    private String resourceCode;

    /**
     * 资源介绍
     */
    private String introduction;
}
