package fm.lizhi.ocean.wavecenter.web.common.util;

import lombok.experimental.UtilityClass;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Map;
import java.util.TreeMap;

@UtilityClass
public class SignUtil {
    private static final String SECRET_KEY = "your-secret-key";

    /**
     * 生成签名
     * @param params 请求参数
     * @return 签名
     */
    public String generateSign(Map<String, String> params) {
        // 1. 参数排序
        TreeMap<String, String> sortedParams = new TreeMap<>(params);

        // 2. 拼接参数
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        sb.append("&key=").append(SECRET_KEY);

        // 3. MD5加密
        return DigestUtils.md5Hex(sb.toString());
    }

    /**
     * 验证签名，现在还没有时间要求，只校验参数是否合法
     */
    public boolean verifySign(Map<String, String> params, String sign) {
        String calculatedSign = generateSign(params);
        return calculatedSign.equals(sign);
    }
}