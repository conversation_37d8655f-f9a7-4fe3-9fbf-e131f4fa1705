package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ApplyTimeConfigVO {

    /**
     * 至少提前N分钟提报
     */
    private Integer minApplyPreactMin;

    /**
     * 最多提前N天提报
     */
    private Integer maxActivityPeriodMin;

    /**
     * 提报活动最大周期间隔
     */
    private Integer maxPreactApplyDay;
}
