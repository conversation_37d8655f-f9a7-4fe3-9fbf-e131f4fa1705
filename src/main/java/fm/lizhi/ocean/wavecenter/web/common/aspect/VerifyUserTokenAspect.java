package fm.lizhi.ocean.wavecenter.web.common.aspect;

import cn.hutool.core.util.NumberUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.user.service.UserLoginService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.context.ServiceContext;
import fm.lizhi.ocean.wavecenter.web.common.util.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/16
 */
@Slf4j
@Aspect
@Component
@Order(10)
public class VerifyUserTokenAspect {

    public static final long EMPTY_USER_ID = 0L;

    @Autowired
    private UserLoginService userLoginService;


    @Pointcut("@annotation(fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        ServiceContext serviceContext = ContextUtils.getContext();
        VerifyUserToken userToken = getAnnotation(joinPoint, VerifyUserToken.class);
        boolean required = userToken.required();
        String token = serviceContext.getHeader().getAccessToken();

        Long userId = null;
        Result<Long> tokenResult = userLoginService.getUserIdByAccessToken(token);
        if (EnvUtil.isTestEnv()) {
            //测试环境
            //token过期，返回刷新token
            if (required) {
                if (StringUtils.isNotBlank(token) && NumberUtil.isNumber(token)) {
                    userId = Long.parseLong(token);
                } else {
                    if (tokenResult.rCode() == UserLoginService.ACCESS_TOKEN_EXPIRED) {
                        return ResultVO.failure(MsgCodes.ACCESS_TOKEN_EXPIRED);
                    }
                    userId = tokenResult.target();
                }
            }
        } else {
            //线上环境
            if (required) {
                //token为空，返回未登录
                if (StringUtils.isEmpty(token)) {
                    return ResultVO.failure(MsgCodes.NOT_LOGGED_IN);
                }
                //token过期，返回刷新token
                if (tokenResult.rCode() == UserLoginService.ACCESS_TOKEN_EXPIRED) {
                    log.info("ACCESS_TOKEN_EXPIRED,token={}", token);
                    return ResultVO.failure(MsgCodes.ACCESS_TOKEN_EXPIRED);
                }
                if (RpcResult.isFail(tokenResult)) {
                    log.error("getUserIdByAccessToken,token={},rCode={}", token, tokenResult.rCode());
                    return ResultVO.failure();
                }
                userId = tokenResult.target();
            }
        }

        if (userId == null && required) {
            // 获取不到，用户未登录
            log.warn("user not login, token={}, appId={}, url={}", token, serviceContext.getHeader().getAppId(), serviceContext.getUrl());
            return ResultVO.failure(MsgCodes.NOT_LOGGED_IN);
        }

        // 设置进上下文中
        serviceContext.setUserId(userId == null ? EMPTY_USER_ID : userId);
        serviceContext.setToken(token);
        return joinPoint.proceed();
    }

    /**
     * 获取切入目标上指定的Annotation。此方法优先从方法获取，若不存在则从类获取
     *
     * @param pjp
     * @param annotationClass
     * @return
     */
    protected <T extends Annotation> T getAnnotation(final ProceedingJoinPoint pjp, Class<T> annotationClass) {
        Method targetMethod = getTargetMethod(pjp);
        T annotation = targetMethod.getAnnotation(annotationClass);
        if (annotation == null) {
            annotation = targetMethod.getDeclaringClass().getAnnotation(annotationClass);
        }
        return annotation;
    }

    /**
     * 获取切入目标方法
     *
     * @return
     */
    protected Method getTargetMethod(final ProceedingJoinPoint pjp) {
        return ((MethodSignature) pjp.getSignature()).getMethod();
    }


}
