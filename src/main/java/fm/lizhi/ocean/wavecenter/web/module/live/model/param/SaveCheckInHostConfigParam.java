package fm.lizhi.ocean.wavecenter.web.module.live.model.param;

import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.CheckInHostConfigVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SaveCheckInHostConfigParam {

    /**
     * 周开始时间"2025-01-01"
     */
    private String startDate;

    /**
     * 周结束时间"2025-01-07"
     */
    private String endDate;

    /**
     * 主持人配置
     */
    private List<CheckInHostConfigVo> hostDetails;

}
