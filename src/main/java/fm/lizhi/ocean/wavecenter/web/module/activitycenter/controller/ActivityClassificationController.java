package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityClassification;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityClassificationService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityClassificationConvert;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.ActivityClassificationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scala.annotation.meta.param;

import java.util.List;


/**
 * 活动分类
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity/classification")
@Slf4j
public class ActivityClassificationController {

    @Autowired
    private ActivityClassificationService activityClassificationService;

    /**
     * 查询活动分类
     */
    @GetMapping("/list")
    @VerifyUserToken
    public ResultVO<List<ActivityClassificationVO>> list() {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        Result<List<ResponseActivityClassification>> result = activityClassificationService.getClassificationListByUserId(appId, userId);

        if (RpcResult.isFail(result)) {
            return ResultVO.failure(result.getMessage());
        }

        return ResultVO.success(ActivityClassificationConvert.I.ResponseActivityClassification2ActivityClassificationVO(result.target()));
    }


}
