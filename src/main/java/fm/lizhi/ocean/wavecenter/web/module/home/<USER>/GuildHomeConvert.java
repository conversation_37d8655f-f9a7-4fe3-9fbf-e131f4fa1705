package fm.lizhi.ocean.wavecenter.web.module.home.convert;

import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildMarketMonitorSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildMarketMonitorTrendChart;
import fm.lizhi.ocean.wavecenter.web.module.home.result.GuildKeyDataSummaryResult;
import fm.lizhi.ocean.wavecenter.web.module.home.result.GuildKeyDataTrendChartResult;
import fm.lizhi.ocean.wavecenter.web.module.home.result.GuildMarketMonitorSummaryResult;
import fm.lizhi.ocean.wavecenter.web.module.home.result.GuildMarketMonitorTrendChartResult;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GuildHomeConvert {

    GuildHomeConvert I = Mappers.getMapper(GuildHomeConvert.class);

    GuildKeyDataSummaryResult toGuildKeyDataSummaryResult(ResponseGuildKeyDataSummary target);

    List<GuildKeyDataTrendChartResult> toGuildKeyDataTrendChartResult(List<ResponseGuildKeyDataTrendChart> target);

    GuildMarketMonitorSummaryResult toGuildMarketMonitorSummaryResult(ResponseGuildMarketMonitorSummary target);

    List<GuildMarketMonitorTrendChartResult> toGuildMarketMonitorTrendChartResults(List<ResponseGuildMarketMonitorTrendChart> target);

}
