package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.RegionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestDataMonitorMapData;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestDataMonitorRoomSummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestRegion;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorRoomSummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneDataMonitorService;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert.OfflineZoneConvert;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.param.OfflineZoneSummaryParam;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.OfflineZoneFamilySummaryVo;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.OfflineZoneMapDataVo;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.OfflineZoneSummaryVo;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.RegionVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 线下专区-数据监控Controller
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@RestController
@RequestMapping("/offline/dataMonitor")
public class OfflineZoneDataMonitorController {

    @Autowired
    private OfflineZoneDataMonitorService offlineZoneDataMonitorService;
    
    @Autowired
    private DataScopeHandler dataScopeHandler;

    @Autowired
    private OfflineZoneConvert offlineZoneConvert;

    /**
     * 数据监控-地图数据分布
     * @return 地图数据分布信息
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/mapData")
    public ResultVO<OfflineZoneMapDataVo> mapData() {
        Long familyId = ContextUtils.getContext().getSubjectId();

        // 构建请求参数
        RequestDataMonitorMapData request = offlineZoneConvert.buildMapDataRequest(
                ContextUtils.getBusinessEvnEnum().getAppId(), familyId);

        // 调用Service获取地图数据
        Result<List<MapDataBean>> result = offlineZoneDataMonitorService.getMapData(request);

        if (RpcResult.isFail(result)) {
            log.error("mapData,error,familyId={},rCode={}", familyId, result.rCode());
            return ResultVO.failure();
        }

        // 转换并返回结果
        OfflineZoneMapDataVo resVo = new OfflineZoneMapDataVo();
        resVo.setMapData(offlineZoneConvert.mapDataBeans2Vos(result.target()));
        return ResultVO.success(resVo);
    }

    /**
     * 数据监控-汇总-厅
     * @param paramVo 请求参数
     * @return 汇总数据信息
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/room/summary")
    public ResultVO<OfflineZoneSummaryVo> summary(@Validated OfflineZoneSummaryParam paramVo) {
        // 权限校验 - 检查当前用户是否有权限访问该家族数据
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        if (familyId == null) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }
        Long queryRoomId = dataScopeHandler.getRoomForRoomOrDefault(paramVo.getRoomId());

        RequestDataMonitorRoomSummary request = offlineZoneConvert.buildRoomSummaryRequest(
                ContextUtils.getBusinessEvnEnum().getAppId(),familyId, queryRoomId);

        // 调用Service获取汇总数据
        Result<ResponseDataMonitorRoomSummary> result = offlineZoneDataMonitorService.getRoomSummary(request);

        if (RpcResult.isFail(result)) {
            log.error("summary,error,familyId={},roomId={},rCode={}", familyId, paramVo.getRoomId(), result.rCode());
            return ResultVO.failure();
        }

        // 转换并返回结果
        return ResultVO.success(offlineZoneConvert.roomSummaryBean2Vo(result.target()));
    }

    /**
     * 数据监控-汇总-公会
     * @return 公会汇总数据信息
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/family/summary")
    public ResultVO<OfflineZoneFamilySummaryVo> familySummary() {
        // 获取当前用户的家族ID
        Long familyId = dataScopeHandler.getFamilyForFamily();
        if (familyId == null) {
            return ResultVO.failure("未找到关联的家族信息");
        }

        // 构建请求参数
        RequestDataMonitorFamilySummary request = offlineZoneConvert.buildFamilySummaryRequest(
                ContextUtils.getBusinessEvnEnum().getAppId(), familyId);

        // 调用Service获取公会汇总数据
        Result<ResponseDataMonitorFamilySummary> result = offlineZoneDataMonitorService.getFamilySummary(request);

        if (RpcResult.isFail(result)) {
            log.error("familySummary,error,familyId={},rCode={}", familyId, result.rCode());
            return ResultVO.failure();
        }

        // 转换并返回结果
        return ResultVO.success(offlineZoneConvert.familySummaryBean2Vo(result.target()));
    }

    /**
     * 获取行政区划列表
     * @return 行政区划列表
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN, RoleEnum.ROOM, RoleEnum.PLAYER})
    @VerifyUserToken
    @GetMapping("/regionList")
    public ResultVO<List<RegionVo>> regionList() {
        try {
            RequestRegion request = offlineZoneConvert.buildRegionRequest(ContextUtils.getBusinessEvnEnum().getAppId());
            Result<List<RegionBean>> result = offlineZoneDataMonitorService.getRegionList(request);
            if (RpcResult.isSuccess(result)) {
                List<RegionVo> regionVos = offlineZoneConvert.regionBeans2Vos(result.target());
                return ResultVO.success(regionVos);
            } else {
                log.error("获取行政区划列表失败: {}", result.rCode());
                return ResultVO.failure();
            }
        } catch (Exception e) {
            log.error("获取行政区划列表异常", e);
            return ResultVO.failure();
        }
    }
}
