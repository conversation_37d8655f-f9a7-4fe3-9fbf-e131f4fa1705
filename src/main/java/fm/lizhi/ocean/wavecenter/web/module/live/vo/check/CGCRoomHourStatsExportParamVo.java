package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 公会-厅打卡汇总小时统计参数
 * <AUTHOR>
 * @date 2024/6/7 11:05
 */
@Data
public class CGCRoomHourStatsExportParamVo {

    /**
     * 开始时间 yyyy-MM-dd
     */
    @NotBlank(message = "开始时间不可为空")
    private String startDate;

    /**
     * 结束时间 yyyy-MM-dd
     */
    @NotBlank(message = "结束时间不可为空")
    private String endDate;

}
