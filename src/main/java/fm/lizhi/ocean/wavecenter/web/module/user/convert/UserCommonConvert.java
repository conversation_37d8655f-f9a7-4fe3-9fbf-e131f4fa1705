package fm.lizhi.ocean.wavecenter.web.module.user.convert;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageConfigBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.response.ResponseSearchUser;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10 19:30
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface UserCommonConvert {

    UserCommonConvert I = Mappers.getMapper(UserCommonConvert.class);

    RoomSignVo roomSignBean2Vo(RoomSignBean bean);

    List<RoomSignVo> roomSignBeans2Vos(List<RoomSignBean> beans);

    PlayerSignVo playerSignBean2Vo(PlayerSignBean bean);

    List<PlayerSignVo> playerSignBeans2Vos(List<PlayerSignBean> beans);

    UserVo userBean2Vo(UserBean bean);

    UserRoleInfoVo userBean2roleInfoVo(UserBean bean);

    UserRoleInfoVo userRoleInfoBean2Vo(UserRoleInfoBean bean);

    PageConfigVo pageConfigBean2Vo(PageConfigBean bean);

    List<PageConfigVo> pageConfigBeans2Vos(List<PageConfigBean> beans);

    UserRoleInfoVo loginRoleInfoBean2Vo(LoginRoleInfoBean bean);

    UserInfoBean userInfoBean2Vo(UserInfoVo vo);

    SearchUserResultVo searchUserResultBean2Vo(ResponseSearchUser bean);

    SearchFamilyVo searchFamilyBean2Vo(SearchFamilyBean bean);

    SearchRoomVo searchRoomBean2Vo(SearchRoomBean bean);

    RoomVo roomBean2Vo(RoomBean bean);

    List<RoomVo> roomBeans2Vos(List<RoomBean> beans);

}
