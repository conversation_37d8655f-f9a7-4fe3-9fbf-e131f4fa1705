package fm.lizhi.ocean.wavecenter.web.module.income.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomePlayerService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.util.WcAssert;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.income.convert.IncomeConvert;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/26 12:01
 */
@Slf4j
@RestController
@RequestMapping("income/player/")
public class IncomePlayerController {

    @Autowired
    private IncomePlayerService incomeService;

    @Autowired
    private FileExportHandler fileExportHandler;

    /**
     * 个人收益汇总
     * @return
     */
    @VerifyUserToken
    @GetMapping("sum")
    public ResultVO<PlayerSumResVo> playerSum(){
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<PlayerSumResBean> result = incomeService.playerSum(appId, userId);
        if (RpcResult.isFail(result)) {
            log.error("playerSum,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(IncomeConvert.I.playerSumResBean2Vo(result.target()));
    }

    /**
     * 个人收益-房间收礼流水-查询
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("room/giftflow/sum")
    public ResultVO<PlayerRoomGiftflowVo> playerRoomGiftflowSum(@Validated PlayerRoomGiftflowParamVo paramVo){
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        Result<PlayerRoomGiftflowBean> result = incomeService.playerRoomGiftflowSum(PlayerRoomGiftflowParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .startDate(DateUtil.parse(paramVo.getStartDate() + " 00:00:00"))
                .endDate(DateUtil.parse(paramVo.getEndDate() + " 23:59:59"))
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .sendUserBand(paramVo.getSendUserId())
                .recUserBand(paramVo.getRecUserId())
                .build());
        if (RpcResult.isFail(result)) {
            log.error("playerRoomGiftflowSum,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(IncomeConvert.I.playerRoomGiftflowBean2Vo(result.target()));
    }

    /**
     * 个人收益-房间收礼流水-导出
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("room/giftflow/export")
    public ResultVO<Void> playerRoomGiftflowExport(@Validated PlayerRoomGiftflowParamVo paramVo){
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        String fileName = fileExportHandler.genFileName("房间收礼流水");
        return fileExportHandler.exportFile(fileName, PlayerRoomGiftflowExportVo.class, (pageNo, pageSize)->{
            Result<PageBean<PlayerRoomGiftflowBean>> result = incomeService.playerRoomGiftflow(PlayerRoomGiftflowParamBean.builder()
                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                    .userId(ContextUtils.getContext().getUserId())
                    .startDate(DateUtil.parse(paramVo.getStartDate() + " 00:00:00"))
                    .endDate(DateUtil.parse(paramVo.getEndDate() + " 23:59:59"))
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .sendUserBand(paramVo.getSendUserId())
                    .recUserBand(paramVo.getRecUserId())
                    .build());
            if (RpcResult.isFail(result)) {
                log.error("playerRoomGiftflowExport,rCode={}", result.rCode());
                return PageVO.empty();
            }
            PageBean<PlayerRoomGiftflowBean> pageBean = result.target();
            List<PlayerRoomGiftflowExportVo> voList = IncomeConvert.I.playerRoomGiftflowBeans2ExportVos(pageBean.getList());
            return PageVO.of(pageBean.getTotal(), voList);
        });
    }

    /**
     * 个人收益-房间收礼流水-查询
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("room/giftflow")
    public ResultVO<PageVO<PlayerRoomGiftflowVo>> playerRoomGiftflow(@Validated PlayerRoomGiftflowParamVo paramVo){

        DateTime startDate = DateUtil.parse(paramVo.getStartDate()+ " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate()+ " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        Long flushTime = endDate.getTime()> paramVo.getFlushTime()? paramVo.getFlushTime():endDate.getTime();
        endDate = new DateTime(flushTime);

        Result<PageBean<PlayerRoomGiftflowBean>> result = incomeService.playerRoomGiftflow(PlayerRoomGiftflowParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .startDate(startDate)
                .endDate(endDate)
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .sendUserBand(paramVo.getSendUserId())
                .recUserBand(paramVo.getRecUserId())
                .build());
        if (RpcResult.isFail(result)) {
            log.error("playerRoomGiftflow,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        PageBean<PlayerRoomGiftflowBean> pageBean = result.target();
        List<PlayerRoomGiftflowVo> voList = IncomeConvert.I.playerRoomGiftflowBeans2Vos(pageBean.getList());
        return ResultVO.success(PageVO.of(pageBean.getTotal(), voList));
    }

    /**
     * 个人收益-个人收礼流水-导出
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("personal/giftflow/export")
    public ResultVO<Void> playerPersonalGiftflowExport(@Validated PlayerPersonalGiftflowParamVo paramVo){
        DateTime startDate = DateUtil.parse(paramVo.getStartDate()+ " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate()+ " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        long now = System.currentTimeMillis();
        Long flushTime = Math.min(endDate.getTime(), now);
        endDate = new DateTime(flushTime);

        String fileName = fileExportHandler.genFileName("个人收礼流水");
        DateTime finalEndTime = endDate;
        return fileExportHandler.exportFile(fileName, PersonalGiftflowExportVo.class, (pageNo, pageSize)->{
            Result<PageBean<PersonalGiftflowBean>> result = incomeService.playerPersonalGiftflow(PlayerPersonalGiftflowParamBean.builder()
                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                    .userId(ContextUtils.getContext().getUserId())
                    .startDate(startDate)
                    .endDate(finalEndTime)
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .sendUserBand(paramVo.getSendUserId())
                    .recRoomBand(paramVo.getRecRoomId())
                    .build());
            if (RpcResult.isFail(result)) {
                log.error("playerPersonalGiftflowExport fail rCode={}", result.rCode());
                return PageVO.empty();
            }
            PageBean<PersonalGiftflowBean> pageBean = result.target();
            List<PersonalGiftflowExportVo> voList = IncomeConvert.I.personalGiftflowBeans2ExportVos(pageBean.getList());
            return PageVO.of(pageBean.getTotal(), voList);
        });
    }

    /**
     * 个人收益-个人收礼流水-查询
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("personal/giftflow/sum")
    public ResultVO<PersonalGiftflowVo> playerPersonalGiftflowSum(@Validated PlayerPersonalGiftflowParamVo paramVo){
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        Result<PersonalGiftflowBean> result = incomeService.playerPersonalGiftflowSum(PlayerPersonalGiftflowParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .startDate(DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN))
                .endDate(DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN))
                .sendUserBand(paramVo.getSendUserId())
                .recRoomBand(paramVo.getRecRoomId())
                .build());
        if (RpcResult.isFail(result)) {
            log.error("playerPersonalGiftflowSum,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(IncomeConvert.I.personalGiftflowBean2Vo(result.target()));
    }

    /**
     * 个人收益-个人收礼流水-查询
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("personal/giftflow")
    public ResultVO<PageVO<PersonalGiftflowVo>> playerPersonalGiftflow(@Validated PlayerPersonalGiftflowParamVo paramVo){
        DateTime startDate = DateUtil.parse(paramVo.getStartDate()+ " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate()+ " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(paramVo.getStartDate(), paramVo.getEndDate());
        Long flushTime = endDate.getTime()> paramVo.getFlushTime()? paramVo.getFlushTime():endDate.getTime();
        endDate = new DateTime(flushTime);

        Result<PageBean<PersonalGiftflowBean>> result = incomeService.playerPersonalGiftflow(PlayerPersonalGiftflowParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .startDate(startDate)
                .endDate(endDate)
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .sendUserBand(paramVo.getSendUserId())
                .recRoomBand(paramVo.getRecRoomId())
                .build());
        if (RpcResult.isFail(result)) {
            log.error("playerPersonalGiftflow,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        PageBean<PersonalGiftflowBean> pageBean = result.target();
        List<PersonalGiftflowVo> voList = IncomeConvert.I.personalGiftflowBeans2Vos(pageBean.getList());
        return ResultVO.success(PageVO.of(pageBean.getTotal(), voList));
    }

    /**
     * 个播收益-个人收入明细
     */
    @VerifyUserToken
    @GetMapping("/incomeDetail")
    public ResultVO<PageVO<PlayerIncomeDetailVo>> playerIncomeDetail(@Validated GetPlayerIncomeDetailParamVo paramVo){
        DateTime startDate = DateUtil.parse(paramVo.getStartDate()+ " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate()+ " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);



        GetPlayerIncomeDetailParamBean.GetPlayerIncomeDetailParamBeanBuilder builder = GetPlayerIncomeDetailParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .endDate(endDate)
                .startDate(startDate)
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize());
        if (CollectionUtils.isNotEmpty(paramVo.getIncomeType())) {
            builder.incomeType(paramVo.getIncomeType().stream().map(IncomeType::valueOf).collect(Collectors.toList()));
        }

        Result<PageBean<PlayerIncomeDetailBean>> result = incomeService.getPlayerIncomeDetail(builder.build());
        if (RpcResult.isFail(result)) {
            log.error("playerIncomeDetail,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        PageBean<PlayerIncomeDetailBean> target = result.target();
        List<PlayerIncomeDetailVo> voList = IncomeConvert.I.playerIncomeDetailBeans2Vos(target.getList());
        return ResultVO.success(PageVO.of(target.getTotal(), voList));
    }

    /**
     * 个播收益-收入账户明细记录-导出
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("/incomeDetail/export")
    public ResultVO<Void> playerIncomeDetailExport(@Validated GetPlayerIncomeDetailExportParamVo paramVo){
        DateTime startDate = DateUtil.parse(paramVo.getStartDate()+ " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate()+ " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        String fileName = "个播收入账户明细_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, PlayerIncomeDetailExportVo.class, (pageNo, pageSize)->{
            GetPlayerIncomeDetailParamBean.GetPlayerIncomeDetailParamBeanBuilder builder = GetPlayerIncomeDetailParamBean.builder()
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .userId(ContextUtils.getContext().getUserId())
                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                    .endDate(endDate)
                    .startDate(startDate);
            if (CollectionUtils.isNotEmpty(paramVo.getIncomeType())) {
                builder.incomeType(paramVo.getIncomeType().stream().map(IncomeType::valueOf).collect(Collectors.toList()));
            }

            Result<PageBean<PlayerIncomeDetailBean>> result = incomeService.getPlayerIncomeDetailOut(builder
                    .build());
            if (RpcResult.isFail(result)) {
                log.error("playerIncomeDetailExport fail pageNo={}, pageSize={}, rCode={}, paramVo={}", pageNo
                        , pageSize, result.rCode(), JsonUtil.dumps(paramVo));
                return PageVO.empty();
            }
            int total = result.target().getTotal();
            List<PlayerIncomeDetailBean> list = result.target().getList();
            return PageVO.of(total, IncomeConvert.I.playerIncomeDetailBeans2ExportVos(list));
        });

    }
    /**
     * 个播收入-收入账户明细记录-合计
     *
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("/incomeDetail/sum")
    public ResultVO<PlayerIncomeDetailSumVo> playerIncomeDetailSum(@Validated GetPlayerIncomeDetailSumParamVo paramVo){
        DateTime startDate = DateUtil.parse(paramVo.getStartDate()+ " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate()+ " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        GetPlayerIncomeDetailSumParamBean.GetPlayerIncomeDetailSumParamBeanBuilder builder = GetPlayerIncomeDetailSumParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .endDate(endDate)
                .startDate(startDate);
        if (CollectionUtils.isNotEmpty(paramVo.getIncomeType())) {
            builder.incomeType(paramVo.getIncomeType().stream().map(IncomeType::valueOf).collect(Collectors.toList()));
        }

        Result<PlayerIncomeDetailSumBean> result = incomeService.getPlayerIncomeDetailSum(builder
                .build());
        if (RpcResult.isFail(result)) {
            log.error("playerIncomeDetailSum,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }

        return ResultVO.success(IncomeConvert.I.playerIncomeDetailSumBean2Vo(result.target()));
    }

}
