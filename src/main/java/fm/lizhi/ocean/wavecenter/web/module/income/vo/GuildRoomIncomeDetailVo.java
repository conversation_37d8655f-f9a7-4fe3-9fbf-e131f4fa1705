package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/27 20:07
 */
@Data
public class GuildRoomIncomeDetailVo {
    /**
     * 厅主信息
     */
    private UserVo roomInfo;
    /**
     * 厅品类
     */
    private String cateName;
    /**
     * 收入
     */
    private String income;
    /**
     * 魅力值
     */
    private String charm;
    /**
     * 签约厅收礼（收入钻）
     */
    private String signRoomIncome;
    /**
     * 个播收入（钻）
     */
    private String  personalRoomIncome;
    /**
     * 厅贵族提成
     */
    private String signRoomVipIncome;
    /**
     * 个播贵族收入
     */
    private String personalRoomVipIncome;
    /**
     * 有收入主播数
     */
    private String playerPayCount;

    /**
     * 官方厅收礼收入
     */
    private String officialIncome;
}
