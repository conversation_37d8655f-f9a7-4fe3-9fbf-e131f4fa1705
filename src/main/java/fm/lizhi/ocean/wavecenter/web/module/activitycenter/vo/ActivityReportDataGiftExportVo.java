package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 活动报表-用户送礼表
 *
 * <AUTHOR>
 * @date 2024-10-16 04:43:36
 */
@Data
public class ActivityReportDataGiftExportVo {

    /**
     * 活动ID
     */
    @ExcelProperty("活动ID")
    private String activityId;

    /**
     * 提报厅厅主ID
     */
    @ExcelProperty("提报厅厅主ID")
    private String njId;

    /**
     * 家族ID
     */
    @ExcelProperty("家族ID")
    private String familyId;

    /**
     * 用户ID
     */
    @ExcelProperty("用户ID")
    private String userId;


    /**
     * 用户名称
     */
    @ExcelProperty("用户昵称")
    private String userName;

    /**
     * 送礼钻石数
     */
    @ExcelProperty("送礼钻石数")
    private Long allIncome;

    /**
     * 送礼魅力值
     */
    @ExcelProperty("送礼魅力值")
    private Long allCharm;

    /**
     * 送礼数量
     */
    @ExcelProperty("送礼数量")
    private Long giftCnt;
}