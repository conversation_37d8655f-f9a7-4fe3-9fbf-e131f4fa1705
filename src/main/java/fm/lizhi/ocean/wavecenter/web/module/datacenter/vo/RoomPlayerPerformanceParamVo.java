package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @date 2024/4/18 16:13
 */
@Data
public class RoomPlayerPerformanceParamVo {

    /**
     * 厅主ID，厅主访问可不传
     */
    private Long roomId;

    @Min(value = 1)
    private Integer pageNo = 1;

    @Min(value = 1)
    @Max(value = 30)
    private Integer pageSize = 20;

    /**
     * 排序指标
     */
    private String orderMetrics;
    /**
     * desc,asc
     */
    private String orderType;
}
