package fm.lizhi.ocean.wavecenter.web.module.live.model.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserRecordSumBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserSumBean;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomStatistic;
import lombok.Data;

import java.util.List;

/**
 * 获取麦序福利厅详情结果. 参考{@link ResponseGetCheckInRoomStatistic}, 主要是解决Long类型id使用ToStringSerializer.
 */
@Data
public class GetCheckInRoomDetailResult {

    /**
     * 麦序福利厅明细统计列表
     */
    private List<Statistic> list;

    /**
     * 行合计, 厅打卡明细按指标&时间合计
     */
    private Statistic detailSum;

    /**
     * 主持人信息
     */
    private User hostInfo;

    @Data
    public static class Statistic {

        /**
         * 麦序福利主播信息
         */
        private User player;

        /**
         * 打卡明细, 按时间段排序
         */
        private List<WaveCheckInUserRecordSumBean> detail;

        /**
         * 打卡汇总
         */
        private WaveCheckInUserSumBean sum;
    }

    @Data
    public static class User {

        /**
         * id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 昵称
         */
        private String name;

        /**
         * 波段号
         */
        private String band;

        /**
         * 头像
         */
        private String photo;
    }
}
