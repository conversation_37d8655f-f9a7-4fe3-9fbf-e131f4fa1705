package fm.lizhi.ocean.wavecenter.web.config;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.util.EnvUtils;
import fm.lizhi.ocean.wavecenter.web.exception.AssertException;
import fm.lizhi.ocean.wavecenter.web.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;

/**
 * Web ExceptionHandler
 *
 * <AUTHOR> generator
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice
public class WebExceptionHandler {

    private final static Logger logger = LoggerFactory.getLogger(WebExceptionHandler.class);

    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ResultVO<Void>> handleBusinessException(BusinessException exception) {
        logger.error("business exception", exception);
        ResultVO<Void> result = ResultVO.failure(exception.getCode(), exception.getMessage());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ExceptionHandler(AssertException.class)
    public ResponseEntity<ResultVO<Void>> handleAssertException(AssertException exception) {
        logger.warn("process exception", exception);
        ResultVO<Void> result = ResultVO.failure(exception.getCode(), exception.getMessage());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ResultVO<Void>> handleIllegalArgumentException(IllegalArgumentException exception) {
        logger.warn("process exception", exception);
        ResultVO<Void> result = ResultVO.failure(exception.getMessage());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResponseEntity<ResultVO<Void>> handleMethodArgumentNotValidException(Exception validateException) {
        String errorMsg = getErrorMessage(validateException);
        logger.warn("method argument not valid exception, msg:{}", errorMsg);
        ResultVO<Void> result = ResultVO.failure(errorMsg);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    private String getErrorMessage(Exception exception) {
        FieldError fieldError = null;
        if(exception instanceof MethodArgumentNotValidException) {
            fieldError = ((MethodArgumentNotValidException) exception).getBindingResult().getFieldError();
        }
        else if(exception instanceof BindException) {
            fieldError = ((BindException) exception).getBindingResult().getFieldError();
            if (fieldError != null) {
                logger.warn("method argument not valid BindException, msg:{}", fieldError.getDefaultMessage());
            }
            return "参数类型错误";
        }
        if(fieldError == null) {
            return "参数校验错误";
        }
        return fieldError.getDefaultMessage();
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ResultVO<Void>> handleException(Exception exception) {
        logger.error("exception", exception);
        ResultVO<Void> result = null;
        if (EnvUtils.isOffice()) {
            result = ResultVO.failure();
        } else {
            result = ResultVO.warn();
        }
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * Sentinel限制异常处理器
     *
     * @param e       异常
     * @param request 请求
     * @return Sentinel限制返回
     */
    @ExceptionHandler(BlockException.class)
    public ResultVO<Void> handleBlockException(BlockException e, HttpServletRequest request) {
        logger.warn("request limit, uri: {}", request.getRequestURI(), e);
        return ResultVO.failure("请求频繁");
    }

}