package fm.lizhi.ocean.wavecenter.web.module.activitycenter.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.ActivityTemplateFlowResourceVO;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.HighlightVO;
import lombok.Data;

import java.util.List;

/**
 * 分页查询通用活动模板结果
 */
@Data
public class PageGeneralActivityTemplateResult {

    /**
     * 活动模板id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 大类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bigClassId;

    /**
     * 分类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 流量资源
     */
    private List<ActivityTemplateFlowResourceVO> flowResources;

    /**
     * 活动模板亮点标签列表
     */
    private List<HighlightVO> highlights;
}
