package fm.lizhi.ocean.wavecenter.web.common.util;

import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;
import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;

/**
 * 环境判断工具类
 */
public class EnvUtils {

    private static String appName;
    private static String localIP;

    private static String vTag;

    static {
        appName = System.getProperty("app.name", "unknown");
        localIP = System.getProperty("local.ip", "unknown");
    }

    /**
     * 是否是办公环境，测试环境
     *
     * @return
     */
    public static boolean isOffice() {
        return ConfigUtils.getEnv() == Env.TEST;
    }

    /**
     * 是否是预发环境
     *
     * @return
     */
    public static boolean isPre() {
        return ConfigUtils.getEnv() == Env.PRE;
    }

    /**
     * 是否是线上环境
     *
     * @return
     */
    public static boolean isPro() {
        return ConfigUtils.getEnv() == Env.PRO;
    }

    /**
     * 是否生产环境，预发或者线上
     *
     * @return
     */
    public static boolean isProduct() {
        return isPre() || isPro();
    }

    /**
     * 获取APPName
     *
     * @return 获取不到则会返回字符串：unknown
     */
    public static String getAppName() {
        String serviceName = ConfigUtils.getServiceName();
        if (StringUtils.isBlank(serviceName)) {
            serviceName = appName;
        }
        return serviceName;
    }

    /**
     * 获取本机IP地址
     *
     * @return
     */
    public static String getLocalIp() {
        String result = "unknown";
        if (StringUtils.isNotBlank(localIP) && !result.equalsIgnoreCase(localIP)) {
            result = localIP;
        } else {
            try {
                // 多网卡情况下，只获取第一个
                result = InetAddress.getLocalHost().getHostAddress();
            } catch (Exception e) {
            }
        }
        return result;
    }

    /**
     * 获取迭代标签,线上和预发环境获取 Env.name
     *
     * @return
     */
    public static String getVTag() {
        vTag = ConfigUtils.getEnv().name();
        if (Env.TEST == ConfigUtils.getEnv()) {
            String sysVTag = System.getenv("vtag");
            if (sysVTag == null || "".equals(sysVTag)) {
                sysVTag = System.getProperty("vtag", "common");
            }
            vTag = sysVTag;
        }
        return vTag;
    }

}
