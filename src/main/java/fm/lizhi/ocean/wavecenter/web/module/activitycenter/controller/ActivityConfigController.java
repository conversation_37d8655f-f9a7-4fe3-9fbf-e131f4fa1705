package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityCommonService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityBaseEnumConfigConvert;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityBaseConfigResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityConfigResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityOptionOfficialTimeResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/activity")
public class ActivityConfigController {

    @Autowired
    private ActivityCommonService activityCommonService;


    /**
     * 获取基础枚举配置
     */
    @GetMapping("/getBaseEnumConfig")
    public ResultVO<ActivityBaseConfigResult> getBaseEnumConfig() {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<ResponseActivityConfigBean> result = activityCommonService.getBaseEnumConfig(appId);
        if (RpcResult.isFail(result)) {
            log.warn("getBaseEnumConfig fail. appId:{}", appId);
            return ResultVO.failure();
        }

        return ResultVO.success(ActivityBaseEnumConfigConvert.I.toActivityBaseConfigResult(result.target()));
    }

    @GetMapping("/config")
    public ResultVO<ActivityConfigResult> getConfig() {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<ResponseActivityConfig> activityConfigs = activityCommonService.getActivityConfigs(appId);
        if (RpcResult.isFail(activityConfigs)) {
            log.warn("getConfig fail. appId:{}", appId);
            return ResultVO.failure();
        }
        return ResultVO.success(ActivityBaseEnumConfigConvert.I.configResp2Result(activityConfigs.target()));
    }

}
