package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PlayerCheckInDetailVo {

    /**
     * 打卡的用户id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 波段号
     */
    private String band;

    private String income;

    /**
     * 最终的魅力值
     */
    private long charm;

    /**
     * 麦序
     */
    private int seatOrder;

    /**
     * 调整前的
     */
    private long originalCharm;

}
