package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 个人收入
 * <AUTHOR>
 */
@Data
public class PersonalIncomeDetailVo {

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date date;

    private String income;

    private String content;

    private String revenueAmount;

    /**
     * 类型
     */
    private String incomeType;

}
