package fm.lizhi.ocean.wavecenter.web.module.permission.convert;

import fm.lizhi.ocean.wavecenter.api.permissions.bean.GetUserPermissionBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleInfoAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.PermissionInfoVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.RoleAuthRefVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.RoleInfoAuthRefVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.RoleVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11 11:28
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface RoleConvert {

    RoleConvert I = Mappers.getMapper(RoleConvert.class);

    RoleVo bean2Vo(RoleBean bean);

    List<RoleVo> beans2Vos(List<RoleBean> beans);

    UserVo userBean2Vo(UserBean bean);

    List<UserVo> userBeans2Vos(List<UserBean> bean);

    RoleAuthRefVo authRefBean2Vo(RoleAuthRefBean bean);

    RoleInfoAuthRefVo authRefInfoBean2InfoVo(RoleInfoAuthRefBean bean);

    List<RoleInfoAuthRefVo> authRefInfoBeans2InfoVos(List<RoleInfoAuthRefBean> beans);

    List<RoleAuthRefVo> authRefBeans2Vos(List<RoleAuthRefBean> beans);

    PermissionInfoVo getUserPermissionBean2InfoVo(GetUserPermissionBean bean);
}
