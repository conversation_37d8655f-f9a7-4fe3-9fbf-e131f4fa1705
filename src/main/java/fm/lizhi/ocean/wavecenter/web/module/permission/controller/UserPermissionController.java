package fm.lizhi.ocean.wavecenter.web.module.permission.controller;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.GetUserPermissionBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleInfoAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.service.RoleService;
import fm.lizhi.ocean.wavecenter.api.permissions.service.UserPermissionService;
import fm.lizhi.ocean.wavecenter.api.user.bean.SaveUserRoleReqBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.api.user.service.UserLoginService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.context.ServiceContext;
import fm.lizhi.ocean.wavecenter.web.module.permission.convert.RoleConvert;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.GetPermissionListVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.PermissionInfoVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.SaveRoleReqVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.UserRoleListVo;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserRoleInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/27 14:36
 */
@Slf4j
@RestController
@RequestMapping("/user/permission")
public class UserPermissionController {

    @Autowired
    private UserPermissionService userPermissionService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserCommonService userCommonService;
    @Autowired
    private UserLoginService userLoginService;

    /**
     * 保存用户角色
     * @return
     */
    @VerifyUserToken
    @PostMapping("saveRole")
    public ResultVO<PermissionInfoVo> saveRole(@Validated @RequestBody SaveRoleReqVo reqVo){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        String deviceId = ContextUtils.getContext().getHeader().getDeviceId();
        //调用DC设置
        Result<Void> result = userLoginService.saveUserRole(SaveUserRoleReqBean.builder()
                .userId(userId)
                .appId(appId)
                .roleConfigId(reqVo.getRoleConfigId())
                .deviceId(deviceId)
                .build());
        int rCode = result.rCode();
        log.info("saveRole,saveUserRole,rCode={}", rCode);
        if (rCode == UserLoginService.NOT_LOGIN) {
            return ResultVO.failure("请重新登录，登录过期");
        }
        if (rCode == UserLoginService.ROLE_AUTH_NOT_EXIST) {
            return ResultVO.failure("授权已被取消，请刷新页面");
        }
        if (RpcResult.isFail(result)) {
            return ResultVO.failure();
        }
        //查询权限信息
        Result<GetUserPermissionBean> permissionResult = userPermissionService.getUserPermission(appId, userId, deviceId);
        if (RpcResult.isFail(permissionResult)) {
            return ResultVO.failure("获取权限失败");
        }
        GetUserPermissionBean permissionBean = permissionResult.target();
        PermissionInfoVo permissionInfoVo = RoleConvert.I.getUserPermissionBean2InfoVo(permissionBean);
        log.info("saveRole,permissionInfoVo={}", JsonUtil.dumps(permissionInfoVo));
        return ResultVO.success(permissionInfoVo);
    }

    /**
     * 用户可选角色列表
     * @return
     */
    @VerifyUserToken
    @GetMapping("/roleList")
    public ResultVO<UserRoleListVo> roleList(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();

        //查询被授权角色列表
        Result<List<RoleInfoAuthRefBean>> roleResult = roleService.getUserAuthRoles(appId, userId);
        if (RpcResult.isFail(roleResult)) {
            log.error("roleList,getUserAuthRoles,error,rCode={}", roleResult.rCode());
            return ResultVO.failure("获取角色信息失败");
        }

        //设置用户信息
        Result<UserBean> userInfoResult = userCommonService.getUserById(appId, userId);
        if (RpcResult.isFail(userInfoResult)) {
            log.error("roleList,getUserById,error,rCode={}", userInfoResult.rCode());
            return ResultVO.failure("获取用户信息失败");
        }

        UserRoleInfoVo userRoleInfoVo = UserCommonConvert.I.userBean2roleInfoVo(userInfoResult.target());
        Result<String> userRoleCode = userPermissionService.getUserRoleCode(appId, userId);
        if (RpcResult.isFail(userRoleCode)) {
            log.error("roleList,getUserRoleCode,error,rCode={}", userRoleCode.rCode());
            return ResultVO.failure("获取用户角色失败");
        }
        userRoleInfoVo.setRoleCode(userRoleCode.target());
        UserRoleListVo userRoleListVo = new UserRoleListVo();
        userRoleListVo.setUserInfo(userRoleInfoVo);
        userRoleListVo.setRoles(RoleConvert.I.authRefInfoBeans2InfoVos(roleResult.target()));
        log.info("roleList,userRoleListVo={}", JsonUtil.dumps(userRoleListVo));
        return ResultVO.success(userRoleListVo);
    }

    /**
     * 获取用户权限信息
     * @return
     */
    @GetMapping("permissionList")
    @VerifyUserToken
    public ResultVO<GetPermissionListVo> permissionList() {
        ServiceContext context = ContextUtils.getContext();
        long userId = context.getUserId();
        int appId = context.getBusinessEvnEnum().getAppId();
        String deviceId = context.getHeader().getDeviceId();

        Result<GetUserPermissionBean> response = userPermissionService.getUserPermission(appId, userId, deviceId);
        if (RpcResult.isFail(response)) {
            log.error("permissionList,getUserPermission,error,rCode={}", response.rCode());
            return ResultVO.failure();
        }

        GetUserPermissionBean bean = response.target();
        GetPermissionListVo permissionInfoVo = new GetPermissionListVo();
        permissionInfoVo.setRoleCode(bean.getRoleCode());
        permissionInfoVo.setMenu(bean.getMenu());
        permissionInfoVo.setWriteComponents(bean.getWriteComponents());
        permissionInfoVo.setReadComponents(bean.getReadComponents());
        log.info("permissionList,permissionInfoVo={}", JsonUtil.dumps(permissionInfoVo));
        return ResultVO.success(permissionInfoVo);
    }

}
