package fm.lizhi.ocean.wavecenter.web.module.grow.family.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/3/14 17:17
 */
@Data
@Accessors(chain = true)
public class FamilyLevelVO {

    /**
     * 等级id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long levelId;

    /**
     * 收入
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer income;

}
