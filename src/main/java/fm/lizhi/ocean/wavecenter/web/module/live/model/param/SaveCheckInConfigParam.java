package fm.lizhi.ocean.wavecenter.web.module.live.model.param;

import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInHistoryLimitDateEnum;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInTaskRuleTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 麦序福利配置保存参数
 */
@Data
public class SaveCheckInConfigParam {

    /**
     * 基础配置
     */
    private BaseConfig baseConfig;

    /**
     * 收光奖励配置
     */
    private LightGiftConfig lightGiftConfig;

    /**
     * 全麦配置
     */
    private AllMicGiftConfig allMicGiftConfig;

    /**
     * 日麦序配置
     */
    private DayMicConfig dayMicConfig;

    /**
     * 麦序福利管理员配置
     */
    private List<Long> checkInManagerConfig;

    /**
     * 基础配置
     */
    @Data
    public static class BaseConfig {

        /**
         * 是否展示麦序福利图.
         */
        private Boolean showAd;

        /**
         * 麦序福利图地址.
         */
        private String adUrl;

        /**
         * 是否展示奖励金额.
         */
        private Boolean showReward;

        /**
         * 查看历史打卡记录时间类型.
         *
         * @see CheckInHistoryLimitDateEnum
         */
        private Integer historyLimitDate;

        /**
         * 任务类型.
         *
         * @see CheckInTaskRuleTypeEnum
         */
        private Integer taskType;

        /**
         * 调账权限类型
         * @see fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInAdjustCharmAuthTypeEnum
         */
        private Integer adjustCharmAuthType;

    }

    /**
     * 收光奖励配置
     */
    @Data
    public static class LightGiftConfig {

        /**
         * 是否开启收光奖励规则
         */
        private Boolean enabled;

        /**
         * 奖励阶梯列表
         */
        private List<Ladder> ladders;

        /**
         * 奖励阶梯
         */
        @Data
        public static class Ladder {

            /**
             * 魅力值大于等于.
             */
            private Integer charmGreaterEqual;

            /**
             * 奖励金额.
             */
            private Integer rewardAmount;
        }
    }

    /**
     * 全麦配置
     */
    @Data
    public static class AllMicGiftConfig {


        /**
         * 是否开启全麦奖励规则
         */
        private Boolean enabled;

        /**
         * 有效人数.
         */
        private Integer effectiveCount;

        /**
         * 是否包含房主（大头号）
         */
        private Boolean containNj;

        /**
         * 计算类型.
         * 1=梯度计算
         * 2=倍率计算
         * @see fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInAllMicCalcTypeEnum
         */
        private Integer calcType;

        /**
         * 奖励阶梯列表
         */
        private List<AllMicGiftConfig.Ladder> ladders;

        /**
         * 奖励阶梯
         */
        @Data
        public static class Ladder {

            /**
             * 魅力值大于等于.
             */
            private Integer charmGreaterEqual;

            /**
             * 基础奖励金额.
             */
            private Integer rewardAmount;

            /**
             * 包含大头号的奖励金额.
             */
            private Integer containNjRewardAmount;
        }
    }


    /**
     * 日麦序配置
     */
    @Data
    public static class DayMicConfig {


        /**
         * 是否开启日麦序奖励规则
         */
        private Boolean enabled;

        /**
         * 计算类型.
         * 1=固定金额计算
         * 2=麦序计算
         * @see fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInDayMicCalcTypeEnum
         */
        private Integer calcType;

        /**
         * 最高麦序奖励个数
         */
        private Integer maxCount;

        /**
         * 奖励阶梯列表
         */
        private List<DayMicConfig.Ladder> ladders;

        /**
         * 奖励阶梯
         */
        @Data
        public static class Ladder {

            /**
             * 魅力值大于等于.
             */
            private Integer charmGreaterEqual;

            /**
             * 基础奖励金额.
             */
            private Integer rewardAmount;

            /**
             * 有效麦序个数.
             * <p>
             * 当{@link DayMicConfig#calcType}为2时, 有效麦序个数.
             */
            private Integer validMicCount;
        }
    }
}
