package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelRightBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLevelRight;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result.ListLevelRightResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface OfflineZoneLevelRightConvert {

    @Mapping(target = "appId", source = "appId")
    RequestListLevelRight toRequestListLevelRight(Integer appId);

    List<ListLevelRightResult> toListLevelRightResults(List<ListLevelRightBean> rightBeans);
}
