package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/4/24 20:01
 */
@Data
public class PlayerPersonalGiftflowParamVo {

    @NotBlank(message = "开始时间不能为空")
    private String startDate;

    @NotBlank(message = "结束时间不能为空")
    private String endDate;

    @Min(value = 1)
    private Integer pageNo=1;

    @Min(value = 1)
    @Max(value = 50)
    private Integer pageSize=20;

    private String recRoomId;

    private String sendUserId;

    private Long flushTime = System.currentTimeMillis();

}
