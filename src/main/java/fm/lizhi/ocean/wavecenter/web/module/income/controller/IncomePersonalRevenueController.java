package fm.lizhi.ocean.wavecenter.web.module.income.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.GetPersonalIncomeDetailParamBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.GetPersonalRevenueIncomeDetailParamBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.PersonalIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.PersonalIncomeDetailSumBean;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomePlayerService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.util.WcAssert;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.income.convert.IncomeConvert;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("income/revenue/")
public class IncomePersonalRevenueController {

    @Autowired
    private IncomePlayerService incomeService;

    @Autowired
    private FileExportHandler fileExportHandler;

    /**
     * 考核收入-个人收入明细
     */
    @VerifyUserToken
    @GetMapping("/incomeDetail")
    public ResultVO<PageVO<PersonalIncomeDetailVo>> revenuePersonalIncomeDetail(@Validated GetPersonalRevenueIncomeDetailParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);
        Long flushTime = endDate.getTime() > paramVo.getFlushTime() ? paramVo.getFlushTime() : endDate.getTime();
        GetPersonalRevenueIncomeDetailParamBean.GetPersonalRevenueIncomeDetailParamBeanBuilder builder = GetPersonalRevenueIncomeDetailParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .endDate(endDate)
                .startDate(startDate)
                .flushTime(flushTime)
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize());

        if (CollUtil.isNotEmpty(paramVo.getIncomeType())){
            builder.incomeType(paramVo.getIncomeType().stream().map(IncomeType::valueOf).collect(Collectors.toList()));
        }
        Result<PageBean<PersonalIncomeDetailBean>> result = incomeService.getRevenueIncomeDetail(builder.build());
        if (RpcResult.isFail(result)) {
            log.error("revenuePersonalIncomeDetail,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }
        PageBean<PersonalIncomeDetailBean> target = result.target();
        List<PersonalIncomeDetailVo> voList = IncomeConvert.I.personalIncomeDetailBeans2Vos(target.getList());
        return ResultVO.success(PageVO.of(target.getTotal(), voList, flushTime));
    }

    /**
     * 考核收入-收入账户明细记录-导出
     *
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("/incomeDetail/export")
    public ResultVO<Void> revenuePersonalIncomeDetailExport(@Validated GetPersonalIncomeDetailExportParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        String fileName = "考核收入账户明细_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        return fileExportHandler.exportFile(fileName, PersonalRevenueIncomeDetailExportVo.class, (pageNo, pageSize) -> {
            Result<PageBean<PersonalIncomeDetailBean>> result = incomeService.getRevenueIncomeDetailOut(GetPersonalIncomeDetailParamBean.builder()
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .userId(ContextUtils.getContext().getUserId())
                    .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                    .endDate(endDate)
                    .startDate(startDate)
                    .build());
            if (RpcResult.isFail(result)) {
                log.error("revenuePersonalIncomeDetailExport fail pageNo={}, pageSize={}, rCode={}, paramVo={}", pageNo
                        , pageSize, result.rCode(), JsonUtil.dumps(paramVo));
                return PageVO.empty();
            }
            int total = result.target().getTotal();
            List<PersonalIncomeDetailBean> list = result.target().getList();
            List<PersonalRevenueIncomeDetailExportVo> personalRevenueIncomeDetailExportVos = IncomeConvert.I.personalRevenueIncomeDetailBeans2ExportVos(list);
            return PageVO.of(total, personalRevenueIncomeDetailExportVos);
        });

    }

    /**
     * 考核收入-收入账户明细记录-合计
     *
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("/incomeDetail/sum")
    public ResultVO<PersonalIncomeDetailSumVo> revenuePersonalIncomeDetailSum(@Validated GetDetailBaseParamVo paramVo) {
        DateTime startDate = DateUtil.parse(paramVo.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        DateTime endDate = DateUtil.parse(paramVo.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        WcAssert.isDateQueryAllow(startDate, endDate);

        Result<PersonalIncomeDetailSumBean> result = incomeService.getRevenueIncomeDetailSum(GetPersonalRevenueIncomeDetailParamBean.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .endDate(endDate)
                .startDate(startDate)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("revenuePersonalIncomeDetailSum,rpcRCode={}", result.rCode());
            return ResultVO.failure();
        }

        return ResultVO.success(IncomeConvert.I.personalIncomeDetailSumBean2Vo(result.target()));
    }

}
