package fm.lizhi.ocean.wavecenter.web.module.live.model.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 获取麦序福利主播明细的参数
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetCheckInPlayerDetailParam {

    /**
     * 主播id
     */
    private Long playerId;

    /**
     * 开始时间毫秒时间戳, 包含
     */
    @NotNull
    private Long startDate;

    /**
     * 结束时间毫秒时间戳, 包含
     */
    @NotNull
    private Long endDate;
}
