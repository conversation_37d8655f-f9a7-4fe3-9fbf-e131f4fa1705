package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

@Data
public class GetPersonalRevenueIncomeDetailParamVo extends GetDetailBaseParamVo {



    @Min(value = 1)
    private Integer pageNo;

    @Min(value = 1)
    @Max(value = 50)
    private Integer pageSize;


    private List<Integer> incomeType;


    private Long flushTime = System.currentTimeMillis();

}
