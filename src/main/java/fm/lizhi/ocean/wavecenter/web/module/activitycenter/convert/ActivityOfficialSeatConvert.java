package fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.ActivityGetOptionalOfficialTimeParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityOptionOfficialTimeResult;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityOfficialSeatConvert {

    ActivityOfficialSeatConvert I = Mappers.getMapper(ActivityOfficialSeatConvert.class);

    @Mappings({
        @Mapping(target = "optionalTime", source = "officialOptionalTime")
    })
    ActivityOptionOfficialTimeResult toActivityOptionOfficialTimeResult(ResponseActivityOptionOfficialTime target);

    @Mappings({
        @Mapping(target = "appId", expression = "java(fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils.getBusinessEvnEnum().getAppId())")
    })
    RequestActivityOptionOfficialTime toRequestActivityOptionOfficialTime(ActivityGetOptionalOfficialTimeParam param);

}
