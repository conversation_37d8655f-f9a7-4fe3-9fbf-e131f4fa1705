package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/10/5 16:11
 */
@Data
public class ReviewCancelParamVo {

    /**
     * 签约记录ID
     */
    @NotNull(message = "签约记录ID为空")
    private Long contractId;

    /**
     * 审批意见
     * AGREE=同意, REJECT=拒绝
     */
    @NotBlank(message = "审批意见")
    private String reviewType;

}
