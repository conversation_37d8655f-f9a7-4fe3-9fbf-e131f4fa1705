package fm.lizhi.ocean.wavecenter.web.module.live.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;

/**
 * 厅私信数据导出对象
 * <AUTHOR>
 * @date 2024/4/20 15:32
 */
@Data
@FieldNameConstants
public class LiveSmsRoomExportVo {

    @ExcelProperty("厅主名称")
    private String roomName;

    @ExcelProperty("厅主ID")
    private String njId;

    /**
     * 厅签约主播数 roomCnt
     */
    @ExcelProperty("厅签约主播数")
    private Integer signPlayerCnt;

    /**
     * 私信用户数-私信人数  chatCnt
     */
    @ExcelProperty("私信人数")
    private Integer chatUserCnt;

    /**
     * 私信回复人数  replyChatCnt
     */
    @ExcelProperty("私信回复人数")
    private Integer replyChatUserCnt;

    /**
     * 私信回复率
     */
    @ExcelProperty("私信回复率")
    private BigDecimal replyChatRate;

    /**
     * 私信进房人数 chatEnterRoomCnt
     */
    @ExcelProperty("私信进房人数")
    private Integer chatEnterRoomUserCnt;

    /**
     * 私信进房率
     */
    @ExcelProperty("私信进房率")
    private BigDecimal chatEnterRoomRate;

    /**
     * 邀请人数 inviteCnt
     */
    @ExcelProperty("邀请人数")
    private Integer inviteUserCnt;

    /**
     * 邀请进房人数 inviteEnterRoomCnt
     */
    @ExcelProperty("邀请进房人数")
    private Integer inviteEnterRoomUserCnt;

    /**
     * 邀请付费人数 inviteGiftCnt
     */
    @ExcelProperty("邀请付费人数")
    private Integer inviteGiftUserCnt;

    /**
     * 邀请进房率
     */
    @ExcelProperty("邀请进房率")
    private BigDecimal inviteEnterRoomRate;

    /**
     * 邀请付费率
     */
    @ExcelProperty("邀请付费率")
    private BigDecimal inviteGiftRate;

    /**
     * 私信付费人数-私信送礼人数 chatGiftCnt
     */
    @ExcelProperty("私信送礼人数")
    private Integer chatGiftUserCnt;


    /**
     * 私信付费率-私信送礼率
     */
    @ExcelProperty("私信付费率")
    private BigDecimal chatGiftRate;

    /**
     * 私信主播数
     */
    @ExcelProperty("私信主播数")
    private Integer chatPlayerCnt;
}