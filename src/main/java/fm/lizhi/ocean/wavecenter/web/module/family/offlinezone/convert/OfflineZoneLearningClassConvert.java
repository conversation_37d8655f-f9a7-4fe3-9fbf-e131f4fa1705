package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLearningClassByTypeBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLearningClassByType;
import fm.lizhi.ocean.wavecenter.web.common.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.param.ListLearningClassByTypeParam;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result.ListLearningClassByTypeResult;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                CollectionUtils.class,
        },
        uses = {
                CommonConvert.class,
        }
)
public interface OfflineZoneLearningClassConvert {

    /**
     * 将按类型查询学习课堂的web参数转换为DC请求对象
     *
     * @param param 查询参数
     * @param appId 应用ID
     * @return 按类型查询学习课堂的DC请求对象
     */
    RequestListLearningClassByType toRequestListLearningClassByType(ListLearningClassByTypeParam param, Integer appId);

    /**
     * 将按类型列出学习课堂的bean列表转换为web结果列表, 如果白名单列表不为空则设置inWhiteList字段为true, 因为未命中白名单的数据在外层过滤了
     *
     * @param beans 按类型列出的学习课堂bean列表
     * @return 按类型列出的学习课堂结果列表
     */
    List<ListLearningClassByTypeResult> toListLearningClassByTypeResults(List<ListLearningClassByTypeBean> beans);

    @Mapping(target = "inWhiteList", expression = "java(CollectionUtils.isNotEmpty(bean.getWhiteIds()))")
    ListLearningClassByTypeResult toListLearningClassByTypeResult(ListLearningClassByTypeBean bean);
}
