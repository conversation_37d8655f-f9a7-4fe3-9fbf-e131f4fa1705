package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLearningClassByTypeBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLearningClassByType;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneLearningClassService;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert.OfflineZoneLearningClassConvert;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.param.ListLearningClassByTypeParam;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result.ListLearningClassByTypeResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 线下专区学习课堂控制器
 */
@RestController
@RequestMapping("/offline/learningClass")
@Slf4j
public class OfflineZoneLearningClassController {

    /**
     * 不存在的家族ID, 用于表示没有家族ID
     */
    private static final Long NONEXISTENT_FAMILY_ID = -1L;

    @Autowired
    private OfflineZoneLearningClassConvert offlineZoneLearningClassConvert;

    @Autowired
    private OfflineZoneLearningClassService offlineZoneLearningClassService;

    @Autowired
    private UserFamilyService userFamilyService;

    /**
     * 根据类型查询学习课堂列表
     *
     * @param param 请求参数
     * @return 学习课堂列表结果
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM})
    @VerifyUserToken
    @GetMapping("/listByType")
    public ResultVO<List<ListLearningClassByTypeResult>> listLearningClassByType(@Validated ListLearningClassByTypeParam param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        RequestListLearningClassByType request = offlineZoneLearningClassConvert.toRequestListLearningClassByType(param, appId);
        Result<List<ListLearningClassByTypeBean>> result = offlineZoneLearningClassService.listLearningClassByType(request);
        if (RpcResult.isFail(result)) {
            int rCode = result.rCode();
            if (rCode == CommonService.PARAM_ERROR) {
                String message = StringUtils.defaultIfBlank(result.getMessage(), MsgCodes.PARAM_ERROR.getMsg());
                log.info("listLearningClassByType param invalid, request={}, rCode={}, message={}", request, rCode, message);
                return ResultVO.failure(MsgCodes.PARAM_ERROR.getCode(), message);
            } else {
                log.info("listLearningClassByType fail, request={}, rCode={}, message={}", request, rCode, result.getMessage());
                return ResultVO.failure(MsgCodes.FAIL);
            }
        }
        List<ListLearningClassByTypeBean> beans = result.target();
        List<ListLearningClassByTypeBean> filteredBeans = this.filterWhiteList(beans);
        List<ListLearningClassByTypeResult> results = offlineZoneLearningClassConvert.toListLearningClassByTypeResults(filteredBeans);
        log.debug("listLearningClassByType success, request={}, results={}", request, results);
        return ResultVO.success(results);
    }

    private List<ListLearningClassByTypeBean> filterWhiteList(List<ListLearningClassByTypeBean> allBeans) {
        // 如果都没有白名单限制，直接返回所有数据（默认为不限制）
        boolean hasWhiteIds = allBeans.stream().map(ListLearningClassByTypeBean::getWhiteIds).anyMatch(CollectionUtils::isNotEmpty);
        if (!hasWhiteIds) {
            return allBeans;
        }
        boolean isFamily = ContextUtils.getContext().isFamily();
        boolean isRoom = ContextUtils.getContext().isRoom();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long subjectId = ContextUtils.getContext().getSubjectId();
        // 如果不是家族长/家族管理/厅主/厅管理，则只返回全部可见的数据（白名单列表为空）
        if (!isFamily && !isRoom) {
            ArrayList<ListLearningClassByTypeBean> allVisibleBeans = new ArrayList<>();
            for (ListLearningClassByTypeBean bean : allBeans) {
                if (CollectionUtils.isEmpty(bean.getWhiteIds())) {
                    allVisibleBeans.add(bean);
                }
            }
            return allVisibleBeans;
        }
        // 如果是家族长或家族管理，subjectId即为家族ID; 如果是厅主或厅管理，subjectId为厅主ID，需根据厅主ID查询家族ID.
        Long familyId = isFamily ? subjectId : this.getFamilyId(appId, subjectId);
        // 只保留白名单列表为空，或命中当前家族ID的学习课堂
        ArrayList<ListLearningClassByTypeBean> filteredBeans = new ArrayList<>();
        for (ListLearningClassByTypeBean bean : allBeans) {
            List<Long> whiteIds = ListUtils.emptyIfNull(bean.getWhiteIds());
            if (whiteIds.isEmpty() || whiteIds.contains(familyId)) {
                filteredBeans.add(bean);
            }
        }
        return filteredBeans;
    }

    private Long getFamilyId(int appId, long userId) {
        Result<UserInFamilyBean> result = userFamilyService.getUserInFamily(appId, userId);
        if (RpcResult.isSuccess(result)) {
            UserInFamilyBean userInFamilyBean = result.target();
            Long familyId = userInFamilyBean.getFamilyId();
            log.debug("getUserInFamily success. appId={}, userId={}, familyId={}", appId, userId, familyId);
            return familyId;
        } else {
            log.warn("getUserInFamily fail. appId={}, userId={}, rCode={}", appId, userId, result.rCode());
            return NONEXISTENT_FAMILY_ID;
        }
    }
}
