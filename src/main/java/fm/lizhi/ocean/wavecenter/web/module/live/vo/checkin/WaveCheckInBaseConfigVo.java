package fm.lizhi.ocean.wavecenter.web.module.live.vo.checkin;

import lombok.Data;

/**
 * 麦序福利基础配置
 * <AUTHOR>
 */
@Data
public class WaveCheckInBaseConfigVo {

    /**
     * 是否展示麦序福利图.
     */
    private Boolean showAd;

    /**
     * 麦序福利图地址.
     */
    private String adUrl;

    /**
     * 是否展示奖励金额.
     */

    private Boolean showReward;

    /**
     * 查看历史打卡记录时间类型.
     */

    private Integer historyLimitDate;

    /**
     * 任务类型.
     */

    private Integer taskType;

    /**
     * 调账权限类型
     * @see fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInAdjustCharmAuthTypeEnum
     */

    private Integer adjustCharmAuthType;


}
