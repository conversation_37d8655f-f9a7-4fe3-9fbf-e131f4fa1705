package fm.lizhi.ocean.wavecenter.web.module.live.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveRoomCheckInService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.file.convert.FileConvert;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.DynamicColTable;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.StatsUser;
import fm.lizhi.ocean.wavecenter.web.module.income.convert.CheckInConvert;
import fm.lizhi.ocean.wavecenter.web.module.live.convert.CheckConvert;
import fm.lizhi.ocean.wavecenter.web.module.live.handler.CheckHandler;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.*;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserFamilyHandler;
import fm.lizhi.ocean.wavecenter.web.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 厅打卡明细
 *
 * <AUTHOR>
 * @date 2024/6/6 18:28
 * @deprecated 已迁移至 {@link CheckInTempController}
 */
@Slf4j
@RestController
@RequestMapping("live/check/room")
@Deprecated
public class CheckRoomController {

    @Autowired
    private DataScopeHandler dataScopeHandler;
    @Autowired
    private FileExportHandler fileExportHandler;
    @Autowired
    private UserFamilyHandler userFamilyHandler;

    @Autowired
    private LiveRoomCheckInService liveRoomCheckInService;
    @Autowired
    private UserCommonService userCommonService;
    @Autowired
    private CheckHandler checkHandler;


    /**
     * 厅打卡-厅打卡-天-汇总
     *
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/day/sum")
    public ResultVO<RoomDayStatsSummaryResVo> roomDaySum(@Validated GuildRoomCheckSummaryParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        //厅ID
        Long roomId = dataScopeHandler.getRoomForBase(paramVo.getRoomId());
        if (roomId == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        RoomDayCheckStatsReq statsReq = RoomDayCheckStatsReq.builder()
                .startDate(dayStart)
                .endDate(dayEnd)
                .roomId(roomId)
                .appId(appId)
                .build();
        Result<RoomDayStatsSummaryRes> result = liveRoomCheckInService.roomDayStatsSummary(statsReq);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomSum error rCode={},statsReq={}", result.rCode(), JSONObject.toJSONString(statsReq));
            return ResultVO.failure("查询失败！");
        }
        RoomDayStatsSummaryRes summaryRes = result.target();
        RoomDayStatsSummaryResVo roomDayStatsSummaryResVo = CheckInConvert.I.convertRoomDayStatsSummaryVo(summaryRes);
        return ResultVO.success(roomDayStatsSummaryResVo);
    }


    /**
     * 厅打卡-厅打卡-小时-汇总
     *
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/hour/sum")
    public ResultVO<RoomHourStatsSummaryResVo> roomSum(@Validated GuildRoomCheckSummaryParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        //厅ID
        Long roomId = dataScopeHandler.getRoomForBase(paramVo.getRoomId());
        if (roomId == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        RoomHourCheckStatsReq statsReq = RoomHourCheckStatsReq.builder()
                .startDate(dayStart)
                .endDate(dayEnd)
                .roomId(roomId)
                .appId(appId)
                .build();
        Result<RoomHourStatsSummaryRes> result = liveRoomCheckInService.roomHourStatsSummary(statsReq);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomSum error rCode={},statsReq={}", result.rCode(), JSONObject.toJSONString(statsReq));
            return ResultVO.failure("查询失败！");
        }
        RoomHourStatsSummaryRes summaryRes = result.target();
        RoomHourStatsSummaryResVo roomHourStatsSummaryResVo = CheckInConvert.I.convertRoomHourStatsSummaryVo(summaryRes);
        return ResultVO.success(roomHourStatsSummaryResVo);
    }


    /**
     * 厅打卡-日历-汇总
     *
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = RoleEnum.ROOM)
    @GetMapping("/calendar")
    public ResultVO<RoomDayCalendarVo> roomCalendar(@Validated RoomDayCalendarParamVo paramVo) {
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId = ContextUtils.getContext().getSubjectId();
        RoomDayCalendarReq req = RoomDayCalendarReq.builder()
                .appId(appId)
                .startDate(dayStart)
                .endDate(dayEnd)
                .roomId(roomId)
                .build();

        Result<RoomDayCalendarRes> result = liveRoomCheckInService.roomCalendar(req);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("roomCalendar error paramVo={} rCode={}", JSONObject.toJSONString(paramVo), result.rCode());
            return ResultVO.failure("查询失败！");
        }
        RoomDayCalendarRes roomDayCalendarRes = result.target();
        RoomDayCalendarVo roomDayCalendarVo = CheckConvert.I.convertRoomDayCalendarVo(roomDayCalendarRes);
        return ResultVO.success(roomDayCalendarVo);
    }

    /**
     * 厅打卡-日历-汇总
     *
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = RoleEnum.ROOM)
    @GetMapping("/calendar/export")
    public ResultVO<Void> roomCalendarExport(@Validated RoomDayCalendarParamVo paramVo) {
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId = ContextUtils.getContext().getSubjectId();

        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.putCol("日期");

        return fileExportHandler.exportDynamicFile("厅打卡月统计", dynamicColTable, (sheet, pageNo, pageSize)->{
            RoomDayCalendarReq req = RoomDayCalendarReq.builder()
                    .appId(appId)
                    .startDate(dayStart)
                    .endDate(dayEnd)
                    .roomId(roomId)
                    .build();
            Result<RoomDayCalendarRes> result = liveRoomCheckInService.roomCalendar(req);
            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("roomCalendar error paramVo={} rCode={}", JSONObject.toJSONString(paramVo), result.rCode());
                return PageVO.empty();
            }
            RoomDayCalendarRes roomDayCalendarRes = result.target();
            List<RoomDayCalendarDetailRes> beanList = roomDayCalendarRes.getDetail();

            List<DynamicColTable.Row<Date>> statsList = new ArrayList<>();
            for (RoomDayCalendarDetailRes bean : beanList) {
                Date day = bean.getTime();
                String dayStr = DateUtil.formatDate(day);

                DynamicColTable.Row<Date> row = new DynamicColTable.Row<>();
                row.setSortRow(day);
                row.putFreezeCol(dayStr);

                row.putCol(MetricsEnum.INCOME.getName(), bean.getIncome() != null?bean.getIncome():"0");
                row.putCol(MetricsEnum.CHARM.getName(), bean.getCharm() != null?String.valueOf(bean.getCharm()):"0");
                row.putCol(MetricsEnum.SEAT_ORDER.getName(), bean.getSeatOrder() != null?String.valueOf(bean.getSeatOrder()):"0");
                row.putCol("打卡主播", bean.getCheckPlayerNumber() != null?String.valueOf(bean.getCheckPlayerNumber()):"0");
                statsList.add(row);
            }

            statsList.sort(Comparator.comparing(DynamicColTable.Row::getSortRow));

            return PageVO.of(0, statsList);
        });

    }


    /**
     * 厅打卡-厅打卡日统计
     *
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/day/stats")
    public ResultVO<CRCRoomDayStatsRes> roomDayStats(@Validated RoomDayCheckStatsParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());
        //厅ID
        Long roomId = dataScopeHandler.getRoomForBase(paramVo.getRoomId());
        if (roomId == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Integer pageNo = paramVo.getPageNo();
        Integer pageSize = paramVo.getPageSize();


        RoomDayCheckStatsReq statsReq = RoomDayCheckStatsReq.builder()
                .appId(appId)
                .endDate(dayEnd)
                .roomId(roomId)
                .startDate(dayStart)
                .pageNo(pageNo)
                .pageSize(pageSize)
                .build();
        Result<LRCSRoomDayStatsRes> result = liveRoomCheckInService.roomDayStats(statsReq);

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomDayStats error rCode={},statsReq={}", result.rCode(), JSONObject.toJSONString(statsReq));
            return ResultVO.failure("查询失败！");
        }
        List<RoomDayStatsRes> roomDayStatsRes = result.target().getList();
        List<RoomDayStatsResVo> roomDayStatsResVos = roomDayStatsRes.stream().map(CheckInConvert.I::convertRoomDayStatsResVo).collect(Collectors.toList());

        CRCRoomDayStatsRes res = new CRCRoomDayStatsRes();
        res.setTotal(result.target().getTotal());
        res.setList(roomDayStatsResVos);
        res.setTimeStats(CheckInConvert.I.timeStatsBeans2Vos(result.target().getTimeStats()));
        res.setTimeStatsSum(CheckInConvert.I.timeStatsSumBean2Vo(result.target().getTimeStatsSum()));

        return ResultVO.success(res);
    }


    /**
     * 厅打卡-厅打卡小时统计
     *
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/hour/stats")
    public ResultVO<CRCRoomHourStatsRes> roomHourStats(@Validated RoomHourCheckStatsParamVo paramVo) {
        if (!dataScopeHandler.checkParamRoom(paramVo.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());
        //厅ID
        Long roomId = dataScopeHandler.getRoomForBase(paramVo.getRoomId());
        if (roomId == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        RoomHourCheckStatsReq statsReq = RoomHourCheckStatsReq.builder()
                .appId(appId)
                .endDate(dayEnd)
                .startDate(dayStart)
                .roomId(roomId)
                .build();
        Result<LRCSRoomHourStatsRes> result = liveRoomCheckInService.roomHourStats(statsReq);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomHourStats error rCode={},statsReq={}", result.rCode(), JSONObject.toJSONString(statsReq));
            return ResultVO.failure("查询失败！");
        }
        List<RoomHourStatsRes> roomHourStatsRes = result.target().getList();
        List<RoomHourStatsResVo> roomHourStatsResVos = roomHourStatsRes.stream().map(e -> CheckInConvert.I.convertRoomHourStatsVo(e, roomId)).collect(Collectors.toList());

        CRCRoomHourStatsRes res = new CRCRoomHourStatsRes();
        res.setTotal(roomHourStatsResVos.size());
        res.setList(roomHourStatsResVos);
        res.setTimeStats(CheckInConvert.I.timeStatsBeans2Vos(result.target().getTimeStats()));
        res.setTimeStatsSum(CheckInConvert.I.timeStatsSumBean2Vo(result.target().getTimeStatsSum()));

        return ResultVO.success(res);
    }


    /**
     * 公会&厅打卡-厅打卡小时详情
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.PLAYER, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/hour/detail")
    public ResultVO<RoomHourCheckDetailVo> roomHourDetail(@Validated RoomHourCheckDetailParamVo paramVo) {
        //厅ID
        Long roomId = paramVo.getRoomId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        RoomHourCheckDetailReq req = RoomHourCheckDetailReq.builder()
                .appId(appId)
                .endDate(DateUtil.parse(paramVo.getEndDate(), DatePattern.NORM_DATETIME_PATTERN))
                .startDate(DateUtil.parse(paramVo.getStartDate(), DatePattern.NORM_DATETIME_PATTERN))
                .roomId(roomId)
                .build();
        Result<RoomHourCheckDetailRes> result = liveRoomCheckInService.hourDetail(req);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("roomHourDetail error req={},rCode={}", JSONObject.toJSONString(req), result.rCode());
            return ResultVO.failure("查询失败！");
        }
        RoomHourCheckDetailRes detailRes = result.target();
        RoomHourCheckDetailVo roomHourCheckDetailVo = CheckInConvert.I.convertRoomHourCheckDetailVo(detailRes);
        return ResultVO.success(roomHourCheckDetailVo);
    }


    /**
     * 厅打卡明细-小时统计-导出
     *
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/hour/stats/export")
    public ResultVO<Void> hourStatsExport(@Validated CRCHourStatsExportParamVo paramVo) {
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        //厅ID
        Long roomId = dataScopeHandler.getRoomForBase(paramVo.getRoomId());
        if (roomId == null) {
            return ResultVO.failure("请选择厅");
        }

        //公会ID
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();

        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.putCol("档期");
        dynamicColTable.putCol("数据类型");

        if ("all".equals(paramVo.getScope()) && ContextUtils.getContext().isFamily()) {
            //查询公会所有厅
            List<RoomSignBean> guildAllRooms = userFamilyHandler.getGuildAllRooms(familyId);
            for (RoomSignBean guildAllRoom : guildAllRooms) {
                dynamicColTable.addSheet(guildAllRoom.getName() + "-" + guildAllRoom.getBand(), guildAllRoom.getId());
            }
        } else {
            Result<UserBean> userInfoResult = userCommonService.getUserById(appId, roomId);
            String sheetName = "0";
            if (RpcResult.isSuccess(userInfoResult)) {
                UserBean userBean = userInfoResult.target();
                sheetName = userBean.getName() + "-" + userBean.getBand();
            }
            dynamicColTable.resetSheet(roomId, sheetName);
        }

        return fileExportHandler.exportDynamicFile("厅打卡明细_小时统计", dynamicColTable, (sheet, pageNo, pageSize) -> {
            Long queryRoomId = sheet.getId();

            RoomHourCheckStatsReq statsReq = RoomHourCheckStatsReq.builder()
                    .appId(appId)
                    .endDate(dayEnd)
                    .startDate(dayStart)
                    .roomId(queryRoomId)
                    .build();
            Result<LRCSRoomHourStatsRes> result = liveRoomCheckInService.roomHourStats(statsReq);
            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("roomHourStats error rCode={},statsReq={}", result.rCode(), JSONObject.toJSONString(statsReq));
                return PageVO.empty();
            }
            List<RoomHourStatsRes> rpcData = result.target().getList();

            //小时开始时间
            Table<Date, String, String> incomeTable = HashBasedTable.create();
            Table<Date, String, String> charmTable = HashBasedTable.create();
            Table<Date, String, String> hostCntTable = HashBasedTable.create();
            Table<Date, String, String> remarkTable = HashBasedTable.create();

            for (RoomHourStatsRes rpcDatum : rpcData) {
                List<RoomHourDetail> hourDetails = rpcDatum.getDetail();
                UserBean player = rpcDatum.getPlayer();
                String room = new StatsUser(player.getName(), player.getBand()).format();
                for (RoomHourDetail hourDetail : hourDetails) {
                    incomeTable.put(hourDetail.getTime(), room, hourDetail.getIncome() == null ? "0" : String.valueOf(hourDetail.getIncome()));
                    charmTable.put(hourDetail.getTime(), room, hourDetail.getCharm() == null ? "0" : String.valueOf(hourDetail.getCharm()));
                    hostCntTable.put(hourDetail.getTime(), room, hourDetail.getHostCnt() == null ? "0" : String.valueOf(hourDetail.getHostCnt()));
                    remarkTable.put(hourDetail.getTime(), room, hourDetail.getRemark() == null ? "" : hourDetail.getRemark());
                }
            }

            List<DynamicColTable.Row<Date>> statsList = new ArrayList<>();
            statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(incomeTable, "收入"));
            statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(charmTable, "魅力值"));
            statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(remarkTable, "备注"));
            statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(hostCntTable, "主持档"));

            //按照时间字段排序
            statsList.sort(Comparator.comparing(DynamicColTable.Row::getSortRow));
            return PageVO.of(24, statsList);
        });
    }

    /**
     * 厅打卡明细-日统计-导出
     *
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/day/stats/export")
    public ResultVO<Void> dayStatsExport(@Validated CRCDayStatsExportParamVo paramVo) {
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        //厅ID
        Long roomId = dataScopeHandler.getRoomForBase(paramVo.getRoomId());
        if (roomId == null && "current".equals(paramVo.getScope())) {
            return ResultVO.failure("请选择厅");
        }
        //公会ID
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();

        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.putCol("时间");
        dynamicColTable.putCol("数据类型");

        if ("all".equals(paramVo.getScope()) && ContextUtils.getContext().isFamily()) {
            //查询公会所有厅
            List<RoomSignBean> guildAllRooms = userFamilyHandler.getGuildAllRooms(familyId);
            for (RoomSignBean guildAllRoom : guildAllRooms) {
                dynamicColTable.addSheet(guildAllRoom.getName() + "-" + guildAllRoom.getBand(), guildAllRoom.getId());
            }
        } else {
            String sheetName = "0";
            if (roomId != null) {
                Result<UserBean> userInfoResult = userCommonService.getUserById(appId, roomId);
                if (RpcResult.isSuccess(userInfoResult)) {
                    UserBean userBean = userInfoResult.target();
                    sheetName = userBean.getName() + "-" + userBean.getBand();
                }
            }
            dynamicColTable.resetSheet(roomId, sheetName);
        }

        return fileExportHandler.exportDynamicFile("厅打卡明细_日统计", dynamicColTable, (sheet, pageNo, pageSize) -> {
            Long queryRoomId = sheet.getId();
            RoomDayCheckStatsReq statsReq = RoomDayCheckStatsReq.builder()
                    .appId(appId)
                    .endDate(dayEnd)
                    .roomId(queryRoomId)
                    .startDate(dayStart)
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .build();
            Result<LRCSRoomDayStatsRes> result = liveRoomCheckInService.roomDayStats(statsReq);
            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("roomDayStats error rCode={},statsReq={}", result.rCode(), JSONObject.toJSONString(statsReq));
                return PageVO.empty();
            }
            List<RoomDayStatsRes> rpcData = result.target().getList();

            Table<Date, String, String> incomeTable = HashBasedTable.create();
            Table<Date, String, String> charmTable = HashBasedTable.create();
            Table<Date, String, String> seatOrderTable = HashBasedTable.create();
            Table<Date, String, String> hostCntTable = HashBasedTable.create();

            for (RoomDayStatsRes rpcDatum : rpcData) {
                List<RoomDayDetail> dayDetails = rpcDatum.getDetail();
                UserBean player = rpcDatum.getPlayer();
                String room = new StatsUser(player.getName(), player.getBand()).format();
                for (RoomDayDetail dayDetail : dayDetails) {
                    incomeTable.put(dayDetail.getTime(), room, dayDetail.getIncome() == null ? "0" : String.valueOf(dayDetail.getIncome()));
                    charmTable.put(dayDetail.getTime(), room, dayDetail.getCharm() == null ? "0" : String.valueOf(dayDetail.getCharm()));
                    seatOrderTable.put(dayDetail.getTime(), room, dayDetail.getSeatOrder() == null ? "0" : String.valueOf(dayDetail.getSeatOrder()));
                    hostCntTable.put(dayDetail.getTime(), room, dayDetail.getHostCnt() == null ? "0" : String.valueOf(dayDetail.getHostCnt()));
                }
            }

            List<DynamicColTable.Row<Date>> statsList = new ArrayList<>();
            statsList.addAll(FileConvert.I.dayStatsTable2DynamicList(incomeTable, "收入"));
            statsList.addAll(FileConvert.I.dayStatsTable2DynamicList(charmTable, "魅力值"));
            statsList.addAll(FileConvert.I.dayStatsTable2DynamicList(seatOrderTable, "麦序"));
            statsList.addAll(FileConvert.I.dayStatsTable2DynamicList(hostCntTable, "主持档"));

            //按照时间字段排序
            statsList.sort(Comparator.comparing(DynamicColTable.Row::getSortRow));
            return PageVO.of(result.target().getTotal(), statsList);
        });
    }


}
