package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 活动报表-主播表现表
 *
 * <AUTHOR>
 * @date 2024-10-16 04:43:36
 */
@Data
public class ActivityReportDataPlayerExportVo {

    /**
     * 活动ID
     */
    @ExcelProperty("活动ID")
    private String activityId;

    /**
     * 提报厅厅主ID
     */
    @ExcelProperty("厅主ID")
    private String njId;

    /**
     * 家族ID
     */
    @ExcelProperty("家族ID")
    private String familyId;

    /**
     * 主播 ID
     */
    @ExcelProperty("主播ID")
    private String playerId;


    /**
     * 主播名称
     */
    @ExcelProperty("主播昵称")
    private String playerName;

    /**
     * 送礼人数
     */
    @ExcelProperty("送礼人数")
    private Long giftUserCnt;

    /**
     * 房间新增粉丝数
     */
    @ExcelProperty("新增粉丝数")
    private Long newFansUserCnt;

    /**
     * 收礼钻石数
     */
    @ExcelProperty("收礼钻石数")
    private Long allIncome;

    /**
     * 收礼魅力值
     */
    @ExcelProperty("收礼魅力值")
    private Long allCharm;

}