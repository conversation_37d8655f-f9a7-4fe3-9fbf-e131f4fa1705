package fm.lizhi.ocean.wavecenter.web.module.live.model.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInPlayerStatisticBean;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInPlayerStatistic;
import lombok.Data;

import java.util.List;

/**
 * 获取麦序福利主播明细的结果. 参考{@link ResponseGetCheckInPlayerStatistic}, 主要是解决Long类型id使用ToStringSerializer.
 */
@Data
public class GetCheckInPlayerDetailResult {

    /**
     * 麦序福利主播信息
     */
    private User player;

    /**
     * 麦序福利主播明细统计列表
     */
    private List<WaveCheckInPlayerStatisticBean> list;

    @Data
    public static class User {

        /**
         * id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 昵称
         */
        private String name;

        /**
         * 波段号
         */
        private String band;

        /**
         * 头像
         */
        private String photo;
    }
}
