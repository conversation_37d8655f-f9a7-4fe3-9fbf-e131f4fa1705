package fm.lizhi.ocean.wavecenter.web.module.resource.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/14 20:05
 */
@Data
public class RecommendAllocationRecordVO {

    /**
     * 详情
     */
    private String detail;

    /**
     * 分配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date allocationTime;

    /**
     * 分配数量
     */
    private Integer nums;

}
