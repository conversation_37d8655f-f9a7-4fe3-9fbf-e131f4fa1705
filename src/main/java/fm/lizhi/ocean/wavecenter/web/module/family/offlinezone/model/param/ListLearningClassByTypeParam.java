package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.param;

import fm.lizhi.ocean.wave.server.common.validator.EnumValue;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 根据类型查询学习课堂列表请求参数
 */
@Data
public class ListLearningClassByTypeParam {

    /**
     * 学习课堂类型
     */
    @NotNull(message = "学习课堂类型不能为空")
    @EnumValue(OfflineZoneLearningClassTypeEnum.class)
    private Integer type;
}
