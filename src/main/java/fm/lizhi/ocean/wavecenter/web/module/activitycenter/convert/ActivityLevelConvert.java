package fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityLevelInfoBean;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityLevelResult;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityLevelConvert {

    ActivityLevelConvert I = Mappers.getMapper(ActivityLevelConvert.class);

    List<ActivityLevelResult> convertResponseActivityLevel2ActivityLevelVO(List<ActivityLevelInfoBean> activityLevelInfoBeans);
}
