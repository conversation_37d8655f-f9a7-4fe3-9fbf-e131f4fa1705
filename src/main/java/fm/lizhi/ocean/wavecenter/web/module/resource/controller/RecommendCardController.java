package fm.lizhi.ocean.wavecenter.web.module.resource.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.AllocationItemBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendAllocationRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardUseRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardUserStockBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.service.RecommendCardService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.resource.convert.RecommendCardConvert;
import fm.lizhi.ocean.wavecenter.web.module.resource.param.AllocationRecommendCardParam;
import fm.lizhi.ocean.wavecenter.web.module.resource.param.GetAllocationItemListParam;
import fm.lizhi.ocean.wavecenter.web.module.resource.param.GetAllocationRecordParam;
import fm.lizhi.ocean.wavecenter.web.module.resource.param.GetRecommendCardUseRecordParam;
import fm.lizhi.ocean.wavecenter.web.module.resource.vo.AllocationItemVO;
import fm.lizhi.ocean.wavecenter.web.module.resource.vo.RecommendAllocationRecordVO;
import fm.lizhi.ocean.wavecenter.web.module.resource.vo.RecommendCardUseRecordVO;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserFamilyHandler;
import fm.lizhi.ocean.wavecenter.web.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/14 17:40
 */
@Slf4j
@RestController
@RequestMapping("family/recommendCard")
public class RecommendCardController {

    @Autowired
    private RecommendCardService recommendCardService;
    @Autowired
    private UserFamilyHandler userFamilyHandler;

    /**
     * 推荐卡-公会库存
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY})
    @GetMapping("familyStock")
    public ResultVO<Integer> familyStock(){
        RequestGetUserStock request = new RequestGetUserStock();
        request.setUserId(userFamilyHandler.getFamilyUserId(ContextUtils.getContext().getSubjectId()));
        request.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        Result<RecommendCardUserStockBean> result = recommendCardService.getUserStock(request);
        if (RpcResult.isSuccess(result)) {
            return ResultVO.success(result.target().getStock());
        }
        log.error("familyStock,rCode={}", result.rCode());
        return ResultVO.failure(result.getMessage());
    }

    /**
     * 推荐卡-使用明细
     * @param param
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY})
    @VerifyUserToken
    @GetMapping("useRecord")
    public ResultVO<PageVO<RecommendCardUseRecordVO>> useRecord(GetRecommendCardUseRecordParam param){

        RequestGetFamilyUseRecord req = new RequestGetFamilyUseRecord();
        req.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        req.setFamilyId(ContextUtils.getContext().getSubjectId());
        req.setNjId(param.getNjId());
        req.setUseTimeOrderType(param.getUseTimeOrderType());
        req.setPageNo(param.getPageNo());
        req.setPageSize(param.getPageSize());

        Result<PageBean<RecommendCardUseRecordBean>> result = recommendCardService.getFamilyUseRecord(req);
        if (result.rCode() == RecommendCardService.GET_FAMILY_USE_RECORD_MAX_ROOM_COUNT) {
            return ResultVO.success(PageVO.empty());
        }

        if (RpcResult.isFail(result)) {
            log.error("getFamilyUseRecord fail. rCode={},request={}", result.rCode(), req);
            return ResultVO.failure();
        }

        List<RecommendCardUseRecordVO> voList = RecommendCardConvert.I.useRecordBeans2VOs(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList));
    }

    /**
     * 分配推荐卡
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY})
    @VerifyUserToken
    @PostMapping("allocation")
    public ResultVO<Void> allocation(@RequestBody @Validated AllocationRecommendCardParam param){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = userFamilyHandler.getFamilyUserId(ContextUtils.getContext().getSubjectId());
        RequestRewardRecommendCard request = RecommendCardConvert.I.convertRewardBean(param, appId, userId);
        Result<RewardResultBean> result = recommendCardService.rewardRecommendCard(request);
        if (RpcResult.isSuccess(result)) {
            if (result.target().getCode() == 0) {
                return ResultVO.success();
            }
            log.warn("allocation,rewardResult={}", result.target().getRewardResult());
            return ResultVO.failure(result.target().getRewardResult());
        }
        log.error("allocation,rCode={},message={}", result.rCode(), result.getMessage());
        return ResultVO.failure(result.getMessage());
    }

    /**
     * 查询推荐卡分配记录
     * @param param
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY})
    @VerifyUserToken
    @GetMapping("allocationRecord")
    public ResultVO<PageVO<RecommendAllocationRecordVO>> allocationRecord(GetAllocationRecordParam param){
        RequestGetAllocationRecord request = new RequestGetAllocationRecord();
        request.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        request.setFamilyUserId(userFamilyHandler.getFamilyUserId(ContextUtils.getContext().getSubjectId()));
        request.setNjId(param.getNjId());
        request.setStartDate(MyDateUtil.getDayStart(param.getDay()));
        request.setEndDate(MyDateUtil.getDayEnd(param.getDay()));
        request.setPageNum(param.getPageNo());
        request.setPageSize(param.getPageSize());

        Result<PageBean<RecommendAllocationRecordBean>> result = recommendCardService.getAllocationRecord(request);
        if (RpcResult.isFail(result)) {
            log.error("getAllocationRecord fail. rCode={},request={}", result.rCode(), result);
            return ResultVO.failure();
        }

        List<RecommendAllocationRecordBean> list = result.target().getList();
        List<RecommendAllocationRecordVO> voList = RecommendCardConvert.I.allocationRecordBeans2VOs(list);
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList));
    }

    /**
     * 查询推荐卡分配候选列表
     * @param param
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY})
    @VerifyUserToken
    @GetMapping("getAllocationItemList")
    public ResultVO<PageVO<AllocationItemVO>> getAllocationItemList(GetAllocationItemListParam param){
        RequestGetAllocationItemList request = new RequestGetAllocationItemList();
        request.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        request.setFamilyId(ContextUtils.getContext().getSubjectId());
        request.setPageNo(param.getPageNo());
        request.setPageSize(param.getPageSize());

        Result<PageBean<AllocationItemBean>> result = recommendCardService.getAllocationItemList(request);
        if (RpcResult.isFail(result)) {
            log.error("getAllocationItemList fail. rCode={},request={}", result.rCode(), request);
            return ResultVO.failure();
        }

        return ResultVO.success(PageVO.of(result.target().getTotal(), RecommendCardConvert.I.allocationItemBeans2VOs(result.target().getList())));
    }

}
