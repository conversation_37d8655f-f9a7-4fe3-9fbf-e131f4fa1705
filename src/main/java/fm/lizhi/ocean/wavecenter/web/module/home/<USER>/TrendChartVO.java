package fm.lizhi.ocean.wavecenter.web.module.home.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.common.DoubleToLongSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 趋势图
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TrendChartVO {

    /**
     * 当前值
     */
    @JsonSerialize(using = DoubleToLongSerializer.class)
    private Double current;

    /**
     * 上期值
     */
    @JsonSerialize(using = DoubleToLongSerializer.class)
    private Double pre;

    /**
     * 环比
     */
    private String ratio;

    /**
     * 是否达到告警阈值
     */
    private Boolean warn;


}
