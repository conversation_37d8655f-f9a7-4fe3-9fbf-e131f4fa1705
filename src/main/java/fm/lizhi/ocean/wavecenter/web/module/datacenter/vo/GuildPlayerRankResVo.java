package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22 20:33
 */
@Data
@Accessors(chain = true)
public class GuildPlayerRankResVo {

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date date;

    private List<RankVo> ranks;

}
