package fm.lizhi.ocean.wavecenter.web.module.file.handler;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 列动态列表格
 * <AUTHOR>
 * @date 2024/6/12 14:14
 */
@Data
public class DynamicColTable {

    /**
     * 冻结的列
     * 即前面固定的N列，比如 时间、数据类型
     */
    private List<String> freezeCol = new ArrayList<>();

    private List<Sheet> sheets = new ArrayList<>();

    public DynamicColTable() {
        this.sheets.add(new Sheet().setName("0").setId(0L));
    }

    public void resetSheet(Long sheetId){
        this.sheets.clear();
        this.sheets.add(new Sheet().setName(String.valueOf(sheetId)).setId(sheetId));
    }

    public void resetSheet(Long sheetId, String sheetName){
        this.sheets.clear();
        this.sheets.add(new Sheet().setName(sheetName).setId(sheetId));
    }

    public void clearSheet() {
        this.sheets.clear();
    }

    public void putCol(String colName){
        this.freezeCol.add(colName);
    }

    public void addSheet(String name, Long id){
        this.sheets.add(new Sheet().setId(id).setName(name));
    }

    @Data
    @Accessors(chain = true)
    public static class Sheet{
        /**
         * sheet名称
         */
        private String name;
        /**
         * sheetId
         */
        private Long id;
    }

    @Data
    public static class Row<T> {

        /**
         * 排序字段
         */
        private T sortRow;

        /**
         * 冻结列的值
         */
        private List<String> freezeCol = new ArrayList<>();

        /**
         * 动态列值
         * key=列名 value列值
         */
        private LinkedHashMap<String, String> colValueMap = new LinkedHashMap<>();

        public void putFreezeCol(String value){
            this.freezeCol.add(value);
        }

        public void putCol(String colName, String value){
            this.colValueMap.put(colName, value);
        }

        public List<String> getColNameList(){
            return new ArrayList<>(colValueMap.keySet());
        }

    }

}
