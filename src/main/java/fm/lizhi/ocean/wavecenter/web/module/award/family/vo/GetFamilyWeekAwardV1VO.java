package fm.lizhi.ocean.wavecenter.web.module.award.family.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 获取公会周奖励V1的VO
 */
@Data
public class GetFamilyWeekAwardV1VO {

    /**
     * 推荐卡数量
     */
    private Integer recommendCardNumber;

    /**
     * 座驾id, 可能为null
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long vehicleId;

    /**
     * 座驾名称, 可能为null
     */
    private String vehicleName;

    /**
     * 座驾图片, 可能为null
     */
    private String vehicleImage;

    /**
     * 勋章id, 可能为null
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long medalId;

    /**
     * 勋章名称, 可能为null
     */
    private String medalName;

    /**
     * 勋章图片, 可能为null
     */
    private String medalImage;

    /**
     * 短号id, 可能为null
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shortNumberId;

    /**
     * 短号名称, 可能为null
     */
    private String shortNumberName;
}
