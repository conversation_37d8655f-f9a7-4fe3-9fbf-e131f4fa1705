package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserInfoVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/5 16:24
 */
@Data
public class TodoSignVo {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyId;

    /**
     * 合同ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

    /**
     * 签约ID
     */
    private String signId;

    /**
     * 厅主信息
     */
    private UserInfoVo roomInfo;

    /**
     * 下级人数
     */
    private Integer signCount;

    /**
     * 结算方式
     */
    private String settleType;

    /**
     * 分成比例
     */
    private String settlePercentage;

    /**
     * 发起时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /**
     * 类型
     * SIGN=签约，CANCEL=解约
     */
    private String signType;

    /**
     * 状态
     */
    private String status;

    /**
     * 签署有效期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signDeadline;

}
