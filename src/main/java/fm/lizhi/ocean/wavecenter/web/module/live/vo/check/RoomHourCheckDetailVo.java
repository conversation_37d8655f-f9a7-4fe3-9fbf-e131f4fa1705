package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import fm.lizhi.ocean.wavecenter.web.module.user.vo.RoomVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoomHourCheckDetailVo {


    /**
     * 未打卡主播
     */
    private List<PlayerCheckInDetailVo> unCheckPlayer;

    /**
     * 打卡主播
     */
    private List<PlayerCheckInDetailVo> checkPlayer;

    /**
     * 厅主信息
     */
    private RoomVo room;

    /**
     * 合计
     */
    private RoomHourCheckDetailStatsVo stats;

    /**
     * 主持人信息
     */
    private UserVo host;

    /**
     * 备注
     */
    private String remark;

}
