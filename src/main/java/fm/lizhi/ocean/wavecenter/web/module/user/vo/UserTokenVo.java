package fm.lizhi.ocean.wavecenter.web.module.user.vo;

import fm.lizhi.ocean.wavecenter.web.module.permission.vo.PermissionInfoVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.vo.UserRoleListVo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/12 14:52
 */
@Data
@Accessors(chain = true)
public class UserTokenVo extends UserRoleListVo {
    private String accessToken;

    private String refreshToken;

    private PermissionInfoVo permissionInfo;
    /**
     * 1=本人登录；2=授权登录
     */
    private Integer loginType;
}
