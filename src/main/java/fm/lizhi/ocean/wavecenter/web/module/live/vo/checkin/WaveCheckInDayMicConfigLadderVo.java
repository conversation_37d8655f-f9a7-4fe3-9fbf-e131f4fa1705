package fm.lizhi.ocean.wavecenter.web.module.live.vo.checkin;

import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveCheckInDayMicConfigBean;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class WaveCheckInDayMicConfigLadderVo {

    /**
     * 魅力值大于等于.
     */
    private Integer charmGreaterEqual;

    /**
     * 基础奖励金额.
     */

    private Integer rewardAmount;

    /**
     * 有效麦序个数.
     * <p>
     * 当{@link WaveCheckInDayMicConfigBean#calcType}为2时, 有效麦序个数.
     */
    private Integer validMicCount;
}