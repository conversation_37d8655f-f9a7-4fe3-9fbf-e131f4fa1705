package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/20 13:32
 */
@Data
@Accessors(chain = true)
public class FamilyAuthVo {

    /**
     * 公会 ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 公会id
     * 这个其实不是公会ID，是公会长的波段号
     */
    private String familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 1=认证通过，2=未认证
     */
    private Integer authType;

    /**
     * 认证企业
     */
    private String authCompany;

    /**
     * 公会等级ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyLevelId;

    /**
     * 公会头像url
     */
    private String familyPhotoUrl;

    /**
     * 家族长名称
     */
    private String familyUserName;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 等级信息
     */
    private FamilyLevelInfoVO familyLevelInfo;

    /**
     * 线下专区等级信息
     */
    private FamilyOfflineLevelInfoVo familyOfflineLevelInfo;

}
