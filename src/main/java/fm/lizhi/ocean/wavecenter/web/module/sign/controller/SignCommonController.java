package fm.lizhi.ocean.wavecenter.web.module.sign.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestGetContractViewUrl;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseSignContractUrl;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyBizToken;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.sign.convert.SignConvert;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.SignContractUrlVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/10/22 20:05
 */
@Slf4j
@RestController
@RequestMapping("sign/common")
public class SignCommonController {

    @Autowired
    private SignCommonService signCommonService;

    /**
     * 获取签约系统token 提供给业务扫描
     * @return
     */
    @VerifyBizToken
    @GetMapping("getSignTokenForBiz")
    public ResultVO<String> getSignTokenForBiz(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();

        Result<String> result = signCommonService.getSignToken(appId, userId);
        if (RpcResult.isFail(result)) {
            log.error("getSignToken fail. appId={},userId={},rCode={}", appId, userId, result.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }

        return ResultVO.success(result.target());
    }

    /**
     * 获取签约系统token
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.USER, RoleEnum.FAMILY, RoleEnum.PLAYER}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("getSignToken")
    public ResultVO<String> getSignToken(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();

        Result<String> result = signCommonService.getSignToken(appId, userId);
        if (RpcResult.isFail(result)) {
            log.error("getSignToken fail. appId={},userId={},rCode={}", appId, userId, result.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }

        return ResultVO.success(result.target());
    }

    /**
     * 获取查看合同链接
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.USER, RoleEnum.FAMILY, RoleEnum.PLAYER}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("getContractViewUrl")
    public ResultVO<String> getContractViewUrl(@RequestParam("contractId") Long contractId) {
        Result<String> result = signCommonService.getContractViewUrl(RequestGetContractViewUrl.builder()
                .contractId(contractId)
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .build());
        if (RpcResult.isFail(result)) {
            log.error("getContractViewUrl fail. contractId={},rCode={}", contractId, result.rCode());
            return ResultVO.failure();
        }
        return ResultVO.success(result.target());
    }


    /**
     * 获取上上签签署合同的链接
     *
     */
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.USER, RoleEnum.FAMILY, RoleEnum.PLAYER}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("/getSignContractUrl")
    public ResultVO<SignContractUrlVO> getSignContractUrl(@RequestParam("signId") String signId) {

        long userId = ContextUtils.getContext().getUserId();
        Result<ResponseSignContractUrl> result = signCommonService.getSignContractUrl(
                ContextUtils.getBusinessEvnEnum().getAppId(),
                userId, signId
        );

        if (RpcResult.isFail(result)) {
            log.error("getSignContractUrl fail. signId={},userId={},rCode={}", signId, userId, result.rCode());
            return ResultVO.failure();
        }

        return ResultVO.success(SignConvert.I.toSignContractUrlVO(result.target()));
    }

}
