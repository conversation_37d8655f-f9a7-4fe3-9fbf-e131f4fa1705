package fm.lizhi.ocean.wavecenter.web.module.live.vo;


import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class GuildAuditRecordSearchParamVo {


    /**
     * 审核操作op
     */
    private Integer op;

    /**
     * 厅ID
     */
    private Long roomId;

    /**
     * 波段号
     */
    private String band;

    /**
     * yyyy-MM-dd
     */
    @NotBlank(message = "开始时间为空")
    private String startDate;

    @NotBlank(message = "结束时间为空")
    private String endDate;

    private Integer pageNo = 1;

    private Integer pageSize = 20;

}
