package fm.lizhi.ocean.wavecenter.web.module.home.result;


import com.fasterxml.jackson.annotation.JsonFormat;
import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsDataBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.TrendChartBean;
import fm.lizhi.ocean.wavecenter.web.module.home.vo.TrendChartVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 厅关键数据-趋势图
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RoomKeyDataTrendChartResult {


    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;


    /**
     * 总收入
     */
    private TrendChartVO sumIncome;

    /**
     * 上麦主播数
     */
    private TrendChartVO signUpGuestPlayerCnt;

    /**
     * 考核收入
     */
    private TrendChartVO income;

    /**
     * 人均收入
     */
    private TrendChartVO playerAvgIncome;

    /**
     * 有收入主播数
     */
    private TrendChartVO incomePlayerCnt;
}
