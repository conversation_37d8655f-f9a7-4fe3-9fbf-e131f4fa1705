package fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityClassification;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.ActivityClassificationVO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityClassificationConvert {
    ActivityClassificationConvert I = Mappers.getMapper(ActivityClassificationConvert.class);

    List<ActivityClassificationVO> ResponseActivityClassification2ActivityClassificationVO(List<ResponseActivityClassification> target);

}
