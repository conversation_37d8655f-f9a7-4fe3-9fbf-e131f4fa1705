package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;


import lombok.Data;

import java.util.Date;

@Data
public class GuildRoomDayDetailVo implements IDetailEle{

    /**
     * 日的值 yy-mm-dd
     */
    private Date time;


    private String income;

    private int charm;

    /**
     * 麦序
     */
    private int seatOrder;

    /**
     * 打卡主播数
     */
    private int checkPlayerNumber;

    @Override
    public Date foundTime() {
        return this.time;
    }

    @Override
    public String foundIncome() {
        return this.income;
    }

    @Override
    public Integer foundCharm() {
        return this.charm;
    }

    @Override
    public Integer foundSeatOrder() {
        return this.seatOrder;
    }

    @Override
    public Integer foundHostCnt() {
        return null;
    }

    @Override
    public Integer foundCheckPlayerNumber() {
        return this.checkPlayerNumber;
    }
}
