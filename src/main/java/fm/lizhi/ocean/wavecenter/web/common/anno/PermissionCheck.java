package fm.lizhi.ocean.wavecenter.web.common.anno;


import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限校验注解
 * 只指定passRoleCode：只有passRoleCode的角色可以访问
 * 只指定excludeRoleCode：只有excludeRoleCode的角色不能访问
 * 同时指定：passRoleCode优先级最高
 * <AUTHOR>
 * @date 2024/3/28 17:58
 */
@Target({ElementType.METHOD,ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface PermissionCheck {

    /**
     * 允许访问的角色code
     * @return
     */
    RoleEnum[] passRole() default {};

    /**
     * 不允许访问的角色code
     * @return
     */
    RoleEnum[] excludeRole() default {};

    /**
     * 是否仅本人登录可请求
     * 如果为true, 则授权登录方式无法请求
     * @return
     */
    boolean onlySelfLogin() default false;

}
