package fm.lizhi.ocean.wavecenter.web.module.file.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.FileExportRecordBean;
import fm.lizhi.ocean.wavecenter.api.file.service.FileExportRecordService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.file.convert.FileConvert;
import fm.lizhi.ocean.wavecenter.web.module.file.vo.FileExportRecordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/common/export")
public class FileExportController {

    @Autowired
    private FileExportRecordService fileExportRecordService;
    @Autowired
    private AppConfig appConfig;

    @VerifyUserToken
    @GetMapping("/records")
    public ResultVO<PageVO<FileExportRecordVo>> getExportList(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo
            , @RequestParam(value = "pageSize", defaultValue = "20") int pageSize){

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();

        Result<PageBean<FileExportRecordBean>> exportList = fileExportRecordService.getExportList(appId, userId,
                PageParamBean.builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .build());

        if (RpcResult.isFail(exportList)) {
            return ResultVO.failure();
        }

        List<FileExportRecordVo> voList = FileConvert.I.toVoList(exportList.target().getList());
        for (FileExportRecordVo fileExportRecordVo : voList) {
            fileExportRecordVo.setFilePath(appConfig.getRomeFsDownloadCdn()+fileExportRecordVo.getFilePath());
        }

        return ResultVO.success(PageVO.of(exportList.target().getTotal(), voList));

    }

}
