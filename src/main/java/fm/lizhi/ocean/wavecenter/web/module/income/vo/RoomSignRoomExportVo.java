package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:30
 */
@Data
public class RoomSignRoomExportVo {

    @ExcelProperty(value = "记录时间")
    @DateTimeFormat(DatePattern.NORM_DATETIME_PATTERN)
    private Date date;

    @ExcelProperty(value = "收礼厅")
    private String recRoomName;

    @ExcelProperty(value = "收礼厅ID")
    private String recRoomBand;

    @ExcelProperty(value = "送礼人")
    private String sendUserName;

    @ExcelProperty(value = "送礼人ID")
    private String sendUserBand;

    @ExcelProperty(value = "收礼人")
    private String recUserName;

    @ExcelProperty(value = "收礼人ID")
    private String recUserBand;

    @ExcelProperty(value = "礼物名称")
    private String giftName;

    @ExcelProperty(value = "收入钻")
    private String income;

    @ExcelProperty(value = "魅力值")
    private Integer charm;

    @ExcelProperty(value = "内容")
    private String content;

}
