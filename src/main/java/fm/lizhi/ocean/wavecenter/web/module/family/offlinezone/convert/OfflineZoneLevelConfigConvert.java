package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelConfigBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLevelConfig;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result.ListLevelConfigResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface OfflineZoneLevelConfigConvert {

    @Mapping(target = "appId", source = "appId")
    RequestListLevelConfig toRequestListLevelConfig(Integer appId);

    List<ListLevelConfigResult> toListLevelConfigResults(List<ListLevelConfigBean> levelConfigBeans);
}
