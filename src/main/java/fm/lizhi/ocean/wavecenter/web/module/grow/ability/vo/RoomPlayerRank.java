package fm.lizhi.ocean.wavecenter.web.module.grow.ability.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 厅主播排名
 */
@Data
public class RoomPlayerRank {

    /**
     * 主播id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long playerId;

    /**
     * 主播名称
     */
    private String playerName;

    /**
     * 主播波段号
     */
    private String playerBand;

    /**
     * 主播头像
     */
    private String playerAvatar;

    /**
     * 本周能力分, 对应排序能力项的分数
     */
    private BigDecimal thisWeekValue;
}
