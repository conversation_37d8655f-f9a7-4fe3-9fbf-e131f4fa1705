package fm.lizhi.ocean.wavecenter.web.module.file.convert;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Table;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.FileExportRecordBean;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.DynamicColTable;
import fm.lizhi.ocean.wavecenter.web.module.file.vo.FileExportRecordVo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/22 17:21
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface FileConvert {

    FileConvert I = Mappers.getMapper(FileConvert.class);

    FileExportRecordVo convertFileExportRecord2Vo(FileExportRecordBean bean);

    List<FileExportRecordVo> toVoList(List<FileExportRecordBean> list);

    default List<DynamicColTable.Row<Date>> dayStatsTable2DynamicList(Table<Date, String, String> table, String typeName){
        List<DynamicColTable.Row<Date>> statsList = new ArrayList<>();
        for (Map.Entry<Date, Map<String, String>> dateMapEntry : table.rowMap().entrySet()) {
            Date day = dateMapEntry.getKey();

            DynamicColTable.Row<Date> row = new DynamicColTable.Row<>();
            row.setSortRow(day);
            row.putFreezeCol(DateUtil.format(day, DatePattern.NORM_DATE_PATTERN));
            row.putFreezeCol(typeName);

            Map<String, String> valueMap = dateMapEntry.getValue();
            for (Map.Entry<String, String> entry : valueMap.entrySet()) {
                row.putCol(entry.getKey(), entry.getValue());
            }
            statsList.add(row);
        }
        return statsList;
    }

    default List<DynamicColTable.Row<Date>> hourStatsTable2DynamicList(Table<Date, String, String> table, String typeName){
        List<DynamicColTable.Row<Date>> statsList = new ArrayList<>();
        for (Map.Entry<Date, Map<String, String>> dateMapEntry : table.rowMap().entrySet()) {
            Date startDate = dateMapEntry.getKey();

            DynamicColTable.Row<Date> row = new DynamicColTable.Row<>();
            row.setSortRow(startDate);
            DateTime endTime = DateUtil.offsetHour(startDate, 1);
            String timeStr = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(endTime, "HH:mm");
            row.putFreezeCol(timeStr);
            row.putFreezeCol(typeName);

            Map<String, String> valueMap = dateMapEntry.getValue();
            for (Map.Entry<String, String> entry : valueMap.entrySet()) {
                row.putCol(entry.getKey(), entry.getValue());
            }
            statsList.add(row);
        }
        return statsList;
    }


}
