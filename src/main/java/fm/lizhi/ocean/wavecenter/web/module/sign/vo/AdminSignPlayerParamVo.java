package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/10/5 15:59
 */
@Data
public class AdminSignPlayerParamVo {

    /**
     * 签约记录ID
     */
    @NotNull(message = "签约记录ID不可为空")
    private Long contractId;

    /**
     * 签约状态  同意  不同意
     */
    @NotNull(message = "状态不可为空")
    private String status;

}
