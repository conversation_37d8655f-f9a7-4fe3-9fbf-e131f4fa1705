package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/12 15:52
 */
@Data
public class FlowInfoVO {

    /**
     * 创作者侧流程ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long flowId;

    /**
     * 合同ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

    /**
     * 签约ID
     */
    private String signId;

    /**
     * 流程类型：SIGN=签约，CANCEL=解约
     */
    private String type;

    /**
     * 状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum
     */
    private String status;

    /**
     * 是否本人发起
     */
    private Boolean selfCreate;

    /**
     * 关联类型 WITH_FAMILY=与家族签约 WITH_ROOM=与厅签约
     */
    private String relationType;

}
