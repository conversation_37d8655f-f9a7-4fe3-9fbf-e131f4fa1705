package fm.lizhi.ocean.wavecenter.web.module.home.result;


import com.fasterxml.jackson.annotation.JsonFormat;
import fm.lizhi.ocean.wavecenter.api.home.bean.RoomTrendChartBean;
import fm.lizhi.ocean.wavecenter.web.module.home.vo.RoomTrendChartVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 公会大盘规模监控-趋势图
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GuildMarketMonitorTrendChartResult {


    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;


    /**
     * 厅列表
     */
    private List<RoomTrendChartVO> values;
}
