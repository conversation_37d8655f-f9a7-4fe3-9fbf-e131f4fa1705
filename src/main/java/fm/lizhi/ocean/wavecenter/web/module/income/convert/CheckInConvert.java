package fm.lizhi.ocean.wavecenter.web.module.income.convert;


import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInStatus;
import fm.lizhi.ocean.wavecenter.api.live.constants.HostStatus;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface CheckInConvert {

    CheckInConvert I = Mappers.getMapper(CheckInConvert.class);

    GuildRoomDayStatsResVo convertGuildRoomDayStatsResVo(GuildRoomDayStatsRes res);

    GuildRoomHourStatsResVo convertGuildRoomHourStatsVo(GuildRoomHourStatsRes res);

    GuildRoomDayStatsSummaryVo convertGuildRoomDayStatsSummaryVo(GuildRoomDayStatsSummaryRes res);

    GuildRoomHourStatsSummaryVo convertGuildRoomHourStatsSummaryVo(GuildRoomHourStatsSummaryRes res);

    RoomHourCheckDetailVo convertRoomHourCheckDetailVo(RoomHourCheckDetailRes res);

    RoomDayStatsResVo convertRoomDayStatsResVo(RoomDayStatsRes res);


    @Mappings({
            @Mapping(target = "check", source = "checkStatus", qualifiedByName = "convertCheck"),
            @Mapping(target = "host", source = "hostCnt", qualifiedByName = "convertHost"),
    })
    RoomHourDetailVo convertRoomHourDetailVo(RoomHourDetail detail);

    RoomHourStatsResVo convertRoomHourStatsVo(RoomHourStatsRes res,Long roomId);

    RoomHourStatsSummaryResVo convertRoomHourStatsSummaryVo(RoomHourStatsSummaryRes res);

    RoomDayStatsSummaryResVo convertRoomDayStatsSummaryVo(RoomDayStatsSummaryRes res);

    TimeStatsVo timeStatsBean2Vo(TimeStatsBean bean);

    List<TimeStatsVo> timeStatsBeans2Vos(List<TimeStatsBean> beans);

    TimeStatsSumVo timeStatsSumBean2Vo(TimeStatsSumBean bean);

    @Named("convertHost")
    default boolean convertHost(Integer hostCnt){
        if (hostCnt == null) {
            return false;
        }
        return hostCnt.equals(HostStatus.HOST.getValue());
    }


    @Named("convertCheck")
    default boolean convertCheck(Integer checkStatus){
        if (checkStatus == null) {
            return false;
        }
        return checkStatus.equals(CheckInStatus.CHECKED.getValue());
    }

}
