package fm.lizhi.ocean.wavecenter.web.module.income.vo.export.pp;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/24 17:34
 */
@Data
public class PpRoomSignPlayerIncomeExportVo {

    @ExcelProperty(value = "签约主播")
    private String playerName;

    @ExcelProperty(value = "签约主播ID")
    private String playerBand;

    @ExcelProperty(value = "签约厅收礼魅力值")
    private Integer charm;

    /**
     * 签约厅收礼
     */
    @ExcelProperty(value = "签约厅收礼钻")
    private String signHallIncome;

    /**
     * 个播收礼
     */
    @ExcelProperty(value = "个播收礼钻")
    private String personalHallIncome;

    /**
     * 个播贵族提成
     */
    @ExcelProperty(value = "个播贵族提成钻")
    private String personalNobleIncome;



}
