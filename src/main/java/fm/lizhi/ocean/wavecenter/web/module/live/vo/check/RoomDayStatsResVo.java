package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;


import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class RoomDayStatsResVo implements IDetailList<RoomDayDetailVo>{


    private UserVo player;

    /**
     * 合计
     */
    private RoomDayStatsVo stats;

    /**
     * 明细
     */
    private List<RoomDayDetailVo> detail;

    @Override
    public List<RoomDayDetailVo> foundDetail() {
        return this.detail;
    }
}
