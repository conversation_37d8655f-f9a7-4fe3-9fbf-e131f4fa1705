package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 地图数据厅VO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Accessors(chain = true)
public class MapDataRoomVo {

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 厅主昵称
     */
    private String njName;

    /**
     * 波段号
     */
    private String njBand;

    /**
     * 头像
     */
    private String photo;

    /**
     * 线下主播数
     */
    private Long offlinePlayerCnt;
}
