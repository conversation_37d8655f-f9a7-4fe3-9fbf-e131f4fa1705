package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * 个人收入
 * <AUTHOR>
 */
@Data
public class PlayerIncomeDetailExportVo {

    @ExcelProperty(value = "记录时间")
    @DateTimeFormat(DatePattern.NORM_DATETIME_PATTERN)
    private Date date;

    @ExcelProperty(value = "收入钻")
    private String income;

    @ExcelProperty(value = "内容")
    private String content;

    @ExcelProperty(value = "收入类型")
    private String incomeName;
}
