package fm.lizhi.ocean.wavecenter.web.module.live.vo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * 工会审核记录导出VO
 * <AUTHOR>
 * @date 2024/5/8 10:22
 */
@Data
public class GuildAuditRecordExportVo {

    @ExcelProperty(value = "违规厅ID")
    private String njId;

    @ExcelProperty(value = "违规厅")
    private String roomName;
    
    @ExcelProperty(value = "违规用户ID")
    private String playerBand;
    
    @ExcelProperty(value = "违规用户")
    private String playerNickname;
    
    @ExcelProperty(value = "处罚措施")
    private String op;
    
    @ExcelProperty(value = "违规原因")
    private String reason;
    
    @ExcelProperty(value = "违规时间")
    @DateTimeFormat(DatePattern.NORM_DATETIME_PATTERN)
    private Date insertTime;
    
    @ExcelProperty(value = "违规场景")
    private String scene;
}