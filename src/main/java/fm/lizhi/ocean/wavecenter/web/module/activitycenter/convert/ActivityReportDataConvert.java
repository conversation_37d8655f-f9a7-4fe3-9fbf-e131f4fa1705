package fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.*;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityReportDataConvert {

    ActivityReportDataConvert I = Mappers.getMapper(ActivityReportDataConvert.class);

    ActivityReportDataSummaryVO toActivityReportDataSummaryVO(ActivityReportDataSummaryBean target);

    List<ActivityReportDataDetailVO> toActivityReportDataDetailVO(List<ActivityReportDataDetailBean> target);

    List<ActivityReportDataPlayerVO> toActivityReportDataPlayerVO(List<ActivityReportDataPlayerBean> target);

    List<ActivityReportDataGiftVO> toActivityReportDataGiftVO(List<ActivityReportDataGiftBean> target);

    PageVO<ActivityReportDataPlayerExportVo> convertActivityReportDataPlayerExportVos(PageBean<ActivityReportDataPlayerBean> target);

    PageVO<ActivityReportDataGiftExportVo> convertActivityReportDataGiftExportVos(PageBean<ActivityReportDataGiftBean> target);
}
