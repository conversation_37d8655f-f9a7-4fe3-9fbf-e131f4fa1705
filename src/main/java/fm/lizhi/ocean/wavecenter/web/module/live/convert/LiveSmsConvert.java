package fm.lizhi.ocean.wavecenter.web.module.live.convert;

import fm.lizhi.ocean.wavecenter.api.live.bean.PlayerSmsStatBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomSmsStatBean;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.LiveSmsPlayerExportVo;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.LiveSmsRoomExportVo;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.PlayerSmsStatVo;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.RoomSmsStatVo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 私信分析转换器
 * <AUTHOR>
 * @date 2024/4/20 15:32
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface LiveSmsConvert {

    LiveSmsConvert I = Mappers.getMapper(LiveSmsConvert.class);

    RoomSmsStatVo roomSmsStatBean2Vo(RoomSmsStatBean bean);

    List<RoomSmsStatVo> roomSmsStatBeans2Vos(List<RoomSmsStatBean> beans);

    PlayerSmsStatVo playerSmsStatBean2Vo(PlayerSmsStatBean bean);

    List<PlayerSmsStatVo> playerSmsStatBeans2Vos(List<PlayerSmsStatBean> beans);


    @Mapping(target = "roomName", source = "bean.roomInfo.name")
    @Mapping(target = "njId", source = "bean.roomInfo.id")
    LiveSmsRoomExportVo roomSmsStatBean2ExportVo(RoomSmsStatBean bean);
    List<LiveSmsRoomExportVo> roomSmsStatBeans2ExportVos(List<RoomSmsStatBean> list);


    @Mapping(target = "roomName", source = "bean.roomInfo.name")
    @Mapping(target = "playerName", source = "bean.playerInfo.name")
    @Mapping(target = "playerBand", source = "bean.playerInfo.band")
    @Mapping(target = "njId", source = "bean.roomInfo.id")
    LiveSmsPlayerExportVo playerSmsStatBean2ExportVo(PlayerSmsStatBean bean);
    List<LiveSmsPlayerExportVo> playerSmsStatBeans2ExportVos(List<PlayerSmsStatBean> list);
}
