package fm.lizhi.ocean.wavecenter.web.common.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/15
 */
@Target({ElementType.METHOD,ElementType.TYPE})
@Retention(java.lang.annotation.RetentionPolicy.RUNTIME)
@Inherited
public @interface VerifyUserToken {


    /**
     * 是否是必需要登录
     * true: 没登录，直接返回用户未登录
     * false：没登录，继续执行，userId 为空
     */
    boolean required() default true;
}
