package fm.lizhi.ocean.wavecenter.web.module.common.controller;

import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.common.vo.VersionNotificationConfigVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import java.util.Map;
import java.util.List;

/**
 * 上新提醒
 */
@Slf4j
@RestController
@RequestMapping("/common/versionNotify")
public class VersionGuideController {

    @Autowired
    private AppConfig appConfig;

    /**
     * 获取上新提醒配置
     * @return
     */
    @VerifyUserToken
    @GetMapping("getConfig")
    public ResultVO<VersionNotificationConfigVo> getVersionGuideConfig() {
        String config = appConfig.getVersionGuideConfig();
        VersionNotificationConfigVo vo = new VersionNotificationConfigVo();
        
        // 使用FastJSON将JSON数组字符串解析为List<Map>
        List<Map<String, Object>> configMapList = JSON.parseObject(config, 
            new TypeReference<List<Map<String, Object>>>(){});
        
        // 将解析后的配置设置到VO对象
        vo.setGuideConfigs(configMapList);
        return ResultVO.success(vo);
    }

}
