package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result;

import com.ctrip.framework.apollo.core.enums.Env;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * 等级权益列表结果
 */
@Data
public class ListLevelRightResult {

    /**
     * 权益ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 等级权益名称
     */
    private String name;

    /**
     * 等级权益图片
     */
    private String image;

    /**
     * 部署环境
     *
     * @see Env#name()
     */
    private String deployEnv;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 创建时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * 修改时间, 毫秒时间戳
     */
    private Long modifyTime;

    /**
     * 等级权益介绍列表
     */
    private List<Introduction> introductions;

    /**
     * 等级权益介绍
     */
    @Data
    public static class Introduction {

        /**
         * 介绍ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 介绍标题
         */
        private String title;

        /**
         * 介绍内容
         */
        private String content;

        /**
         * 排序下标，从0开始，越小越靠前
         */
        private Integer index;

        /**
         * 创建时间, 毫秒时间戳
         */
        private Long createTime;

        /**
         * 修改时间, 毫秒时间戳
         */
        private Long modifyTime;
    }
}
