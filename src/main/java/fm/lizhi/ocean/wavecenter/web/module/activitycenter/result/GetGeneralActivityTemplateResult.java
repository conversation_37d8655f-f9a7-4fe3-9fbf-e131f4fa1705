package fm.lizhi.ocean.wavecenter.web.module.activitycenter.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import fm.lizhi.ocean.wavecenter.web.common.serializer.ListLongToListStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.HighlightVO;
import lombok.Data;

import java.util.List;

/**
 * 获取通用活动模板详情结果
 */
@Data
public class GetGeneralActivityTemplateResult {

    /**
     * 活动模板id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 大类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bigClassId;

    /**
     * 大类名称
     */
    private String bigClassName;

    /**
     * 大类分类
     */
    private Integer bigClassType;

    /**
     * 分类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 活动等级
     */
    private String level;

    /**
     * 活动目标
     */
    private String goal;

    /**
     * 活动介绍
     */
    private String introduction;

    /**
     * 活动流程列表
     */
    private List<Process> processes;

    /**
     * 辅助道具图片列表
     */
    private List<String> auxiliaryPropUrls;

    /**
     * 活动海报
     */
    private String posterUrl;

    /**
     * 玩法工具列表
     */
    private List<Integer> activityTools;

    /**
     * 房间公告
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片列表
     */
    private List<String> roomAnnouncementImages;

    /**
     * 房间背景id列表
     */
    @JsonSerialize(using = ListLongToListStringSerializer.class)
    private List<Long> roomBackgroundIds;

    /**
     * 房间背景可选数量限制 -1表示没有限制
     */
    private Integer roomBackgroundLimit;

    /**
     * 房间头像框id列表
     */
    @JsonSerialize(using = ListLongToListStringSerializer.class)
    private List<Long> avatarWidgetIds;

    /**
     * 头像框可选数量限制 -1表示没有限制
     */
    private Integer avatarWidgetLimit;

    /**
     * 活动资源列表
     */
    private List<FlowResource> flowResources;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 活动模板亮点标签列表
     */
    private List<HighlightVO> highlights;

    /**
     * 上下架状态
     *
     * @see ActivityTemplateStatusEnum
     */
    private Integer status;

    /**
     * 活动限时
     */
    private Integer activityDurationLimit;

    /**
     * 活动开始时间限制
     */
    private Long activityStartTimeLimit;

    /**
     * 活动结束时间限制
     */
    private Long activityEndTimeLimit;

    /**
     * 等级id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long levelId;


    /**
     * 房间角标ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long roomMarkId;

    /**
     * 房间角标URL,xm的角标只是一张图片配置，所以没有 ID
     */
    private String roomMarkUrl;

    /**
     * 日程重复类型
     * @see fm.lizhi.ocean.wavecenter.module.api.background.activitycenter.constants.ActivityTemplateRecurrenceEnum
     */
    private Integer recurrenceType;

    /**
     * 当天+选中的小时+选中的分钟 时间戳，服务端提取小时+分钟
     */
    private Long recurrenceStartTime;

    /**
     * 当天+选中的小时+选中的分钟 时间戳，服务端提取小时+分钟
     */
    private Long recurrenceEndTime;

    /**
     * 活动流程
     */
    @Data
    public static class Process {

        /**
         * 环节名称
         */
        private String name;

        /**
         * 时长
         */
        private String duration;

        /**
         * 说明
         */
        private String explanation;
    }

    /**
     * 流量资源图片扩展信息
     */
    @Data
    public static class FlowResourceImageExtra {

        /**
         * 颜色
         */
        private String color;

        /**
         * 宽高比
         */
        private String scale;
    }

    /**
     * 流量资源图片
     */
    @Data
    public static class FlowResourceImage {

        /**
         * 图片地址
         */
        private String imageUrl;

        /**
         * 图片扩展配置
         */
        private FlowResourceImageExtra extra;
    }

    /**
     * 流量资源扩展信息
     */
    @Data
    public static class FlowResourceExtra {

        /**
         * 官频位时长限制, 单位分钟, 当资源类型为官频位时有值
         */
        @Deprecated
        private Integer durationLimit;

        /**
         * 官频位可选座位号列表, 当资源类型为官频位时有值
         */
        private List<Integer> officialSeatNumbers;

        /**
         * 挂件id, 当资源类型为挂件时有值
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long pendantId;
    }

    /**
     * 流量资源
     */
    @Data
    public static class FlowResource {

        /**
         * 资源配置id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long resourceConfigId;

        /**
         * 资源名称
         */
        private String name;

        /**
         * 资源介绍
         */
        private String introduction;

        /**
         * 资源预览图片
         */
        private String imageUrl;

        /**
         * 资源配置类型
         *
         * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants
         */
        private Integer deployType;

        /**
         * 资源是否必选
         */
        private Boolean required;

        /**
         * 资源code，只有自动配置的资源有
         */
        private String resourceCode;

        /**
         * 资源状态
         */
        private Integer resourceStatus;

        /**
         * 资源是否已删除
         */
        private Boolean resourceDeleted;

        /**
         * 资源扩展信息
         */
        private FlowResourceExtra extra;

        /**
         * 资源物料图片列表
         */
        private List<FlowResourceImage> images;
    }

}
