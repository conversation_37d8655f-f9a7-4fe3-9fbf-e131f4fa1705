package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

/**
 * 活动简单信息
 */
@Data
public class ActivitySimpleInfoVO {

    /**
     * 活动ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动报名类型
     */
    private Integer applyType;

    /**
     * 活动审核状态
     */
    private Integer auditStatus;

    /**
     * 活动状态
     */
    private Integer activityStatus;

    /**
     * 活动联系方式
     */
    private String contact;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 活动联系方式号码
     */
    private String contactNumber;

    /**
     * 主持信息
     */
    private UserVo hostInfo;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 活动分类名称
     */
    private String className;

    /**
     * 大分类名称
     */
    private String bigClassName;

    /**
     * 报名用户信息
     */
    private UserVo applyUserInfo;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 活动申请时间
     */
    private Long applyTime;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 活动申请版本
     */
    private Integer version;

}
