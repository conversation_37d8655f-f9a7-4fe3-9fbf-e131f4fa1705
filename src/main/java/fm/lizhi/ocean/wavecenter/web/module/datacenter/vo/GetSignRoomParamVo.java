package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/4/23 15:36
 */
@Data
public class GetSignRoomParamVo {

    @Min(value = 1)
    private Integer pageNo = 1;

    @Min(value = 1)
    @Max(value = 100)
    private Integer pageSize = 20;

    @NotBlank(message = "排序指标为空")
    private String orderMetrics;

    @NotBlank(message = "排序类型为空")
    private String orderType;

    @NotBlank(message = "时间类型为空")
    private String dateType;

    /**
     * 是否过滤
     */
    private Integer filterZero = 1;

    /**
     * YYYY-MM-DD
     */
    @NotBlank(message = "开始时间为空")
    private String startDate;

    private String endDate;

}
