package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestGetOfficialSeatTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseGetOfficialSeatTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOfficialSeatTimeService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityApplyConvert;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityOfficialSeatConvert;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.ActivityGetOptionalOfficialTimeParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.ActivityOptionOfficialTimeResult;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.ActivityOfficialSeatTimeResultVO;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.GetOfficialSeatTimeParamVO;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.OfficialSeatTimeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/activity")
public class ActivityOfficialSeatTimeController {

    @Autowired
    private ActivityOfficialSeatTimeService activityOfficialSeatTimeService;

    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER})
    @GetMapping("/officialSeat/list")
    public ResultVO<ActivityOfficialSeatTimeResultVO> getActivityOfficialSeatTimeList(@Validated GetOfficialSeatTimeParamVO param) {
        RequestGetOfficialSeatTimeBean request = new RequestGetOfficialSeatTimeBean()
                .setSeat(param.getSeat()).setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        if (param.getStartDate() != null) {
            request.setStartDate(new Date(param.getStartDate()));
        }
        if (param.getEndDate() != null) {
            request.setEndDate(new Date(param.getEndDate()));
        }
        Result<ResponseGetOfficialSeatTimeBean> result = activityOfficialSeatTimeService.getOfficialSeatTimeList(request);
        if (RpcResult.isFail(result)) {
            log.warn("get activity official seat time list fail: {}, rCode:{}, param:{}", result.getMessage(), result.rCode(), JsonUtil.dumps(param));
            return ResultVO.failure("查询官频位时间列表失败");
        }

        List<OfficialSeatTimeVO> lists = ActivityApplyConvert.I.convertOfficialSeatBeans2VOList(result.target().getTimeList());
        ActivityOfficialSeatTimeResultVO timeResultVO = new ActivityOfficialSeatTimeResultVO();
        timeResultVO.setTimeInfoList(lists);
        timeResultVO.setMaxOfficialSeatHallCount(result.target().getMaxOfficialSeatHallCount());
        return ResultVO.success(timeResultVO);
    }


    /**
     * 获取可选的时间范围
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER})
    @GetMapping("/getOptionalOfficialTime")
    public ResultVO<ActivityOptionOfficialTimeResult> getOptionalOfficialTime(ActivityGetOptionalOfficialTimeParam param) {
        RequestActivityOptionOfficialTime request = ActivityOfficialSeatConvert.I.toRequestActivityOptionOfficialTime(param);
        Result<ResponseActivityOptionOfficialTime> result = activityOfficialSeatTimeService.getOptionalOfficialTime(request);
        if (RpcResult.isFail(result)) {
            log.warn("获取官频位失败:, rCode:{}", result.rCode());
            //只要有rCode!=0, 就返回空结果
            return ResultVO.success(new ActivityOptionOfficialTimeResult());
        }

        ActivityOptionOfficialTimeResult resp = ActivityOfficialSeatConvert.I.toActivityOptionOfficialTimeResult(result.target());
        return ResultVO.success(resp);
    }
}
