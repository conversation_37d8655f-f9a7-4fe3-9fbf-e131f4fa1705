package fm.lizhi.ocean.wavecenter.web.common.lark;

import com.lark.oapi.Client;
import com.lark.oapi.core.enums.BaseUrlEnum;
import com.lark.oapi.service.bitable.v1.model.AppTableRecord;
import com.lark.oapi.service.bitable.v1.model.CreateAppTableRecordReq;
import com.lark.oapi.service.bitable.v1.model.CreateAppTableRecordResp;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/6/14 20:08
 */
@Slf4j
@Component
public class LarkClient {

    private Client client;

    private AppConfig appConfig;

    @Autowired
    public LarkClient(AppConfig appConfig) {
        this.client = Client.newBuilder(appConfig.getLarkAppId(), appConfig.getLarkAppSecret())
                .openBaseUrl(BaseUrlEnum.FeiShu)
                .requestTimeout(5, TimeUnit.SECONDS)
                .build();
        this.appConfig = appConfig;
    }

    public static void main(String[] args) {
        AppConfig appConfig1 = new AppConfig();
        LarkClient larkClient = new LarkClient(appConfig1);
        larkClient.submitFeedback(new FeedbackLarkDto()
                        .setTypeId(1)
                .setBand("333"));
        System.out.println("finsih");
    }

    /**
     * 提交反馈单
     * @param feedBack
     */
    public void submitFeedback(FeedbackLarkDto feedBack){
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("用户波段号", feedBack.getBand());
        dataMap.put("昵称", feedBack.getNickName());
        dataMap.put("应用名", feedBack.getAppName());
        // 故障问题   功能建议
        if (feedBack.getTypeId() != null) {
            dataMap.put("反馈类型", feedBack.getTypeId()==1?"故障问题":"功能建议");
        }
        dataMap.put("反馈内容", feedBack.getContent());
        if(feedBack.getUserId()!=null){
            dataMap.put("用户ID", String.valueOf(feedBack.getUserId()));
        }
        dataMap.put("反馈日期", System.currentTimeMillis());
        if (feedBack.getStar() != null) {
            dataMap.put("星星数", String.valueOf(feedBack.getStar()));
        }

        String fileList = feedBack.getFileListStr();
        if(StringUtils.isNoneEmpty(fileList)){
            fileList = fileList.replaceAll(",", "\n");
            dataMap.put("附件",fileList);
        }
        dataMap.put("来源", feedBack.getSource());

        submitBitTable(dataMap, appConfig.getFeedbackTableId(), appConfig.getFeedbackAppToken());
    }

    /**
     * 添加多维表格记录
     * @param rowMap
     * @param tableId
     * @param appToken
     */
    public void submitBitTable(Map<String, Object> rowMap, String tableId, String appToken){
        try {
            CreateAppTableRecordResp resp = client.bitable().appTableRecord().create(
                    CreateAppTableRecordReq.newBuilder()
                            .tableId(tableId)
                            .appToken(appToken)
                            .appTableRecord(AppTableRecord.newBuilder()
                                    .fields(rowMap)
                                    .build()
                            )
                            .build()
            );

            if(!resp.success()) {
                log.warn("submitBitTable error code:{},msg:{}},reqId:{}}", resp.getCode(), resp.getMsg(), resp.getRequestId());
            }
        } catch (Exception e) {
            log.warn("submitBitTable exception:", e);
        }

    }

}
