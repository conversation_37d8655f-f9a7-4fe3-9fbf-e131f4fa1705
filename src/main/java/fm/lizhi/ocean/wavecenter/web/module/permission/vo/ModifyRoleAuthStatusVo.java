package fm.lizhi.ocean.wavecenter.web.module.permission.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/4/11 17:56
 */
@Data
public class ModifyRoleAuthStatusVo {

    @NotNull(message = "找不到配置")
    private Long id;

    @NotNull(message = "请确认状态")
    @Max(1)
    @Min(0)
    private Integer status;
}
