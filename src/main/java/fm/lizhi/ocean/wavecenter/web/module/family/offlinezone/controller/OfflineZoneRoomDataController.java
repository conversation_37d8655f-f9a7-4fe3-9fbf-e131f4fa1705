package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneRoomDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetRoomDataList;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneRoomDataService;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert.OfflineZoneConvert;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param.OfflineZoneRoomDataListParam;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.OfflineZoneRoomDataVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 线下专区-厅数据Controller
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@RestController
@RequestMapping("offline/signHallData")
public class OfflineZoneRoomDataController {

    @Autowired
    private OfflineZoneRoomDataService offlineZoneRoomDataService;
    @Autowired
    private DataScopeHandler dataScopeHandler;
    @Autowired
    private OfflineZoneConvert offlineZoneConvert;

    /**
     * 签约厅数据列表
     * @param paramVo 请求参数
     * @return 签约厅数据列表
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @VerifyUserToken
    @GetMapping("/list")
    public ResultVO<PageVO<OfflineZoneRoomDataVo>> list(@Validated OfflineZoneRoomDataListParam paramVo) {
        // 获取当前角色对应的家族ID
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        if (familyId == null) {
            return ResultVO.failure("未找到关联的家族信息");
        }
        // 构建请求参数
        RequestGetRoomDataList request = offlineZoneConvert.buildRoomDataListRequest(
                ContextUtils.getBusinessEvnEnum().getAppId(), paramVo, familyId);

        // 调用Service获取数据
        Result<PageBean<OfflineZoneRoomDataBean>> result = offlineZoneRoomDataService.getRoomDataList(request);
        
        if (RpcResult.isFail(result)) {
            log.error("getRoomDataList,error,request={},rCode={}", request, result.rCode());
            return ResultVO.failure();
        }

        // 转换并返回结果
        return ResultVO.success(offlineZoneConvert.roomDataPageBean2Vo(result.target()));
    }
}
