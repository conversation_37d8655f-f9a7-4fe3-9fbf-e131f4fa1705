package fm.lizhi.ocean.wavecenter.web.module.live.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.platform.api.platform.request.*;
import fm.lizhi.ocean.wave.platform.api.platform.response.ResponseGetCheckInConfig;
import fm.lizhi.ocean.wave.platform.api.platform.service.WaveCheckInManagementService;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.RoomDataService;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInPlayerStatisticBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInRoomStatisticBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserRecordSumBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserSumBean;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInPlayerStatistic;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInPlayerSum;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInRoomSum;
import fm.lizhi.ocean.wavecenter.api.live.response.*;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveService;
import fm.lizhi.ocean.wavecenter.api.live.service.WaveCheckInDataService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignAdminService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.DynamicColTable;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.live.handler.CheckInExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.live.handler.CheckInReportHandler;
import fm.lizhi.ocean.wavecenter.web.module.live.model.converter.CheckInConverter;
import fm.lizhi.ocean.wavecenter.web.module.live.model.param.*;
import fm.lizhi.ocean.wavecenter.web.module.live.model.result.*;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.CheckInHostConfigVo;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserFamilyHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 麦序福利控制器.
 */
@RestController
@RequestMapping("/live/checkIn")
@Slf4j
public class CheckInController {

    @Autowired
    private CheckInConverter checkInConverter;

    @Autowired
    private DataScopeHandler dataScopeHandler;

    @Autowired
    private FileExportHandler fileExportHandler;

    @Autowired
    private UserFamilyHandler userFamilyHandler;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private LiveService liveService;

    @Autowired
    private SignAdminService signAdminService;

    @Autowired
    private WaveCheckInDataService waveCheckInDataService;

    @Autowired
    private WaveCheckInManagementService waveCheckInManagementService;

    @Autowired
    private UserCommonService userCommonService;

    @Autowired
    private CheckInReportHandler checkInReportHandler;

    @Autowired
    private CheckInExportHandler checkInExportHandler;

    @Autowired
    private RoomDataService roomDataService;

    @Autowired
    private AppConfig appConfig;

    /**
     * 保存麦序福利表主持人配置
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @PostMapping("/config/saveHost")
    public ResultVO<Void> saveHost(@RequestBody SaveCheckInHostConfigParam param) {

        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            return ResultVO.failure("请选择日期");
        }
        // 小于当前周不可编辑
        if (DateUtil.endOfDay(DateUtil.parse(param.getEndDate())).getTime() < new Date().getTime()) {
            return ResultVO.failure("历史周不可保存");
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long njId = ContextUtils.getContext().getSubjectId();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        ContextUtils.getContext().addReqLog("`njId={}", njId);

        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }
        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);

        RequestSaveCheckInHostConfig req = checkInConverter.toRequestSaveCheckInHostConfig(param, appId, familyId, roomId);
        Result<Void> result = waveCheckInManagementService.saveCheckInHostConfig(req);
        if (RpcResult.isFail(result)) {
            log.error("saveCheckInHostConfig fail, req: {}, rCode: {}, message: {}", param, result.rCode(), result.getMessage());
            return ResultVO.failure(result.getMessage());
        }
        return ResultVO.success();
    }

    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN, RoleEnum.PLAYER})
    @GetMapping("/config/getHost")
    public ResultVO<GetCheckInHostConfigResult> getHost(
            @RequestParam(value = "startDate") String startDate,
            @RequestParam(value = "endDate") String endDate,
            @RequestParam(value = "njId", required = false) Long reqNjId
    ) {
        // 如果不是厅主请求，需要传njId
        if (!ContextUtils.getContext().isRoom() && reqNjId == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long njId = ContextUtils.getContext().isRoom()? ContextUtils.getContext().getSubjectId() : reqNjId;
        if(familyId == null && njId != null) {
            //获取当前厅主签约的家族
            familyId = userFamilyHandler.getUserSignFamilyId(njId);
        }
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        ContextUtils.getContext().addReqLog("`njId={}", njId);

        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }

        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);

        RequestGetCheckInHostConfig req = new RequestGetCheckInHostConfig();
        req.setAppId(appId);
        req.setFamilyId(familyId);
        req.setRoomId(roomId);
        req.setStartTime(DateUtil.parseDate(startDate));
        req.setEndTime(DateUtil.parseDate(endDate));
        Result<ResponseGetCheckInHostConfig> result = waveCheckInManagementService.getCheckInHostConfig(req);
        if (RpcResult.isFail(result)) {
            log.error("getCheckInHostConfig fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            return ResultVO.failure(result.getMessage());
        }
        ResponseGetCheckInHostConfig resp = result.target();
        GetCheckInHostConfigResult getResult = checkInConverter.toGetCheckInHostConfigResult(resp);

        // 填充用户信息
        if (getResult != null && CollUtil.isNotEmpty(getResult.getHostDetails())) {
            List<Long> hostIds = getResult.getHostDetails().stream().map(CheckInHostConfigVo::getHostId).collect(Collectors.toList());

            Result<List<UserBean>> hostInfoList = userCommonService.getUserByIds(appId, hostIds);
            if (RpcResult.isSuccess(hostInfoList)) {
                Map<Long, UserBean> hostInfoMap = hostInfoList.target().stream().collect(Collectors.toMap(UserBean::getId, Function.identity(), (a, b) -> a));
                getResult.getHostDetails().forEach(host -> {
                    UserBean userBean = hostInfoMap.get(host.getHostId());
                    host.setHostInfo(UserCommonConvert.I.userBean2Vo(userBean));
                });
            }
        }
        return ResultVO.success(getResult);
    }

    /**
     * 保存麦序福利配置
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM})
    @PostMapping("/config/save")
    public ResultVO<Void> saveCheckInConfig(@RequestBody SaveCheckInConfigParam param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        Long njId = ContextUtils.getContext().getSubjectId();
        ContextUtils.getContext().addReqLog("`njId={}", njId);
        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }
        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);

        if (param.getDayMicConfig() != null && (param.getDayMicConfig().getMaxCount() == null || param.getDayMicConfig().getMaxCount() <= 0)) {
            param.getDayMicConfig().setMaxCount(Integer.MAX_VALUE);
        }

        RequestSaveCheckInConfig req = checkInConverter.toRequestSaveCheckInConfig(param, appId, familyId, roomId, njId);
        Result<Void> result = waveCheckInManagementService.saveCheckInConfig(req);
        if (RpcResult.isFail(result)) {
            log.error("saveCheckInConfig fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        log.info("saveCheckInConfig success, req: {}", req);
        return ResultVO.success();
    }

    private Result<Long> getRoomIdByNjId(int appId, Long njId) {
        Result<ResponseGetRoomInfoByNjId> result = liveService.getRoomInfoByNjId(appId, njId);
        if (RpcResult.isFail(result)) {
            if (result.rCode() == LiveService.GET_ROOM_INFO_BY_NJ_ID_NO_EXIST) {
                log.info("getRoomInfoByNjId no exist, appId: {}, njId: {}", appId, njId);
                return RpcResult.fail(LiveService.GET_ROOM_INFO_BY_NJ_ID_NO_EXIST, "找不到厅信息, 该主播可能没有开播过");
            } else {
                log.info("getRoomInfoByNjId fail, appId: {}, njId: {}, rCode: {}, message: {}", appId, njId, result.rCode(), result.getMessage());
                return RpcResult.fail(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR, "获取厅信息失败");
            }
        }
        return RpcResult.success(result.target().getId());
    }

    /**
     * 获取麦序福利配置
     *
     * @param reqNjId 厅主id
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN, RoleEnum.PLAYER})
    @GetMapping("/config/get")
    public ResultVO<GetCheckInConfigResult> getCheckInConfig(@RequestParam(value = "njId", required = false) Long reqNjId) {
        // 如果不是厅主请求，需要传njId
        if (!ContextUtils.getContext().isRoom() && reqNjId == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long njId = ContextUtils.getContext().isRoom() ? ContextUtils.getContext().getSubjectId() : reqNjId;
        if(familyId == null && njId != null) {
            //获取当前厅主签约的家族
            familyId = userFamilyHandler.getUserSignFamilyId(njId);
        }
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        ContextUtils.getContext().addReqLog("`njId={}", njId);
        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }
        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        RequestGetCheckInConfig req = checkInConverter.toRequestGetCheckInConfig(appId, familyId, roomId);
        Result<ResponseGetCheckInConfig> result = waveCheckInManagementService.getCheckInConfig(req);
        if (RpcResult.isFail(result)) {
            log.error("getCheckInConfig fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        ResponseGetCheckInConfig resp = result.target();
        log.info("getCheckInConfig success, resp: {}", resp);

        List<UserVo> managerList = new ArrayList<>();
        if (CollUtil.isNotEmpty(resp.getCheckInManagerConfig())){
            List<UserBean> managerBeanList = userHandler.getUserByIds(resp.getCheckInManagerConfig());
            managerList = checkInConverter.userBeans2Vos(managerBeanList);
        }

        return ResultVO.success(checkInConverter.toGetCheckInConfigResult(resp, managerList));
    }

    /**
     * 获取麦序福利厅汇总.
     * <ul>
     *     <li>厅主视角: 不限家族, 当前厅, 的数据</li>
     *     <li>家族长视角: 当前家族, 选中厅, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN, RoleEnum.PLAYER})
    @GetMapping("/room/sum")
    public ResultVO<GetCheckInRoomSumResult> getCheckInRoomSum(GetCheckInRoomSumParam param) {
        // 如果不是厅主请求，需要传njId
        if (!ContextUtils.getContext().isRoom() && param.getNjId() == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long njId = ContextUtils.getContext().isRoom() ? ContextUtils.getContext().getSubjectId() : param.getNjId();
        ContextUtils.getContext().addReqLog("`njId={}`startDate={}`endDate={}", njId, param.getStartDate(), param.getEndDate());
        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }
        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        // 注意如果加高级管理角色, subjectId 也是familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        // 	签约主播数
        Integer signPlayerCnt = getSignPlayerCnt(appId, njId);
        if (signPlayerCnt == null) {
            return ResultVO.failure("获取厅签约信息失败");
        }
        Result<Integer> playerPayCntResult = roomDataService.getPlayerPayCountInCurrentFamily(appId, njId, param.getStartDate(), param.getEndDate());
        int playerPayCnt = 0;
        if(playerPayCntResult.rCode() == 0) {
            playerPayCnt = playerPayCntResult.target();
        }
        // 其他数据
        RequestGetCheckInRoomSum req = checkInConverter.toRequestGetCheckInRoomSum(param, appId, roomId, familyId);
        Result<ResponseGetCheckInRoomSum> result = waveCheckInDataService.getCheckInRoomSum(req);
        if (RpcResult.isFail(result)) {
            log.error("getCheckInRoomSum fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        ResponseGetCheckInRoomSum resp = result.target();
        // 合并结果
        GetCheckInRoomSumResult getResult = checkInConverter.toGetCheckInRoomSumResult(resp, signPlayerCnt, playerPayCnt);
        log.info("getCheckInRoomSum success, result: {}", getResult);
        return ResultVO.success(getResult);
    }

    private Integer getSignPlayerCnt(int appId, Long njId) {
        Result<Integer> result = signAdminService.countSignPlayerNum(appId, njId);
        if (RpcResult.isFail(result)) {
            log.info("countSignPlayerNum fail, appId: {}, njId: {}, rCode: {}, message: {}", appId, njId, result.rCode(), result.getMessage());
            return null;
        }
        return result.target();
    }

    /**
     * 获取麦序福利厅详情
     * <ul>
     *     <li>厅主视角: 不限家族, 当前厅, 的数据</li>
     *     <li>家族长视角: 当前家族, 选中厅, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN, RoleEnum.PLAYER})
    @GetMapping("/room/detail")
    public ResultVO<GetCheckInRoomDetailResult> getCheckInRoomDetail(GetCheckInRoomDetailParam param) {
        // 如果不是厅主请求，需要传njId
        if (!ContextUtils.getContext().isRoom() && param.getNjId() == null) {
            return ResultVO.failure("请选择厅");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long njId = ContextUtils.getContext().isRoom() ? ContextUtils.getContext().getSubjectId() : param.getNjId();
        ContextUtils.getContext().addReqLog("`njId={}`dateType={}`startDate={}`endDate={}",
                njId, param.getDateType(), param.getStartDate(), param.getEndDate());
        Result<Long> roomIdByNjId = getRoomIdByNjId(appId, njId);
        if (RpcResult.isFail(roomIdByNjId)) {
            return ResultVO.failure(roomIdByNjId.getMessage());
        }
        Long roomId = roomIdByNjId.target();
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        // 注意如果加高级管理角色, subjectId 也是familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        RequestGetCheckInRoomStatistic req = checkInConverter.toRequestGetCheckInRoomStatistic(param, appId, roomId, familyId);
        Result<ResponseGetCheckInRoomStatistic> result = waveCheckInDataService.getCheckInRoomStatistic(req);
        if (RpcResult.isFail(result)) {
            log.error("getCheckInRoomDetail fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        ResponseGetCheckInRoomStatistic resp = result.target();
        if (log.isDebugEnabled()) {
            log.debug("getCheckInRoomDetail success, resp: {}", JsonUtil.dumps(resp));
        }
        GetCheckInRoomDetailResult vo = checkInConverter.toGetCheckInRoomDetailResult(resp);
        // 计算行合计
        WaveCheckInRoomStatisticBean sum = checkInExportHandler.sumWaveCheckInRoomStatisticList(resp.getList());
        vo.setDetailSum(checkInConverter.waveCheckInRoomStatisticBean2Statistic(sum));
        vo.setHostInfo(checkInConverter.waveCheckInUserBeanToUser(result.target().getHost()));
        return ResultVO.success(vo);
    }


    /**
     * 导出麦序福利厅明细
     * <ul>
     *     <li>厅主视角: 不限家族, 当前厅, 的数据</li>
     *     <li>家族长视角: 当前家族, 选中厅, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN, RoleEnum.PLAYER})
    @GetMapping("/room/export")
    public ResultVO<Void> exportCheckInRoomDetail(@Validated ExportCheckInRoomDetailParam param) {
        ContextUtils.getContext().addReqLog("`exportScore={}`dateType={}`startDate={}`endDate={}",
                param.getExportScore(), param.getDateType(), param.getStartDate(), param.getEndDate());
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        // 导出当前厅, 如果不是厅主角色必须传njId
        boolean exportCurrent = Objects.equals(param.getExportScore(), ExportCheckInRoomDetailParam.ExportScoreEnum.CURRENT);
        if (exportCurrent && !ContextUtils.getContext().isRoom() && param.getNjId() == null) {
            return ResultVO.failure("请选择厅");
        }
        // 注意如果加高级管理角色, 需要传familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        // njId -> UserBean
        TreeMap<Long, UserBean> njIdUserBeanMap;
        if (ContextUtils.getContext().isRoom() || exportCurrent) {
            long njId = ContextUtils.getContext().isRoom() ? ContextUtils.getContext().getSubjectId() : param.getNjId();
            njIdUserBeanMap = getSingleUserBeanMapByUserId(appId, njId);
        } else if (ContextUtils.getContext().isFamilyAdmin()) {
            // 高级管理，只导出权限内的厅
            List<Long> njIds = ContextUtils.getContext().getRoomResource();
            List<UserBean> njBeans = userHandler.getUserByIds(njIds);
            njIdUserBeanMap = njBeans.stream().collect(Collectors.toMap(UserBean::getId, v->v, (v1, v2) -> v1, TreeMap::new));
        } else {
            Validate.notNull(familyId, "家族ID不能为空");
            njIdUserBeanMap = getNjUserBeanMapByFamilyId(familyId);
        }
        ContextUtils.getContext().addReqLog("`njIds={}", njIdUserBeanMap.keySet());
        if (MapUtils.isEmpty(njIdUserBeanMap)) {
            return ResultVO.failure("获取厅信息失败");
        }
        // 文件名
        String fileName;
        if ((ContextUtils.getContext().isFamily() || ContextUtils.getContext().isFamilyAdmin()) && !exportCurrent) {
            // 导出全部厅, 文件名包含家族名
            FamilyBean familyBean = userFamilyHandler.getFamilyById(ContextUtils.getContext().getSubjectId());
            if (familyBean == null) {
                return ResultVO.failure("获取家族信息失败");
            }
            fileName = checkInExportHandler.sanitizeFamilyFileName(familyBean);
        } else {
            // 导出当前厅, 文件名包含厅名
            UserBean userBean = njIdUserBeanMap.entrySet().iterator().next().getValue();
            fileName = checkInExportHandler.sanitizeRoomFileName(userBean);
        }
        fileName = checkInExportHandler.appendDateTypeToFileName(fileName, param.getDateType());

        // sheetId -> param
        TreeMap<Long, ExportCheckInRoomSheetParam> sheetIdParamMap = checkInExportHandler.getExportCheckInRoomSheetParamMap(param, appId, njIdUserBeanMap);

        // sheet
        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.clearSheet();
        for (Map.Entry<Long, ExportCheckInRoomSheetParam> entry : sheetIdParamMap.entrySet()) {
            dynamicColTable.addSheet(entry.getValue().getSheetName(), entry.getKey());
        }
        dynamicColTable.putCol("昵称");
        if (param.getDateType().equals(CheckInDateTypeEnum.WEEK) || param.getDateType().equals(CheckInDateTypeEnum.DAY)){
            dynamicColTable.putCol("ID");
        }

        return fileExportHandler.exportDynamicFile(fileName, dynamicColTable, (sheet, pageNo, pageSize) -> {

            ExportCheckInRoomSheetParam sheetParam = sheetIdParamMap.get(sheet.getId());
            if (sheetParam.getDateType().equals(CheckInDateTypeEnum.WEEK)){
                return checkInExportHandler.buildExportCheckInRoomDetailRowsByWeek(sheetParam, familyId);
            }else if (sheetParam.getDateType().equals(CheckInDateTypeEnum.DAY)){
                return checkInExportHandler.buildExportCheckInRoomDetailRowsByDay(sheetParam, familyId);
            }else if (sheetParam.getDateType().equals(CheckInDateTypeEnum.HOUR)){
                return checkInExportHandler.buildExportCheckInRoomDetailRowsByHour(sheetParam, familyId);
            }else {
                return PageVO.empty();
            }
        });
    }


    private TreeMap<Long, UserBean> getNjUserBeanMapByFamilyId(long familyId) {
        // njId -> UserBean
        TreeMap<Long, UserBean> njIdUserBeanMap = new TreeMap<>();
        List<RoomSignBean> roomSignBeans = userFamilyHandler.getGuildAllRooms(familyId);
        for (RoomSignBean roomSignBean : roomSignBeans) {
            njIdUserBeanMap.put(roomSignBean.getId(), roomSignBean);
        }
        return njIdUserBeanMap;
    }

    private TreeMap<Long, UserBean> getSingleUserBeanMapByUserId(int appId, long userId) {
        // userId -> UserBean
        TreeMap<Long, UserBean> userIdUserBeanMap = new TreeMap<>();
        UserBean userBean = userHandler.getUserById(appId, userId);
        if (userBean != null) {
            userIdUserBeanMap.put(userBean.getId(), userBean);
        }
        return userIdUserBeanMap;
    }

    private UserBean getUserBeanByUserId(int appId, long userId) {
        return userHandler.getUserById(appId, userId);
    }



    /**
     * 获取麦序福利主播汇总数据
     * <ul>
     *     <li>主播视角: 不限家族, 不限厅, 当前主播, 的数据</li>
     *     <li>厅主视角: 不限家族, 当前厅, 选中主播, 的数据</li>
     *     <li>家族长视角: 当前家族, 不限厅, 选中主播, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.PLAYER, RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/player/sum")
    public ResultVO<ResponseGetCheckInPlayerSum> getCheckInPlayerSum(GetCheckInPlayerSumParam param) {
        // 如果不是主播请求, 需要传playerId
        if (!ContextUtils.getContext().isPlayer() && param.getPlayerId() == null) {
            return ResultVO.failure("请选择主播");
        }
        Long playerId = ContextUtils.getContext().isPlayer() ? ContextUtils.getContext().getSubjectId() : param.getPlayerId();
        ContextUtils.getContext().addReqLog("`playerId={}`startDate={}`endDate={}", playerId, param.getStartDate(), param.getEndDate());
        // 注意如果加高级管理角色, 需要传familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId;
        if (ContextUtils.getContext().isRoom()) {
            Result<Long> roomIdByNjId = getRoomIdByNjId(appId, ContextUtils.getContext().getSubjectId());
            if (RpcResult.isFail(roomIdByNjId)) {
                return ResultVO.failure(roomIdByNjId.getMessage());
            }
            roomId = roomIdByNjId.target();
        } else {
            roomId = null;
        }
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        RequestGetCheckInPlayerSum req = checkInConverter.toRequestGetCheckInPlayerSum(param, appId, playerId, familyId, roomId);
        Result<ResponseGetCheckInPlayerSum> result = waveCheckInDataService.getCheckInPlayerSum(req);
        if (RpcResult.isFail(result)) {
            log.info("getCheckInPlayerSum fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        ResponseGetCheckInPlayerSum resp = result.target();
        log.info("getCheckInPlayerSum success, resp: {}", resp);
        return ResultVO.success(resp);
    }

    /**
     * 获取麦序福利主播明细数据
     * <ul>
     *     <li>主播视角: 不限家族, 不限厅, 当前主播, 的数据</li>
     *     <li>厅主视角: 不限家族, 当前厅, 选中主播, 的数据</li>
     *     <li>家族长视角: 当前家族, 不限厅, 选中主播, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.PLAYER, RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/player/detail")
    public ResultVO<GetCheckInPlayerDetailResult> getCheckInPlayerDetail(GetCheckInPlayerDetailParam param) {
        // 如果不是主播请求, 需要传playerId
        if (!ContextUtils.getContext().isPlayer() && param.getPlayerId() == null) {
            return ResultVO.failure("请选择主播");
        }
        Long playerId = ContextUtils.getContext().isPlayer() ? ContextUtils.getContext().getSubjectId() : param.getPlayerId();
        ContextUtils.getContext().addReqLog("`playerId={}`startDate={}`endDate={}",
                playerId, param.getStartDate(), param.getEndDate());
        // 注意如果加高级管理角色, 需要传familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId;
        if (ContextUtils.getContext().isRoom()) {
            Result<Long> roomIdByNjId = getRoomIdByNjId(appId, ContextUtils.getContext().getSubjectId());
            if (RpcResult.isFail(roomIdByNjId)) {
                return ResultVO.failure(roomIdByNjId.getMessage());
            }
            roomId = roomIdByNjId.target();
        } else {
            roomId = null;
        }
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        RequestGetCheckInPlayerStatistic req = checkInConverter.toRequestGetCheckInPlayerStatistic(param, appId, playerId, familyId, roomId);
        Result<ResponseGetCheckInPlayerStatistic> result = waveCheckInDataService.getCheckInPlayerStatistic(req);
        if (RpcResult.isFail(result)) {
            log.info("getCheckInPlayerDetail fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            if (result.rCode() == CommonService.PARAM_ERROR) {
                return ResultVO.failure(result.getMessage());
            } else {
                return ResultVO.failure();
            }
        }
        ResponseGetCheckInPlayerStatistic resp = result.target();
        log.debug("getCheckInPlayerDetail success, resp: {}", resp);
        return ResultVO.success(checkInConverter.toGetCheckInPlayerDetailResult(resp));
    }

    /**
     * 导出麦序福利主播明细
     * <ul>
     *     <li>主播视角: 不限家族, 不限厅, 当前主播, 的数据</li>
     *     <li>厅主视角: 不限家族, 当前厅, 选中主播, 的数据</li>
     *     <li>家族长视角: 当前家族, 不限厅, 选中主播, 的数据</li>
     * </ul>
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.PLAYER, RoleEnum.ROOM, RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("/player/export")
    public ResultVO<Void> exportCheckInPlayerDetail(@Validated ExportCheckInPlayerDetailParam param) {
        // 如果不是主播请求, 需要传playerId
        if (!ContextUtils.getContext().isPlayer() && param.getPlayerId() == null) {
            return ResultVO.failure("请选择主播");
        }
        Long playerId = ContextUtils.getContext().isPlayer() ? ContextUtils.getContext().getSubjectId() : param.getPlayerId();
        ContextUtils.getContext().addReqLog("`playerId={}`startDate={}`endDate={}",
                playerId, param.getStartDate(), param.getEndDate());
        // 注意如果加高级管理角色, 需要传familyId
        Long familyId = dataScopeHandler.getFamilyForFamily();
        ContextUtils.getContext().addReqLog("`familyId={}", familyId);
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long roomId;
        if (ContextUtils.getContext().isRoom()) {
            Result<Long> roomIdByNjId = getRoomIdByNjId(appId, ContextUtils.getContext().getSubjectId());
            if (RpcResult.isFail(roomIdByNjId)) {
                return ResultVO.failure(roomIdByNjId.getMessage());
            }
            roomId = roomIdByNjId.target();
        } else {
            roomId = null;
        }
        ContextUtils.getContext().addReqLog("`roomId={}", roomId);
        UserBean playerUserBean = getUserBeanByUserId(appId, playerId);
        if (playerUserBean == null) {
            return ResultVO.failure("获取主播信息失败");
        }
        String fileName = checkInExportHandler.sanitizePlayerFileName(playerUserBean);
        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.clearSheet();
        long charmSheetId = 0L;
        String charmSheetName = checkInExportHandler.sanitizePlayerCharmSheetName(playerUserBean);
        dynamicColTable.addSheet(charmSheetName, charmSheetId);
        dynamicColTable.putCol("日期/时间");
        return fileExportHandler.exportDynamicFile(fileName, dynamicColTable, (sheet, pageNo, pageSize) -> {
            RequestGetCheckInPlayerStatistic req = checkInConverter.toRequestGetCheckInPlayerStatistic(param, appId, familyId, roomId, playerId);
            Result<ResponseGetCheckInPlayerStatistic> result = waveCheckInDataService.getCheckInPlayerStatistic(req);
            if (RpcResult.isFail(result)) {
                log.warn("getCheckInPlayerStatistic fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
                return PageVO.empty();
            }
            List<WaveCheckInPlayerStatisticBean> list = result.target().getList();
            // 构建行数据
            List<DynamicColTable.Row<Object>> rows = new ArrayList<>();
            for (WaveCheckInPlayerStatisticBean statisticBean : list) {
                DynamicColTable.Row<Object> row = new DynamicColTable.Row<>();
                // 日期/时间
                row.putFreezeCol(Objects.toString(statisticBean.getDate(), StringUtils.EMPTY));
                List<WaveCheckInUserRecordSumBean> detail = statisticBean.getDetail();
                WaveCheckInUserSumBean sum = statisticBean.getSum();
                if (Objects.equals(sheet.getId(), charmSheetId)) {
                    // 魅力值收入明细
                    checkInExportHandler.appendCharmDetailColumns(row, detail, param.getMetrics());
                    // 其他统计字段
                    checkInExportHandler.appendCharmStatisticSum(row, sum, true, param.getMetrics());
                }
                rows.add(row);
            }
            return PageVO.of(rows.size(), rows);
        });
    }

    @GetMapping("/room/report")
    public ResultVO<GetCheckInRoomReportResult> getCheckInRoomReport(GetCheckInRoomReportParam param) {
        String ip = ContextUtils.getContext().getHeader().getIp();
        if(checkInReportHandler.isLimit(param.getAppId(), ip)) {
            return ResultVO.failure("请求频繁");
        }
        //签名生成在lz-ocean-wave-center 先上线 下次一定
        if(!checkInReportHandler.verifySign(param)) {
            return ResultVO.failure("签名校验失败");
        }
        GetCheckInRoomReportResult result = checkInReportHandler.getCheckInRoomReportResult(param);
        return ResultVO.success(result);
    }

}
