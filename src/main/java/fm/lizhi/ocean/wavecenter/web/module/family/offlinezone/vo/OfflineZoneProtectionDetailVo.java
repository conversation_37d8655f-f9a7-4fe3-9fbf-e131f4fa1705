package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * 跳槽保护-获取协议内容响应VO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class OfflineZoneProtectionDetailVo {

    /**
     * 协议ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 协议生效开始时间（时间戳）
     */
    private Long agreementStartTime;

    /**
     * 协议生效结束时间（时间戳）
     */
    private Long agreementEndTime;

    /**
     * 协议文件列表
     */
    private List<AgreementFileVo> agreementFile;

    /**
     * 协议更新时间（时间戳）
     */
    private Long modifyTime;

    /**
     * 失效时间戳
     */
    private Long invalidTimestamp;

    /**
     * 主播同意状态：-1-未处理，0-不同意，1-同意
     */
    private Integer playerAgree;

    /**
     * 是否归档：0-否，1-是
     */
    private Boolean archived;


    /**
     * 保护状态
     */
    private Integer protectionStatus;
}
