package fm.lizhi.ocean.wavecenter.web.module.user.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLastLevelBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLastLevel;
import fm.lizhi.ocean.wavecenter.api.grow.level.service.FamilyLevelService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/28 17:22
 */
@Component
public class UserFamilyHandler {

    private static final Logger log = LoggerFactory.getLogger(UserFamilyHandler.class);
    @Autowired
    private UserFamilyService userFamilyService;
    @Autowired
    private UserCommonService userCommonService;
    @Autowired
    private FamilyLevelService familyLevelService;

    /**
     * 查询家族长用户ID
     * @param familyId
     * @return
     */
    public Long getFamilyUserId(long familyId){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<FamilyBean> result = userFamilyService.getFamily(appId, familyId);
        if (RpcResult.isFail(result)) {
            ContextUtils.getContext().addResLog("family not found");
            return null;
        }
        return result.target().getUserId();
    }

    public Long getUserFamilyId(long userId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<Long> result = userFamilyService.getUserFamilyId(appId, userId);
        if (RpcResult.isFail(result)) {
            ContextUtils.getContext().addResLog("rpcRCode={}", result.rCode());
            return null;
        }
        Long familyId = result.target();
        if (familyId == null) {
            ContextUtils.getContext().addResLog("familyId is null");
        }
        return familyId;
    }

    /**
     * 获取用户当前签约的家族ID
     * @param userId
     * @return
     */
    public Long getUserSignFamilyId(long userId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<UserInFamilyBean> result = userFamilyService.getUserInFamily(appId, userId);
        if (RpcResult.isFail(result)) {
            ContextUtils.getContext().addResLog("rpcRCode={}", result.rCode());
            return null;
        }
        Long familyId = result.target().getFamilyId();
        if (familyId == null) {
            ContextUtils.getContext().addResLog("familyId is null");
        }
        return familyId;
    }

    /**
     * 获取陪玩当前签约的厅
     * @param playerId
     * @return
     */
    public Long getPlayerSignRoom(long playerId){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<UserInFamilyBean> result = userFamilyService.getUserInFamily(appId, playerId);
        if (RpcResult.isFail(result)) {
            ContextUtils.getContext().addResLog("rpcRCode={}", result.rCode());
            return null;
        }
        Long roomId = result.target().getNjId();
        if (roomId == null) {
            ContextUtils.getContext().addResLog("roomId is null");
        }
        return roomId;
    }

    /**
     * 查询陪玩在公会下最近的签约厅，不管是否已经解约
     * @param familyId
     * @param playerId
     * @return
     */
    public Long getPlayerLastRoom(Long familyId, Long playerId){
        if (familyId == null || playerId == null) {
            return null;
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<Long> result = userFamilyService.getPlayerLastRoom(appId, familyId, playerId);
        if (RpcResult.isFail(result)) {
            log.error("getPlayerLastRoom fail. familyId={},playerId={},rpcRCode={}", familyId, playerId, result.rCode());
            return null;
        }
        return result.target();
    }

    /**
     * 查询公会下所有厅ID
     * @param familyId
     * @return
     */
    public List<RoomSignBean> getGuildAllRooms(Long familyId){
        if (familyId == null) {
            return Collections.emptyList();
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        Result<PageBean<RoomSignBean>> result = userCommonService.getAllGuildRooms(appId, familyId, 1, 5000);
        if (RpcResult.isFail(result)) {
            log.warn("getGuildAllRoomIds fail. familyId={}, appId={}, rCode={}", familyId, appId, result.rCode());
            return Collections.emptyList();
        }

        //结果转换
        return result.target().getList();
    }

    /**
     * 根据家族ID获取家族信息
     *
     * @param familyId 家族ID
     * @return 家族信息
     */
    public FamilyBean getFamilyById(long familyId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<FamilyBean> result = userFamilyService.getFamily(appId, familyId);
        if (RpcResult.isFail(result)) {
            log.warn("getFamilyById fail. familyId={}, appId={}, rCode={}", familyId, appId, result.rCode());
            return null;
        }
        return result.target();
    }

    /**
     * 获取用户家族等级
     * @param userId
     * @return
     */
    public Optional<Long> getUserFamilyLevel(Long userId){
        Long familyId = getUserFamilyId(userId);
        if (familyId == null) {
            return Optional.empty();
        }

        Result<FamilyLastLevelBean> result = familyLevelService.getFamilyLastLevel(new RequestGetFamilyLastLevel()
                .setFamilyId(familyId)
                .setAppId(ContextUtils.getBusinessEvnEnum().getAppId()));
        if (RpcResult.isSuccess(result)) {
            return Optional.of(result.target().getLevelId());
        }
        return Optional.empty();
    }
}
