package fm.lizhi.ocean.wavecenter.web.module.live.convert;

import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 14:14
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface CheckConvert {

    CheckConvert I = Mappers.getMapper(CheckConvert.class);

    @Mappings({
            @Mapping(source = "day", target = "time")
    })
    PlayerCheckDayStatsVo playerDayStatsBean2Vo(PlayerCheckDayStatsBean bean);

    List<PlayerCheckDayStatsVo> playerDayStatsBeans2Vos(List<PlayerCheckDayStatsBean> beans);

    @Mappings({
            @Mapping(source = "income", target = "income", qualifiedByName = "convertIncome")
    })
    PlayerDayStatsExportVo playerDayStatsBean2Export(PlayerCheckDayStatsBean bean);

    List<PlayerDayStatsExportVo> playerDayStatsBeans2Exports(List<PlayerCheckDayStatsBean> beans);

    @Named("convertIncome")
    default String convertIncome(BigDecimal income){
        String valueStr = "";
        if (income == null) {
            valueStr = CalculateUtil.formatDecimal("0");
        } else {
            valueStr = CalculateUtil.formatDecimal(income);
        }
        return valueStr;
    }

    @Mappings({
            @Mapping(source = "day", target = "time")
    })
    PlayerCheckHourStatsDayVo playerHourStatsDayBean2Vo(PlayerCheckHourStatsDayBean bean);

    List<PlayerCheckHourStatsDayVo> playerHourStatsDayBeans2Vos(List<PlayerCheckHourStatsDayBean> beans);

    PlayerCheckHourStatsVo playerHourStatsBean2Vo(PlayerCheckHourStatsBean bean);

    List<PlayerCheckHourStatsVo> playerHourStatsBeans2Vos(List<PlayerCheckHourStatsBean> beans);

    CPCSumVo playerStatsSumBean2Vo(PlayerCheckStatsSumBean bean);


    @Mappings({
            @Mapping(source = "playerNumber", target = "checkPlayerNumber")
    })
    RoomDayCalendarStatsVo convertRoomDayCalendarStatsVo(RoomDayCalendarStatsRes stats);

    RoomDayCalendarVo convertRoomDayCalendarVo(RoomDayCalendarRes res);

}
