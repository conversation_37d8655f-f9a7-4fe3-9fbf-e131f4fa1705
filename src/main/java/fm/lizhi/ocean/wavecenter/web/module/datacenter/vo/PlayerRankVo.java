package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import fm.lizhi.ocean.wavecenter.web.common.BigDecimalDownSerializer;
import fm.lizhi.ocean.wavecenter.web.common.RateSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/22 21:49
 */
@Data
public class PlayerRankVo {

    private UserVo playerInfo;

    private UserVo roomInfo;

    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal income;

    /**
     * 公会魅力值	公会考核期间总魅力值，单位：魅力值
     */
    private Integer charm;

    /**
     * 上麦时长(分钟)
     */
    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal upGuestDur;

    /**
     * 送礼人数
     */
    private Integer giftUserCnt;

    /**
     * 送礼客单价
     */
    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal giftUserPrice;

    /**
     * 私信人数
     */
    private Integer chatUserCnt;

    /**
     * 私信回复人数
     */
    private Integer replyChatUserCnt;

    /**
     * 私信进房人数
     */
    private Integer chatEnterRoomUserCnt;

    /**
     * 私信付费人数
     */
    private Integer chatGiftUserCnt;

    /**
     * 私信回复率
     */
    @JsonSerialize(using = RateSerializer.class)
    private BigDecimal replyChatRate;

    /**
     * 私信进房率
     */
    @JsonSerialize(using = RateSerializer.class)
    private BigDecimal chatEnterRoomRate;

    /**
     * 私信付费率
     */
    @JsonSerialize(using = RateSerializer.class)
    private BigDecimal chatGiftRate;

    /**
     * 邀请人数
     */
    private Integer inviteUserCnt;

    /**
     * 邀请进房人数
     */
    private Integer inviteEnterRoomUserCnt;

    /**
     * 邀请付费人数
     */
    private Integer inviteGiftUserCnt;

    /**
     * 邀请进房率
     */
    @JsonSerialize(using = RateSerializer.class)
    private BigDecimal inviteEnterRoomRate;

    /**
     * 邀请付费率
     */
    @JsonSerialize(using = RateSerializer.class)
    private BigDecimal inviteGiftRate;

    /**
     * 主播粉丝数
     */
    private Integer fansUserCnt;

    /**
     * 新增粉丝数
     */
    private Integer newFansUserCnt;

    /**
     * 粉丝送礼收入
     */
    private Integer fansGiftIncome;

    /**
     * 粉丝送礼人数
     */
    private Integer fansGiftUserCnt;

    /**
     * 粉丝送礼客单价
     */
    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal fansGiftUserPrice;

    /**
     * 主播签约厅的主播数
     */
    private Integer roomSignPlayerCnt;

    /**
     * 主播的收入在厅的排名
     */
    private Integer incomeRankRoom;

    /**
     * 主播的收入在工会的排名
     */
    private Integer incomeRankFamily;

    /**
     * 主播的钻石数在厅的排名
     */
    private Integer charmRankRoom;

    /**
     * 主播的钻石数在工会的排名
     */
    private Integer charmRankFamily;

    /**
     * 签约厅收礼收入
     */
    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal signHallIncome;

    /**
     * 官方厅收礼收入
     */
    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal officialHallIncome;

    /**
     * 个播收礼收入
     */
    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal personalHallIncome;
}
