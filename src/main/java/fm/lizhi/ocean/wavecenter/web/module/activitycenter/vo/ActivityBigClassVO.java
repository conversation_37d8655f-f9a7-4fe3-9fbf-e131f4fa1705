package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

@Data
public class ActivityBigClassVO {

    /**
     * 大类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 大类名称
     */
    private String name;

}
