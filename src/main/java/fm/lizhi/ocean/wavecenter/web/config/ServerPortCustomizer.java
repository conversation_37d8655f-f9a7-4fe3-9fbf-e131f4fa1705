package fm.lizhi.ocean.wavecenter.web.config;

import org.springframework.boot.web.server.ConfigurableWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 配置端口号
 *
 * <AUTHOR> generator
 */
@Component
public class ServerPortCustomizer implements WebServerFactoryCustomizer<ConfigurableWebServerFactory> {

    @Resource
    private AppConfig config;

    @Override
    public void customize(ConfigurableWebServerFactory factory) {
        factory.setPort(config.getServerPort());
    }

}