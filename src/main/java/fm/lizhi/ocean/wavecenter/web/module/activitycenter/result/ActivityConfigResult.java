package fm.lizhi.ocean.wavecenter.web.module.activitycenter.result;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ApplyTimeConfigBean;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.ApplyTimeConfigVO;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ActivityConfigResult {

    private ApplyTimeConfigVO applyTimeConfig;

}
