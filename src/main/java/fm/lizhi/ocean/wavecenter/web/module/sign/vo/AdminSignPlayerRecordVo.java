package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:13
 */
@Data
public class AdminSignPlayerRecordVo {

    /**
     * 合同ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long oldContractId;

    private UserVo playerInfo;

    private String status;

    /**
     * 是否本人发起,即管理员
     */
    private boolean selfCreate;

    /**
     * 签署有效期（解约的自动解约时间） 签署截止时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signDeadline;

    /**
     * 签约生效开始时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date startTime;

    /**
     * 解约合同 原合同的签约生效时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date oldStartTime;

    /**
     * 签约生效结束时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date endTime;

    /**
     * 解约时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date stopTime;

    /**
     * 签约申请发起时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

}
