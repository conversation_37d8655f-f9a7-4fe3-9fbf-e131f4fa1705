package fm.lizhi.ocean.wavecenter.web.module.live.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import java.util.List;

/**
 * 主播私信数据导出参数
 * <AUTHOR>
 * @date 2024/4/20 15:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LiveSmsPlayerExportParamVo extends LiveSmsPlayerParamVo {

    /**
     * 导出指标
     */
    @Valid
    private List<String> metrics;


}