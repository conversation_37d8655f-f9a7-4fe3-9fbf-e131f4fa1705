package fm.lizhi.ocean.wavecenter.web.module.live.vo;


import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class PlayerAuditRecordSearchParamVo {


    /**
     * 审核操作op
     */
    private Integer op;

    /**
     * yyyy-MM-dd
     */
    @NotNull(message = "开始时间为空")
    private String startDate;

    @NotNull(message = "结束时间为空")
    private String endDate;

    private Integer pageNo = 1;

    private Integer pageSize = 20;

}
