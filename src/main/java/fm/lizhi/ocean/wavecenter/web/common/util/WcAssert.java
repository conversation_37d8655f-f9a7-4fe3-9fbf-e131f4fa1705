package fm.lizhi.ocean.wavecenter.web.common.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.web.common.IRCodes;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.exception.AssertException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/18 12:43
 */
public class WcAssert extends Assert {

    public static void isTrue(boolean expression, IRCodes irCodes) {
        if (!expression) {
            throw new AssertException(irCodes);
        }
    }

    public static void isFalse(boolean expression, IRCodes irCodes) {
        if (expression) {
            throw new AssertException(irCodes);
        }
    }

    /**
     * 时间查询是否允许
     * 开始时间和现在的差距不能超过90天
     * 开始时间和结束时间相差不能超过31天
     * @param startDate
     * @param endDate
     * @param irCodes
     */
    public static void isDateQueryAllow(Date startDate, Date endDate, IRCodes irCodes){
        long nowDif = DateUtil.between(startDate, new Date(), DateUnit.DAY);
        if (nowDif > 90) {
            throw new AssertException(irCodes);
        }

        if (endDate == null) {
            return;
        }

        long endDif = DateUtil.between(startDate, endDate, DateUnit.DAY);
        if (endDif > 31) {
            throw new AssertException(irCodes);
        }

    }

    public static void isDateQueryAllow(String startDate, String endDate, String patten, IRCodes irCodes){
        Date end = null;
        if (StringUtils.isNotBlank(endDate)) {
            end = DateUtil.parse(endDate, patten);
        }
        Date start = DateUtil.parse(startDate, patten);
        isDateQueryAllow(start, end, irCodes);
    }

    public static void isDateQueryAllow(String startDate, String endDate, IRCodes irCodes){
        isDateQueryAllow(startDate, endDate, DatePattern.NORM_DATE_PATTERN, irCodes);
    }

    public static void isDateQueryAllow(String startDate, String endDate){
        isDateQueryAllow(startDate, endDate, DatePattern.NORM_DATE_PATTERN, MsgCodes.DATE_TO_LONG);
    }

    public static void isDateQueryAllow(Date startDate, Date endDate){
        isDateQueryAllow(startDate, endDate, MsgCodes.DATE_TO_LONG);
    }
}
