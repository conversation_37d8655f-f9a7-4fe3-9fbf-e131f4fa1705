package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/22 21:49
 */
@Data
@FieldNameConstants
public class PlayerRankExportVo {

    @ExcelProperty("主播昵称")
    private String playerName;

    @ExcelProperty("主播ID")
    private String playerBand;

    @ExcelProperty("签约厅")
    private String roomName;

    @ExcelProperty("签约厅ID")
    private String njId;

    @ExcelProperty("个播收礼收入")
    private BigDecimal personalHallIncome;

    /**
     * 公会魅力值	公会考核期间总魅力值，单位：魅力值
     */
    @ExcelProperty("魅力值")
    private Integer charm;

    /**
     * 魅力值总排名
     */
    @ExcelProperty("魅力值总排名")
    private Integer charmRankFamily;

    /**
     * 魅力值厅排名
     */
    @ExcelProperty("魅力值厅排名")
    private Integer charmRankRoom;

    /**
     * 上麦时长(分钟)
     */
    @ExcelProperty("上麦时长(分钟)")
    private BigDecimal upGuestDur;

    /**
     * 送礼人数
     */
    @ExcelProperty("送礼人数")
    private Integer giftUserCnt;

    /**
     * 送礼客单价
     */
    @ExcelProperty("送礼客单价")
    private BigDecimal giftUserPrice;

    /**
     * 私信人数
     */
    @ExcelProperty("私信人数")
    private Integer chatUserCnt;

    /**
     * 私信回复人数
     */
    @ExcelProperty("私信回复人数")
    private Integer replyChatUserCnt;


    /**
     * 私信回复率
     */
    @ExcelProperty("私信回复率")
    private BigDecimal replyChatRate;

    /**
     * 私信进房人数
     */
    @ExcelProperty("私信进房人数")
    private Integer chatEnterRoomUserCnt;

    /**
     * 私信进房率
     */
    @ExcelProperty("私信进房率")
    private BigDecimal chatEnterRoomRate;


    /**
     * 私信付费人数
     */
    @ExcelProperty("私信付费人数")
    private Integer chatGiftUserCnt;

    /**
     * 私信付费率
     */
    @ExcelProperty("私信付费率")
    private BigDecimal chatGiftRate;

    /**
     * 邀请人数
     */
    @ExcelProperty("邀请人数")
    private Integer inviteUserCnt;

    /**
     * 邀请进房人数
     */
    @ExcelProperty("邀请进房人数")
    private Integer inviteEnterRoomUserCnt;

    /**
     * 邀请进房率
     */
    @ExcelProperty("邀请进房率")
    private BigDecimal inviteEnterRoomRate;

    /**
     * 邀请付费人数
     */
    @ExcelProperty("邀请付费人数")
    private Integer inviteGiftUserCnt;


    /**
     * 邀请付费率
     */
    @ExcelProperty("邀请付费率")
    private BigDecimal inviteGiftRate;

    /**
     * 主播粉丝数
     */
    @ExcelProperty("主播粉丝数")
    private Integer fansUserCnt;

    /**
     * 新增粉丝数
     */
    @ExcelProperty("主播新增粉丝数")
    private Integer newFansUserCnt;

    /**
     * 粉丝送礼收入
     */
    @ExcelProperty("粉丝送礼收入")
    private Integer fansGiftIncome;

    /**
     * 粉丝送礼人数
     */
    @ExcelProperty("粉丝送礼人数")
    private Integer fansGiftUserCnt;

    /**
     * 粉丝送礼客单价
     */
    @ExcelProperty("粉丝送礼客单价")
    private BigDecimal fansGiftUserPrice;

}
