package fm.lizhi.ocean.wavecenter.web.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/7 11:20
 */
public class MyDateUtil {

    /**
     * 获取日期的开始时间
     * @param dateStr
     * @return
     */
    public static Date getDayStart(String dateStr){
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        DateTime dateTime = DateUtil.parse(dateStr, DatePattern.NORM_DATE_PATTERN, DatePattern.NORM_DATETIME_PATTERN);
        return DateUtil.beginOfDay(dateTime);
    }

    public static Date getDayStart(Date date){
        if (date == null) {
            return null;
        }
        return DateUtil.beginOfDay(date);
    }

    /**
     * 获取日期的结束时间
     * @param dateStr
     * @return
     */
    public static Date getDayEnd(String dateStr){
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        DateTime dateTime = DateUtil.parse(dateStr, DatePattern.NORM_DATE_PATTERN, DatePattern.NORM_DATETIME_PATTERN);
        return DateUtil.endOfDay(dateTime);
    }

    public static Date getDayEnd(Date date){
        if (date == null) {
            return null;
        }
        return DateUtil.endOfDay(date);
    }

    public static void main(String[] args) {
        Date dayStart = getDayStart("2024-01-01");
        Date dayEnd = getDayEnd("2024-01-01");
        System.out.println("dayEnd = " + dayEnd);
    }

    /**
     * 获取时间范围分页的开始时间
     * @param start
     * @param end
     * @param pageNo
     * @param pageSize
     * @return
     */
    public static Date getRangeStart(Date start, Date end, Integer pageNo, Integer pageSize){
        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        List<DateTime> dayList = DateUtil.rangeToList(start, end, DateField.DAY_OF_YEAR);

        //是否超过范围
        if (pageNo * pageSize > dayList.size()) {
            return start;
        }

        // 计算要跳过的元素数量
        int skipElements = (pageNo - 1) * pageSize;

        // 使用 Stream 实现分页
        List<DateTime> pageList = dayList.stream()
                .skip(skipElements)
                .limit(pageSize)
                .collect(Collectors.toList());
        return pageList.get(0);
    }

    /**
     * 获取时间范围分页的结束时间
     * @param start
     * @param end
     * @param pageNo
     * @param pageSize
     * @return
     */
    public static Date getRangeEnd(Date start, Date end, Integer pageNo, Integer pageSize){
        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        List<DateTime> dayList = DateUtil.rangeToList(start, end, DateField.DAY_OF_YEAR);

        //是否超过范围
        if (pageNo * pageSize > dayList.size()) {
            return end;
        }

        // 计算要跳过的元素数量
        int skipElements = (pageNo - 1) * pageSize;

        // 使用 Stream 实现分页
        List<DateTime> pageList = dayList.stream()
                .skip(skipElements)
                .limit(pageSize)
                .collect(Collectors.toList());
        return pageList.get(pageList.size() - 1);
    }

    /**
     * 获取时间范围的总天数
     * @param start
     * @param end
     * @return
     */
    public static int getRangeTotal(Date start, Date end){
        List<DateTime> dayList = DateUtil.rangeToList(start, end, DateField.DAY_OF_YEAR);
        return dayList.size();
    }

}
