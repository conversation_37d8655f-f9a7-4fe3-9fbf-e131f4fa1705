package fm.lizhi.ocean.wavecenter.web.module.sign.processor;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.ApplySignParamVo;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/23 14:32
 */
@Component
public class XmSignUserProcessor implements ISignUserProcessor{

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public ResultVO<Void> applyAdminCheck(ApplySignParamVo param) {
        if (param.getTargetUserId() == null) {
            return ResultVO.failure(MsgCodes.PARAM_ERROR);
        }
        return ResultVO.success();
    }
}
