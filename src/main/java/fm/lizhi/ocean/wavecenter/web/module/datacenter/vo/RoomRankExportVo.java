package fm.lizhi.ocean.wavecenter.web.module.datacenter.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/23 15:49
 */
@Data
@FieldNameConstants
public class RoomRankExportVo {

    @ExcelProperty("厅主昵称")
    private String roomName;

    @ExcelProperty("厅主ID")
    private String njId;

    /**
     * 公会ID
     */
    @ExcelProperty("公会ID")
    private String familyId;


    /**
     * 总收入
     */
    @ExcelProperty("总收入")
    private BigDecimal allIncome;

    /**
     * 总收入排名
     */
    @ExcelProperty("总收入排名")
    private Integer incomeRank;


    /**
     * 考核收入
     */
    @ExcelProperty("考核收入")
    private BigDecimal income;


    /**
     * 签约厅收礼收入
     */
    @ExcelProperty("签约厅收礼收入")
    private BigDecimal signHallIncome;


    /**
     * 个播收礼收入
     */
    @ExcelProperty("个播收礼收入")
    private BigDecimal personalHallIncome;


    /**
     * 官方厅收礼收入
     */
    @ExcelProperty("官方厅收礼收入")
    private BigDecimal officialHallIncome;


    /**
     * 贵族提成收入
     */
    @ExcelProperty("贵族提成收入")
    private BigDecimal nobleIncome;

    /**
     * 个播贵族提成收入
     */
    @ExcelProperty("个播贵族提成收入")
    private BigDecimal personalNobleIncome;

    /**
     * 厅魅力值
     */
    @ExcelProperty("魅力值")
    private Integer charm;


    /**
     * 人均收入
     */
    @ExcelProperty("人均收入")
    private BigDecimal playerAvgIncome;

    /**
     * 人均魅力值
     */
    @ExcelProperty("人均魅力值")
    private BigDecimal playerAvgCharm;

    /**
     * 开播时长(分钟)
     */
    @ExcelProperty("开播时长(分钟)")
    private BigDecimal openDuration;

    /**
     * 厅签约主播数
     */
    @ExcelProperty("签约主播数")
    private Integer signPlayerCnt;

    /**
     * 上麦主播数
     */
    @ExcelProperty("上麦主播数")
    private Integer signUpGuestPlayerCnt;

    /**
     * 主播上麦率
     */
    @ExcelProperty("主播上麦率")
    private BigDecimal upPlayerRate;


    /**
     * 有收入主播人数
     */
    @ExcelProperty("有收入主播人数")
    private Integer incomePlayerCnt;

    /**
     * 有收入主播占比
     */
    @ExcelProperty("有收入主播占比")
    private BigDecimal incomePlayerRate;

    /**
     * 评论人数
     */
    @ExcelProperty("评论人数")
    private Integer commentUserCnt;



    /**
     * 上麦人数
     */
    @ExcelProperty("嘉宾上麦人数")
    private Integer upGuestPlayerCnt;



    /**
     * 上麦率
     */
    @ExcelProperty("嘉宾上麦率")
    private BigDecimal upGuestRate;

    /**
     * 送礼人数
     */
    @ExcelProperty("送礼人数")
    private Integer giftUserCnt;


    /**
     * 用户逗留人数(满1分钟)
     */
    @ExcelProperty("用户逗留人数(满1分钟)")
    private BigDecimal userFullOneMin;

    /**
     * 用户逗留人数(满3分钟)
     */
    @ExcelProperty("用户逗留人数(满3分钟)")
    private BigDecimal userFullThreeMin;

    /**
     * 用户逗留人数(满5分钟)
     */
    @ExcelProperty("用户逗留人数(满5分钟)")
    private BigDecimal userFullFiveMin;

    /**
     * 厅付费转化率(1min)
     */
    @ExcelProperty("厅付费转化率(1min)")
    private BigDecimal giftOneMinRate;

    /**
     * 厅付费转化率(3min)
     */
    @ExcelProperty("厅付费转化率(3min)")
    private BigDecimal giftThreeMinRate;

    /**
     * 厅付费转化率(5min)
     */
    @ExcelProperty("厅付费转化率(5min)")
    private BigDecimal giftFiveMinRate;

    /**
     * 人均逗留时长(分钟)
     */
    @ExcelProperty("人均逗留时长(分钟)")
    private BigDecimal avgUserStayDuration;


    /**
     * 送礼客单价
     */
    @ExcelProperty("送礼客单价")
    private BigDecimal giftUserPrice;

    /**
     * 私信人数
     */
    @ExcelProperty("私信人数")
    private Integer chatUserCnt;

    /**
     * 私信回复人数
     */
    @ExcelProperty("私信回复人数")
    private Integer replyChatUserCnt;

    /**
     * 私信回复率
     */
    @ExcelProperty("私信回复率")
    private BigDecimal replyChatRate;

    /**
     * 私信进房人数
     */
    @ExcelProperty("私信进房人数")
    private Integer chatEnterRoomUserCnt;

    /**
     * 私信进房率
     */
    @ExcelProperty("私信进房率")
    private BigDecimal chatEnterRoomRate;

    /**
     * 私信付费人数
     */
    @ExcelProperty("私信付费人数")
    private Integer chatGiftUserCnt;

    /**
     * 私信付费率
     */
    @ExcelProperty("私信付费率")
    private BigDecimal chatGiftRate;

    /**
     * 邀请人数
     */
    @ExcelProperty("邀请人数")
    private Integer inviteUserCnt;

    /**
     * 邀请进房人数
     */
    @ExcelProperty("邀请进房人数")
    private Integer inviteEnterRoomUserCnt;

    /**
     * 邀请进房率
     */
    @ExcelProperty("邀请进房率")
    private BigDecimal inviteEnterRoomRate;


    /**
     * 邀请付费人数
     */
    @ExcelProperty("邀请付费人数")
    private Integer inviteGiftUserCnt;

    /**
     * 邀请付费率
     */
    @ExcelProperty("邀请付费率")
    private BigDecimal inviteGiftRate;

    /**
     * 厅粉丝数
     */
    @ExcelProperty("厅粉丝数")
    private Integer fansUserCnt;

    /**
     * 厅新增粉丝
     */
    @ExcelProperty("厅新增粉丝")
    private Integer newFansUserCnt;

    /**
     * 主播粉丝
     */
    @ExcelProperty("主播粉丝")
    private Integer playerFansUserCnt;

    /**
     * 主播新增粉丝
     */
    @ExcelProperty("主播新增粉丝")
    private Integer playerNewFansUserCnt;


    /**
     * 主播粉丝送礼收入
     */
    @ExcelProperty("主播粉丝送礼收入")
    private BigDecimal playerFansGiftIncome;

    /**
     * 主播粉丝送礼人数
     */
    @ExcelProperty("主播粉丝送礼人数")
    private Integer playerFansGiftUserCnt;

    /**
     * 主播粉丝送礼客单价
     */
    @ExcelProperty("主播粉丝送礼客单价")
    private BigDecimal playerFansGiftUserPrice;

}
