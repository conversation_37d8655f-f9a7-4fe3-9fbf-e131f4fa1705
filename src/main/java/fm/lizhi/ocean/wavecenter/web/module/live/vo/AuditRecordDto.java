package fm.lizhi.ocean.wavecenter.web.module.live.vo;


import lombok.Data;

@Data
public class AuditRecordDto {

    /**
     * appName
     */
    private String appName;

    /**
     * 所属业务（liveId）
     */
    private Long belongId;

    /**
     * 送审时间
     */
    private Long createTime;

    /**
     * 审核时间
     */
    private Long insertTime;

    /**
     * PRO-线上，PRE-预发
     */
    private String env;

    /**
     * 违规用户ID
     */
    private Long fromUserId;

    /**
     * 处罚类型
     */
    private Integer op;

    /**
     * 送审ID
     */
    private String parentId;

    /**
     * 审核记录ID
     */
    private String recordId;

    /**
     * 理由
     */
    private String reason;

    /**
     * 受害者用户ID, 可能为空
     */
    private Long toUserId;

    /**
     * 接审业务场景值
     */
    private Integer type;

    /**
     * 接审业务场景名称
     */
    private String typeStr;

    /**
     * 处罚时效时间，比如1天
     */
    private String punishTime;

    /**
     * 违规音频片段
     */
    private String content;

}
