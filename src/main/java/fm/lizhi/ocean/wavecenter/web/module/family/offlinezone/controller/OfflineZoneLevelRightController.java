package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelConfigBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelRightBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLevelConfig;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLevelRight;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneLevelConfigService;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneLevelRightService;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert.OfflineZoneLevelConfigConvert;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert.OfflineZoneLevelRightConvert;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result.ListLevelConfigResult;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.model.result.ListLevelRightResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 线下专区等级权益控制器
 */
@RestController
@RequestMapping("/offline/levelRight")
@Slf4j
public class OfflineZoneLevelRightController {

    @Autowired
    private OfflineZoneLevelConfigConvert offlineZoneLevelConfigConvert;

    @Autowired
    private OfflineZoneLevelRightConvert offlineZoneLevelRightConvert;

    @Autowired
    private OfflineZoneLevelConfigService offlineZoneLevelConfigService;

    @Autowired
    private OfflineZoneLevelRightService offlineZoneLevelRightService;

    /**
     * 查询等级权益列表
     *
     * @return 等级权益列表结果
     */
    @VerifyUserToken
    @GetMapping("/listRight")
    public ResultVO<List<ListLevelRightResult>> listRight() {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        RequestListLevelRight request = offlineZoneLevelRightConvert.toRequestListLevelRight(appId);
        Result<List<ListLevelRightBean>> result = offlineZoneLevelRightService.listLevelRight(request);
        if (RpcResult.isFail(result)) {
            int rCode = result.rCode();
            if (rCode == CommonService.PARAM_ERROR) {
                String message = StringUtils.defaultIfBlank(result.getMessage(), MsgCodes.PARAM_ERROR.getMsg());
                log.info("listLevelRight param invalid, request={}, rCode={}, message={}", request, rCode, message);
                return ResultVO.failure(MsgCodes.PARAM_ERROR.getCode(), message);
            } else {
                log.info("listLevelRight fail, request={}, rCode={}, message={}", request, rCode, result.getMessage());
                return ResultVO.failure(MsgCodes.FAIL);
            }
        }
        List<ListLevelRightBean> rightBeans = result.target();
        List<ListLevelRightResult> rightResults = offlineZoneLevelRightConvert.toListLevelRightResults(rightBeans);
        log.debug("listLevelRight success, request={}, result={}", request, rightResults);
        return ResultVO.success(rightResults);
    }

    /**
     * 查询等级配置列表
     *
     * @return 等级配置列表结果
     */
    @VerifyUserToken
    @GetMapping("/listLevel")
    public ResultVO<List<ListLevelConfigResult>> listLevel() {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        RequestListLevelConfig request = offlineZoneLevelConfigConvert.toRequestListLevelConfig(appId);
        Result<List<ListLevelConfigBean>> result = offlineZoneLevelConfigService.listLevelConfig(request);
        if (RpcResult.isFail(result)) {
            int rCode = result.rCode();
            if (rCode == CommonService.PARAM_ERROR) {
                String message = StringUtils.defaultIfBlank(result.getMessage(), MsgCodes.PARAM_ERROR.getMsg());
                log.info("listLevelConfig param invalid, request={}, rCode={}, message={}", request, rCode, message);
                return ResultVO.failure(MsgCodes.PARAM_ERROR.getCode(), message);
            } else {
                log.info("listLevelConfig fail, request={}, rCode={}, message={}", request, rCode, result.getMessage());
                return ResultVO.failure(MsgCodes.FAIL);
            }
        }
        List<ListLevelConfigBean> levelConfigBeans = result.target();
        List<ListLevelConfigResult> levelConfigResults = offlineZoneLevelConfigConvert.toListLevelConfigResults(levelConfigBeans);
        log.debug("listLevelConfig success, request={}, result={}", request, levelConfigResults);
        return ResultVO.success(levelConfigResults);
    }
}
