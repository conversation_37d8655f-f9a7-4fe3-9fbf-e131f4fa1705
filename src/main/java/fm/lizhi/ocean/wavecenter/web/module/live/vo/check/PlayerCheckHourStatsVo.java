package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import fm.lizhi.ocean.wavecenter.web.common.BigDecimalDownSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.RoomVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 陪玩小时统计指标
 * <AUTHOR>
 * @date 2024/6/11 17:58
 */
@Data
@Accessors(chain = true)
public class PlayerCheckHourStatsVo {

    /**
     * 档期开始时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date endTime;

    @JsonSerialize(using = BigDecimalDownSerializer.class)
    private BigDecimal income;

    private long charm;

    private long charmValue;

    /**
     * 是否是主持,0: 不是主持  1：主持
     */
    private int isHost;

    /**
     * 0：未打卡，1：已打卡，2：确认打卡
     */
    private int checkInStatus;

    /**
     * 打卡厅
     */
    private RoomVo room;

}
