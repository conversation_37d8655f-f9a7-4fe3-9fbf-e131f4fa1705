package fm.lizhi.ocean.wavecenter.web.module.activitycenter.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import lombok.Data;

@Data
public class ActivityLevelResult {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 等级名称
     */
    private String level;
}
