package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.RankVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class RoomPlayerRankResVo {

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date date;

    private List<RankVo> ranks;

}
