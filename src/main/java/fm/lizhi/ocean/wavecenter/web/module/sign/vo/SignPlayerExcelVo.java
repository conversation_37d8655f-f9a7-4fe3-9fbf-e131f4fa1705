package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import cn.hutool.core.date.DatePattern;

import lombok.Data;

@Data
public class SignPlayerExcelVo {

    /**
     * 签约厅ID
     */
    @ExcelProperty(value = "签约厅ID")
    private String roomUserId;

    /**
     * 签约厅昵称
     */
    @ExcelProperty(value = "签约厅昵称")
    private String roomUserName;

    /**
     * 签约厅ID
     */
    @ExcelProperty(value = "签约主播ID")
    private String njId;

    /**
     * 签约厅昵称
     */
    @ExcelProperty(value = "签约主播昵称")
    private String njName;


    /**
     * 签约状态
     */
    @ExcelProperty(value = "签约状态")
    private String signStatus;


    /**
     * 签约时间
     */
    @ExcelProperty(value = "签约时间")
    @DateTimeFormat(DatePattern.NORM_DATETIME_PATTERN)
    private Date signDate;
}
