package fm.lizhi.ocean.wavecenter.web.module.home.result;


import fm.lizhi.ocean.wavecenter.web.module.home.vo.MetricsDataVO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 公会关键数据汇总
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GuildKeyDataSummaryResult {


    /**
     * 公会收入汇总
     */
    private MetricsDataVO sumIncome;

    /**
     * 上麦主播数
     */
    private MetricsDataVO signUpGuestPlayerCnt;

    /**
     * 有收入主播数
     */
    private MetricsDataVO incomePlayerCnt;
}
