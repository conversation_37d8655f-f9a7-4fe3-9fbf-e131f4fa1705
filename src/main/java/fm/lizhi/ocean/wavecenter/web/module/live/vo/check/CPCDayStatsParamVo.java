package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 11:31
 */
@Data
public class CPCDayStatsParamVo {

    /**
     * 签约：1 非签约：0
     */
    private Integer signTab;

    private Long playerId;

    /**
     * 开始时间 yyyy-MM-dd
     */
    @NotBlank(message = "开始时间不可为空")
    private String startDate;

    /**
     * 结束时间 yyyy-MM-dd
     */
    @NotBlank(message = "结束时间不可为空")
    private String endDate;

    private List<String> metrics = new ArrayList<>();

}
