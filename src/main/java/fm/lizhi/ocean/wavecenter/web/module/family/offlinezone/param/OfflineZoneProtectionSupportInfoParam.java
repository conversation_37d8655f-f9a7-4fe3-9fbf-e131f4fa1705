package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 跳槽保护-主播认证协议支撑信息请求参数
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class OfflineZoneProtectionSupportInfoParam {

    /**
     * 家族ID
     */
    @NotNull(message = "家族ID不能为空")
    private Long familyId;

    /**
     * 厅主ID
     */
    @NotNull(message = "厅主ID不能为空")
    private Long njId;

    /**
     * 主播ID
     */
    @NotNull(message = "主播ID不能为空")
    private Long playerId;
}
