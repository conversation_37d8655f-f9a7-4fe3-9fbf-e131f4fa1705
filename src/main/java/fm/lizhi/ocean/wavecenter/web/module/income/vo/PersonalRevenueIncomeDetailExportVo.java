package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * 考核收入
 */
@Data
public class PersonalRevenueIncomeDetailExportVo {

    @ExcelProperty(value = "记录时间")
    @DateTimeFormat(DatePattern.NORM_DATETIME_PATTERN)
    private Date date;

    @ExcelProperty(value = "收入钻")
    private String income;

    @ExcelProperty(value = "收益钻")
    private String revenueAmount;

    @ExcelProperty(value = "内容")
    private String content;
}
