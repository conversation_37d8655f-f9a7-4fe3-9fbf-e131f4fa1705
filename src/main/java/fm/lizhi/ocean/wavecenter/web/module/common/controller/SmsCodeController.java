package fm.lizhi.ocean.wavecenter.web.module.common.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserTokenBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserLoginService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.util.EnvUtils;
import fm.lizhi.ocean.wavecenter.web.config.AppConfig;
import fm.lizhi.ocean.wavecenter.web.module.common.vo.GenUserTokenParamVo;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserTokenVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import redis.clients.jedis.Jedis;

/**
 * 验证码查询工具
 */
@Slf4j
@RestController
@RequestMapping("/tool")
public class SmsCodeController {

    @Autowired
    private AppConfig appConfig;
    @Autowired
    private UserLoginService userLoginService;

    /**
     * 生成用户token, 仅预发可以访问
     * @return
     */
    @PostMapping("genUserToken")
    public ResultVO<UserTokenVo> genUserToken(@RequestBody GenUserTokenParamVo paramVo){
        if (EnvUtils.isPro()) {
            return ResultVO.failure("工具不可用，请联系管理员");
        }
        Result<UserTokenBean> userTokenBeanResult = userLoginService.genUserToken(paramVo.getAppId(), paramVo.getUserId(), paramVo.getDeviceId());
        if (RpcResult.isFail(userTokenBeanResult)) {
            log.warn("user token generate fail. rCode={}", userTokenBeanResult.rCode());
            return ResultVO.failure();
        }
        String refreshToken = userTokenBeanResult.target().getRefreshToken();
        String accessToken = userTokenBeanResult.target().getAccessToken();
        return ResultVO.success(new UserTokenVo().setRefreshToken(refreshToken).setAccessToken(accessToken));
    }

    /**
     * 获取验证码
     *
     * @param phone 手机号
     * @return 结果
     */
    @GetMapping("/smsCode")
    public ResultVO<String> getCode(String phone) {
        //只有预发和灯塔可以查询，过滤掉其他环境
        if (EnvUtils.isPro()) {
            return ResultVO.failure("工具不可用，请联系管理员");
        }

        if (StringUtils.isBlank(phone)) {
            return ResultVO.failure("手机号不能为空");
        }

        String host = appConfig.getSmsCodeRedisHost();
        int port = appConfig.getSmsCodeRedisPort();

        String code = "";
        Jedis jedis = null;
        try {
            jedis = new Jedis(host, port);
            if (!phone.contains("-")) {
                phone = "86-" + phone.trim();
            }
            String result = jedis.get("LZ_SMS_CODE_+" + phone.trim() + "-1");
            if (result != null) {
                JSONObject json = JSON.parseObject(result);
                code = json.getString("code");
            }
        } catch (Exception e) {
            log.warn("connect sms redis failed, msg={}", e.getMessage());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return ResultVO.success(code);
    }
}
