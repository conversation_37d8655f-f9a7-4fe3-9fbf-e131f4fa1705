package fm.lizhi.ocean.wavecenter.web.module.live.model.param;

import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.web.module.live.constant.CheckInExportMetricsType;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 导出麦序福利厅明细的参数
 */
@Data
public class ExportCheckInRoomDetailParam {

    /**
     * 厅主id
     */
    private Long njId;

    /**
     * 导出范围
     */
    @NotNull(message = "导出范围不能为空")
    private ExportScoreEnum exportScore;

    /**
     * 统计的时间类型
     */
    @NotNull(message = "时间类型不能为空")
    private CheckInDateTypeEnum dateType = CheckInDateTypeEnum.DAY;

    /**
     * 开始时间毫秒时间戳, 包含
     */
    @NotNull(message = "开始时间不能为空")
    private Long startDate;

    /**
     * 结束时间毫秒时间戳, 包含
     */
    @NotNull(message = "结束时间不能为空")
    private Long endDate;

    /**
     *  指标列表
     *  @see CheckInExportMetricsType
     */
    private List<String> metrics;



    /**
     * 导出范围的枚举
     */
    public enum ExportScoreEnum {

        /**
         * 全部厅, 当角色为家族长或高级管理时可用
         */
        ALL,
        /**
         * 当前厅. 当角色不是厅主时需指定厅主id
         */
        CURRENT,
    }
}
