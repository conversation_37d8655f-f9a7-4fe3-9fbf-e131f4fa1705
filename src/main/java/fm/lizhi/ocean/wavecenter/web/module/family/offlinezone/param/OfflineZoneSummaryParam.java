package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 离线区域数据监控-汇总-厅请求参数
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class OfflineZoneSummaryParam {

    /**
     * 家族ID
     */
    @NotNull(message = "家族ID不能为空")
    private Long familyId;

    /**
     * 线下厅ID
     */
    @NotNull(message = "线下厅ID不能为空")
    private Long roomId;
}
