package fm.lizhi.ocean.wavecenter.web.module.income.processor;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.RoomSignPlayerIncomeBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.functional.ExportDataQuery;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.income.convert.IncomeConvert;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.export.hy.HyGuildRoomIncomeDetailExportVo;
import fm.lizhi.ocean.wavecenter.web.module.income.vo.export.hy.HyRoomSignPlayerIncomeExportVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/20 19:32
 */
@Slf4j
@Component
public class HyIIncomeProcessor implements IIncomeProcessor{

    @Autowired
    private FileExportHandler fileExportHandler;


    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public ResultVO<Void> roomIncomeDetailExport(ExportDataQuery<RoomIncomeDetailBean> dataQuery) {
        String fileName = fileExportHandler.genFileName("签约厅收入统计明细");
        return fileExportHandler.exportFile(fileName, HyGuildRoomIncomeDetailExportVo.class, ((pageNo, pageSize) -> {
            PageBean<RoomIncomeDetailBean> pageBean = dataQuery.queryData(pageNo, pageSize);
            List<RoomIncomeDetailBean> list = pageBean.getList();
            List<HyGuildRoomIncomeDetailExportVo> voList = IncomeConvert.I.hyGuildRoomIncomeDetailBeans2ExportVos(list);
            return PageVO.of(0, voList);
        }));
    }

    @Override
    public ResultVO<Void> roomSignPlayerExport(ExportDataQuery<RoomSignPlayerIncomeBean> dataQuery) {
        String fileName = fileExportHandler.genFileName("签约主播收入统计明细");
        return fileExportHandler.exportFile(fileName, HyRoomSignPlayerIncomeExportVo.class, ((pageNo, pageSize) -> {
            PageBean<RoomSignPlayerIncomeBean> pageBean = dataQuery.queryData(pageNo, pageSize);
            List<RoomSignPlayerIncomeBean> list = pageBean.getList();
            List<HyRoomSignPlayerIncomeExportVo> voList = IncomeConvert.I.hyRoomSignPlayerIncomeBeans2ExportVos(list);
            return PageVO.of(0, voList);
        }));
    }
}
