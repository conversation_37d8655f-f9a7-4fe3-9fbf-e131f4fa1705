package fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.gift.bean.GetGiftsBean;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestGetGifts;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.GetGiftsParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.GetGiftsResult;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityGiftConvert {

    RequestGetGifts toRequestGetGifts(GetGiftsParam param, Integer appId);

    List<GetGiftsResult> toGetGiftsResults(List<GetGiftsBean> beans);
}
