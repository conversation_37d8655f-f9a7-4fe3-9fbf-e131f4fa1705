package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/6/11 10:27
 */
@Data
public class CRCDayStatsParamVo {

    private Long roomId;

    /**
     * 开始时间 yyyy-MM-dd
     */
    @NotBlank(message = "开始时间不可为空")
    private String startDate;

    /**
     * 结束时间 yyyy-MM-dd
     */
    @NotBlank(message = "结束时间不可为空")
    private String endDate;

    @Min(value = 1, message = "最早只能查看第一页")
    private Integer pageNo;

    @Min(value = 1, message = "每页最少查询1条")
    @Max(value = 100, message = "每页最多查询100条")
    private Integer pageSize;

}
