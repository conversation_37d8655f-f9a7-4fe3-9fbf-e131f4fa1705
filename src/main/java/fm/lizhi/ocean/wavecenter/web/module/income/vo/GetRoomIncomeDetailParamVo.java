package fm.lizhi.ocean.wavecenter.web.module.income.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/28 14:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetRoomIncomeDetailParamVo extends GetDetailBaseParamVo {


    @Min(value = 1)
    private Integer pageNo;

    @Min(value = 1)
    @Max(value = 100)
    private Integer pageSize;

    /**
     * 收入类型
     */
    private List<Integer> incomeType;

    private Long flushTime = System.currentTimeMillis();

}
