package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.gift.bean.GetGiftsBean;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestGetGifts;
import fm.lizhi.ocean.wavecenter.api.gift.service.GiftService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityGiftConvert;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.param.GetGiftsParam;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.result.GetGiftsResult;
import fm.lizhi.ocean.wavecenter.web.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/activity/gift")
@Slf4j
public class ActivityGiftController {

    @Autowired
    private ActivityGiftConvert activityGiftConvert;

    @Autowired
    private GiftService giftService;

    @GetMapping("/getGifts")
    @VerifyUserToken
    public ResultVO<List<GetGiftsResult>> getGifts(@Validated GetGiftsParam param) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        RequestGetGifts request = activityGiftConvert.toRequestGetGifts(param, appId);
        Result<List<GetGiftsBean>> result = giftService.getGifts(request);
        if (RpcResult.isFail(result)) {
            int rCode = result.rCode();
            String message = StringUtils.defaultIfBlank(result.getMessage(), "查询礼物失败");
            log.info("getGifts fail, param: {}, result: {}", param, result);
            return ResultVO.failure(rCode, message);
        }
        List<GetGiftsBean> beans = result.target();
        if (log.isDebugEnabled()) {
            log.debug("getGifts success, param: {}, beans: {}", param, JsonUtils.toJsonString(beans));
        }
        List<GetGiftsResult> results = activityGiftConvert.toGetGiftsResults(beans);
        return ResultVO.success(results);
    }
}
