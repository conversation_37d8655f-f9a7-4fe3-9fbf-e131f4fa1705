package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserInfoVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/8 11:23
 */
@Data
public class RoomSignRecordVo {

    /**
     * 合同ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

    /**
     * 解约记录的原合同
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long oldContractId;

    /**
     * 签约ID
     */
    private String signId;

    /**
     * 厅主信息
     */
    private UserInfoVo roomInfo;

    /**
     * 结算方式
     */
    private String settleType;

    /**
     * 分成比例
     */
    private Integer settlePercentage;

    /**
     * 下级人数
     */
    private Integer signCount;

    /**
     * 状态
     */
    private String status;

    /**
     * 签署截止时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signDeadline;

    /**
     * 签约完成时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signFinishTime;

    /**
     * 合同开始时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date beginTime;

    /**
     * 解约合同 原签约合同的开始时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date oldBeginTime;

    /**
     * 合同到期时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date expireTime;

    /**
     * 原合同到期时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date oldExpireTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /**
     * 终止/解约合同时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date stopTime;

}
