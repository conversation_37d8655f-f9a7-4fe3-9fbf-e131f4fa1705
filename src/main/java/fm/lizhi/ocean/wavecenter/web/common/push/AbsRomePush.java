package fm.lizhi.ocean.wavecenter.web.common.push;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.rome.push.api.RomePushService;
import fm.lizhi.commons.rome.push.dto.BroadcastResult;
import fm.lizhi.commons.rome.push.dto.RomeBroadcastMessage;
import fm.lizhi.commons.rome.push.dto.RomePushMessage;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2024/4/17 16:02
 */
@Slf4j
public abstract class AbsRomePush {

    public static final String businessEnv = "creatorCenter";

    @Autowired
    protected RomePushService romePushService;

    public RomePushMessage buildDevicePushMessage(){
        RomePushMessage message = new RomePushMessage();
        message.setBusinessEnv(businessEnv);
        message.setTargetType(RomePushMessage.RomePushTargetType.DEVICE_ID);
        message.setPayloadId(String.valueOf(System.currentTimeMillis()));
        return message;
    }

    public void pushMessage(String topic, PushVo<?> pushVo){
        long payloadId = System.currentTimeMillis();
        String payload = JsonUtil.dumps(pushVo);
        byte[] bytes = payload.getBytes(StandardCharsets.UTF_8);
        RomeBroadcastMessage broadcastMessage = new RomeBroadcastMessage();
        broadcastMessage.setBusinessEnv(businessEnv);
        broadcastMessage.setTopic(topic);
        broadcastMessage.setPayloadId(String.valueOf(payloadId));
        broadcastMessage.setPayload(bytes);

        Result<BroadcastResult> resp = romePushService.broadcastMessage(broadcastMessage);
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("pushMessage failed to request - topic:{}, payload:{}, payloadId:{}, rCode:{}",
                    topic, payload, payloadId, resp.rCode());
            return;
        }

        long total = resp.target().getTotal();
        long successCnt = resp.target().getSuccessCnt();
        log.info("pushMessage successful request - topic:{}, payload:{}, payloadId:{}, total:{}, successCnt:{}",
                topic, payload, payloadId, total, successCnt);

    }
}
