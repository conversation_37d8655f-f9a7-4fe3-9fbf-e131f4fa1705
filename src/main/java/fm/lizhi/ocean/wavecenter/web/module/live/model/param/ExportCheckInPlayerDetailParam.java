package fm.lizhi.ocean.wavecenter.web.module.live.model.param;

import fm.lizhi.ocean.wavecenter.web.module.live.constant.CheckInExportMetricsType;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Arrays;
import java.util.List;

/**
 * 导出麦序福利主播明细的参数
 */
@Data
public class ExportCheckInPlayerDetailParam {

    /**
     * 主播id
     */
    private Long playerId;

    /**
     * 开始时间毫秒时间戳, 包含
     */
    @NotNull
    private Long startDate;

    /**
     * 结束时间毫秒时间戳, 包含
     */
    @NotNull
    private Long endDate;

    /**
     *  指标列表
     *  @see CheckInExportMetricsType
     */
    private List<String> metrics = CheckInExportMetricsType.defaultPlayerMetrics();
}
