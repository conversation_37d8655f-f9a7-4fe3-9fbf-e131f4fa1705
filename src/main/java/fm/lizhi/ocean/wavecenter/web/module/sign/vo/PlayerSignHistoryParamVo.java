package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:22
 */
@Data
public class PlayerSignHistoryParamVo {

    @Min(value = 1, message = "页码最小为1")
    private Integer pageNo = 1;

    @Min(value = 1, message = "每页条数最小为1")
    @Max(value = 100, message = "每页条数最大为100")
    private Integer pageSize = 10;

    /**
     * 类型
     * SIGN=签约, CANCEL=解约
     */
    private String type;

    /**
     * 状态
     */
    private String status;

}
