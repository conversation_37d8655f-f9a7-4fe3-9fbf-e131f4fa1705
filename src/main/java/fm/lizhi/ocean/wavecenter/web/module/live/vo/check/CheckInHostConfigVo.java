package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 主持人配置 Bean
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CheckInHostConfigVo {


    /**
     * 时段（0=0-1点，23=23-24点）
     */
    private Integer timeSlot;

    /**
     * 主持人ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long hostId;

    /**
     * 主持人信息
     */
    private UserVo hostInfo;

}
