package fm.lizhi.ocean.wavecenter.web.module.sign.controller;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.CancelPlayerRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.TodoSignBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.TodoSignPlayerBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignFamilyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.sign.convert.SignConvert;
import fm.lizhi.ocean.wavecenter.web.module.sign.param.FamilyDoUnwindParam;
import fm.lizhi.ocean.wavecenter.web.module.sign.param.FamilyUnwindParam;
import fm.lizhi.ocean.wavecenter.web.module.sign.result.FamilyDoUnwindResult;
import fm.lizhi.ocean.wavecenter.web.module.sign.result.FamilyUnwindResult;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/5 16:10
 */
@Slf4j
@RestController
@RequestMapping("sign/family")
public class SignFamilyController {

    @Autowired
    private SignFamilyService signFamilyService;

    /**
     * 审批解约申请
     * @param param
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("reviewCancel")
    public ResultVO<Void> reviewCancel(@Validated @RequestBody ReviewCancelParamVo param){
        RequestFamilyReviewCancel rpcParam = RequestFamilyReviewCancel.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .playerSignId(param.getContractId())
                .operateType(OperateTypeEnum.getByCode(param.getReviewType()))
                .build();
        Result<ResponseFamilyReviewCancel> result = signFamilyService.reviewCancel(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("reviewCancel fail. rCode={},rpcParam={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        return SignCommonUtil.signCommonCodeCheck(result.target());
    }

    /**
     * 工作台列表
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("todoList")
    public ResultVO<List<TodoSignVo>> todoList(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();
        Result<List<TodoSignBean>> result = signFamilyService.todoRoomList(RequestFamilyTodoRoomList.builder()
                .appId(appId)
                .familyId(familyId)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("todoList fail. appId={},familyId={},rCode={}", appId, familyId, result.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }
        return ResultVO.success(SignConvert.I.todoSignBeans2Vos(result.target()));
    }

    /**
     * 工作台列表-审批主播申请
     * @param
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("todoPlayerList")
    public ResultVO<List<TodoSignPlayerVo>> todoPlayerList(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();
        Result<List<TodoSignPlayerBean>> result = signFamilyService.todoPlayerList(RequestFamilyTodoPlayerList.builder()
                .appId(appId)
                .familyId(familyId)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("todoPlayerList fail. appId={},familyId={},rCode={}", appId, familyId, result.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }
        return ResultVO.success(SignConvert.I.todoSignPlayerBeans2Vos(result.target()));
    }

    /**
     * 签约解约列表
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("signList")
    public ResultVO<PageVO<RoomSignRecordVo>> signList(@Validated GetSignListParamVo paramVo){
        //参数检查
        ContractTypeEnum contractType = ContractTypeEnum.from(paramVo.getType());
        if (contractType == null) {
            return ResultVO.failure("不支持该类型");
        }

        SignRelationEnum status = SignRelationEnum.fromCode(paramVo.getStatus());
        if (status == SignRelationEnum.UNKNOWN) {
            return ResultVO.failure("不支持该状态查询");
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();

        RequestQueryRoomSignList rpcParam = RequestQueryRoomSignList.builder()
                .appId(appId)
                .familyId(familyId)
                .type(contractType)
                .status(status)
                .userBand(paramVo.getUserBand())
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .build();
        Result<PageBean<RoomSignRecordBean>> result = signFamilyService.queryRoomSignList(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("family queryRoomSignList fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }

        List<RoomSignRecordVo> voList = SignConvert.I.roomSignRecordBeans2Vos(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList));
    }

    /**
     * 解约陪玩列表
     * 仅会查询 待确认 和 已拒绝
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("cancelPlayerList")
    public ResultVO<PageVO<CancelPlayerRecordVo>> cancelPlayerList(@Validated GetCancelPlayerListParamVo paramVo){
        //参数检查
        SignRelationEnum status = SignRelationEnum.fromCode(paramVo.getStatus());
        if (status != SignRelationEnum.REVIEW_REJECTED
                && status != SignRelationEnum.SIGN_CONFIRM) {
            return ResultVO.failure("不支持该状态查询");
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();

        RequestQueryRoomSignList rpcParam = RequestQueryRoomSignList.builder()
                .appId(appId)
                .familyId(familyId)
                .type(ContractTypeEnum.CANCEL)
                .status(status)
                .pageNo(paramVo.getPageNo())
                .pageSize(paramVo.getPageSize())
                .userBand(paramVo.getUserBand())
                .build();
        Result<PageBean<CancelPlayerRecordBean>> result = signFamilyService.queryCancelPlayerList(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("family queryCancelPlayerList fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }

        List<CancelPlayerRecordVo> voList = SignConvert.I.cancelPlayerRecordBeans2Vos(result.target().getList());
        return ResultVO.success(PageVO.of(result.target().getTotal(), voList));
    }

    /**
     * 签约统计
     * @return
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY}, onlySelfLogin = true)
    @VerifyUserToken
    @GetMapping("signCount")
    public ResultVO<FamilySignCountVo> signCount(){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();

        FamilySignCountVo vo = new FamilySignCountVo();
        Result<Integer> canOpenResult = signFamilyService.countCanOpenRoomNum(appId, familyId);
        if (RpcResult.isSuccess(canOpenResult)) {
            vo.setCanCreateRoomNum(canOpenResult.target());
        } else {
            log.error("family countCanOpenRoomNum fail. appId={},familyId={},rCode={}", appId, familyId, canOpenResult.rCode());
        }

        Result<Integer> signNumResult = signFamilyService.countSignRoomNum(appId, familyId);
        if (RpcResult.isSuccess(signNumResult)) {
            vo.setSignRoomNum(signNumResult.target());
        } else {
            log.error("family countSignRoomNum fail. appId={},familyId={},rCode={}", appId, familyId, signNumResult.rCode());
        }

        return ResultVO.success(vo);
    }


    /**
     * 邀请签约
     * 邀请用户签约为管理员
     * 仅黑叶和PP支持，且仅黑叶会返回签约ID
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("/inviteSign")
    public ResultVO<FamilyInviteSignResVo> inviteSign(@Validated @RequestBody InviteSignParamVo param){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();
        long curUserId = ContextUtils.getContext().getUserId();

        RequestFamilyInviteAdmin request = RequestFamilyInviteAdmin.builder()
                .appId(appId)
                .familyId(familyId)
                .curUserId(curUserId)
                .targetUserId(param.getTargetUserId())
                .duration(param.getDuration())
                .build();

        Result<ResponseFamilyInviteAdmin> result = signFamilyService.inviteAdmin(request);
        if (RpcResult.isFail(result)) {
            log.error("family inviteAdmin fail. request={},rCode={}", JsonUtil.dumps(request), result.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }
        ResultVO<Void> voRes = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voRes.isOK()) {
            return ResultVO.failure(voRes.getRCode(), voRes.getPrompt());
        }

        return ResultVO.success(new FamilyInviteSignResVo()
                .setSignId(result.target().getSignId())
                .setContractUrl(result.target().getContractUrl())
                .setContractId(result.target().getContractId())
        );
    }


    /**
     * 签约
     * @param param
     * @return 合同url
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("/doSign")
    public ResultVO<FamilyDoSignResVo> doSign(@Validated @RequestBody DoSignParamVo param){

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        Long familyId = ContextUtils.getContext().getSubjectId();

        RequestFamilyDoSign rpcParam = RequestFamilyDoSign.builder()
                .appId(appId)
                .familyId(familyId)
                .curUserId(userId)
                .signId(param.getSignId())
                .contractId(param.getContractId())
                .build();
        Result<ResponseFamilyDoSign> result = signFamilyService.doSign(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("family doSign fail. rCode={},rpcParam={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }

        ResultVO<Void> voRes = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voRes.isOK()) {
            return ResultVO.failure(voRes.getRCode(), voRes.getPrompt());
        }

        return ResultVO.success(new FamilyDoSignResVo()
                .setContractUrl(result.target().getContractUrl()));
    }

    /**
     * 申请解约
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("/unwind")
    public ResultVO<FamilyUnwindResult> unwind(@Validated @RequestBody FamilyUnwindParam param){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Long familyId = ContextUtils.getContext().getSubjectId();

        RequestFamilyApplyCancelAdmin request = RequestFamilyApplyCancelAdmin.builder()
                .appId(appId)
                .familyId(familyId)
                .contractId(param.getContractId())
                .targetUserId(param.getTargetUserId())
                .curUserId(ContextUtils.getContext().getUserId())
                .build();

        Result<ResponseFamilyApplyCancelAdmin> result = signFamilyService.applyCancelAdmin(request);
        if (RpcResult.isFail(result)) {
            log.error("family applyCancelAdmin fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return ResultVO.failure(MsgCodes.FAIL);
        }

        ResultVO<Void> voRes = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voRes.isOK()) {
            return ResultVO.failure(voRes.getRCode(), voRes.getPrompt());
        }

        return ResultVO.success(new FamilyUnwindResult()
                .setContractId(result.target().getContractId())
                .setContractUrl(result.target().getContractUrl()));
    }

    /**
     * 签署解约
     */
    @PermissionCheck(passRole = {RoleEnum.FAMILY}, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("/doUnwind")
    public ResultVO<FamilyDoUnwindResult> doUnwind(@Validated @RequestBody FamilyDoUnwindParam param){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        RequestFamilyDoCancelAdmin rpcParam = RequestFamilyDoCancelAdmin.builder()
                .appId(appId)
                .contractId(param.getContractId())
                .signId(param.getSignId())
                .familyId(ContextUtils.getContext().getSubjectId())
                .currUserId(ContextUtils.getContext().getUserId())
                .build();

        Result<ResponseFamilyDoCancelAdmin> result = signFamilyService.doCancelAdmin(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("family doCancelFamily fail. rCode={},param={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        ResultVO<Void> voRes = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voRes.isOK()) {
            return ResultVO.failure(voRes.getRCode(), voRes.getPrompt());
        }

        return ResultVO.success(new FamilyDoUnwindResult()
                .setContractUrl(result.target().getContractUrl()));
    }

}
