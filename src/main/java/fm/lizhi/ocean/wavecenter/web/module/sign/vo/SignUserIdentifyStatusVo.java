package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户实名状态
 * (UNFINISHED 未完成、FINISHED 已完成、PROCESSING 进行中/认证中、 AUDITING 审核中)
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SignUserIdentifyStatusVo {

    /**
     * 平台实名
     */
    private String platformStatus;

    /**
     * 上上签实名
     */
    private String bestSignStatus;


}
