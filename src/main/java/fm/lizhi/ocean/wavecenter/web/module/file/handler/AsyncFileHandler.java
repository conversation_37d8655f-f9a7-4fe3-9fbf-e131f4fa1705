package fm.lizhi.ocean.wavecenter.web.module.file.handler;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.constants.ExportFileStatusEnum;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.FileExportRecordBean;
import fm.lizhi.ocean.wavecenter.api.file.service.FileExportRecordService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.push.CommonPushHandler;
import fm.lizhi.ocean.wavecenter.web.common.push.PushConstants;
import fm.lizhi.ocean.wavecenter.web.common.push.PushTopic;
import fm.lizhi.ocean.wavecenter.web.common.push.PushVo;
import fm.lizhi.ocean.wavecenter.web.common.util.WcAssert;
import fm.lizhi.ocean.wavecenter.web.module.file.vo.FileExportFinishVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2024/4/23 19:43
 */
@Slf4j
@Component
public class AsyncFileHandler {

    @Autowired
    private RomefsUploadHandler romefsUploadHandler;
    @Autowired
    private FileExportRecordService fileExportRecordService;
    @Autowired
    private CommonPushHandler commonPushHandler;

    /**
     * 自定义导出
     * @param recordId
     * @param fileName
     * @param writerConsumer
     */
    @Async("fileExport")
    public void asyncExportDiy(Long recordId, String fileName, Consumer<ExcelWriterBuilder> writerConsumer){
        String path = createTempFile(fileName);
        if (path == null) {
            return;
        }

        try {
            // 生成文件
            ExcelWriterBuilder writerBuilder = EasyExcelFactory.write(path);
            writerConsumer.accept(writerBuilder);

            // 上传文件
            String oPath = romefsUploadHandler.uploadFileToRome(path);
            WcAssert.hasText(oPath, "oPath is empty");

            // 更新文件状态
            updateTaskStatus(recordId, oPath, ExportFileStatusEnum.EXPORT_COMPLETED);

        } catch (Exception e) {
            // 更新异常状态
            log.error("asyncExport error recordId={}", recordId, e);
            updateTaskStatus(recordId, "", ExportFileStatusEnum.EXPORT_FAILED);
        } finally {
            deleteTempPath(path);
        }

        // 推送通知
        pushFinishMsg(recordId);
    }

    @Async("fileExport")
    public void asyncExport(Long recordId, String fileName, Class<?> head, Consumer<ExcelWriter> writerConsumer){
        String path = createTempFile(fileName);
        if (path == null) {
            return;
        }

        try {
            // 生成文件
            ExcelWriter excelWriter = EasyExcelFactory.write(path, head).build();
            writerConsumer.accept(excelWriter);
            excelWriter.finish();

            // 上传文件
            String oPath = romefsUploadHandler.uploadFileToRome(path);
            WcAssert.hasText(oPath, "oPath is empty");

            // 更新文件状态
            updateTaskStatus(recordId, oPath, ExportFileStatusEnum.EXPORT_COMPLETED);

            // 推送通知
            pushFinishMsg(recordId);
        } catch (Exception e) {
            // 更新异常状态
            log.error("asyncExport error recordId={}", recordId, e);
            updateTaskStatus(recordId, "", ExportFileStatusEnum.EXPORT_FAILED);
        } finally {
            deleteTempPath(path);
        }
    }

    private void pushFinishMsg(Long recordId){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        String topic = PushTopic.USER_PUSH.getKey(appId, userId);
        PushVo<FileExportFinishVo> pushVo = new PushVo<>();
        pushVo.setBiz(PushConstants.Biz.FILE_DOWN);
        pushVo.setData(new FileExportFinishVo().setRecordId(String.valueOf(recordId)));
        commonPushHandler.pushMessage(topic, pushVo);
    }

    private String createTempFile(String fileName) {
        try {
            Path tmpDir = Files.createTempDirectory("excel_exports");
            Path filePath = tmpDir.resolve(fileName + ".xlsx");
            return filePath.toString();
        } catch (IOException e) {
            log.error("Failed to create temp file for fileName={}", fileName, e);
            return null;
        }
    }

    private void updateTaskStatus(Long recordId, String oPath, ExportFileStatusEnum statusEnum) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<FileExportRecordBean> result = fileExportRecordService.updateTask(appId,
                recordId, oPath, statusEnum);
        if (RpcResult.isFail(result)) {
            log.error("updateTask fail, recordId={},rCode={}", recordId, result.rCode());
        }
    }

    private void deleteTempPath(String path) {
        try {
            Files.deleteIfExists(Paths.get(path));
        } catch (IOException e) {
            log.error("Failed to delete temp file path={}", path, e);
        }
    }

}
