package fm.lizhi.ocean.wavecenter.web.module.live.handler;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.IDetailEle;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.IDetailList;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.TimeStatsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/6/25 11:20
 */
@Component
public class CheckHandler {

    /**
     * 按照时间合计
     * @param dataList
     * @return
     * @param <T>
     */
    public <T extends IDetailList> List<TimeStatsVo> groupTimeStats(List<T> dataList){
        //分组
        Map<String, List<IDetailEle>> timeMap = new HashMap<>();
        for (T data : dataList) {
            List<IDetailEle> eleList = data.foundDetail();
            if (CollectionUtils.isEmpty(eleList)) {
                continue;
            }
            for (IDetailEle ele : eleList) {
                Date date = ele.foundTime();
                if (date == null) {
                    continue;
                }
                String dateStr = DateUtil.formatDateTime(date);
                timeMap.computeIfAbsent(dateStr, k -> new ArrayList<>()).add(ele);
            }
        }

        //合计
        List<TimeStatsVo> result = new ArrayList<>();
        for (Map.Entry<String, List<IDetailEle>> entry : timeMap.entrySet()) {
            TimeStatsVo vo = new TimeStatsVo();

            String dateStr = entry.getKey();
            DateTime dateTime = DateUtil.parseDateTime(dateStr);
            vo.setTime(dateTime);

            List<IDetailEle> eleList = entry.getValue();
            BigDecimal income = BigDecimal.ZERO;
            int charm = 0;
            int seatOrder = 0;
            int hostCnt = 0;
            int checkPlayerNumber = 0;
            for (IDetailEle ele : eleList) {
                String incomeStr = ele.foundIncome();
                if (StringUtils.isNotBlank(incomeStr)) {
                    income = income.add(new BigDecimal(incomeStr));
                }

                if (ele.foundCharm() != null) {
                    charm += ele.foundCharm();
                }

                if (ele.foundSeatOrder() != null) {
                    seatOrder += ele.foundSeatOrder();
                }

                if (ele.foundHostCnt() != null) {
                    hostCnt += ele.foundHostCnt();
                }

                if (ele.foundCheckPlayerNumber() != null) {
                    checkPlayerNumber += ele.foundCheckPlayerNumber();
                }
            }

            vo.setIncome(income)
                    .setCharm(charm)
                    .setSeatOrder(seatOrder)
                    .setHostCnt(hostCnt)
                    .setCheckPlayerNumber(checkPlayerNumber);

            result.add(vo);
        }

        return result;
    }

}
