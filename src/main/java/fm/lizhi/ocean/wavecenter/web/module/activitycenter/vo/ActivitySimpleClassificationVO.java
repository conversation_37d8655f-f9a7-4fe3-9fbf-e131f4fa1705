package fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Builder;
import lombok.Data;

/**
 *
 * 活动分类
 *
 * <AUTHOR>
 * @date 2024-10-10 06:34:31
 */
@Data
@Builder
public class ActivitySimpleClassificationVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String name;

    /**
     * 权重
     */
    private Integer weight;
}
