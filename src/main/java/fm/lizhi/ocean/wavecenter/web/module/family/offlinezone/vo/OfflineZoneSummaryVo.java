package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo;

import lombok.Data;

/**
 * 离线区域数据监控-汇总-厅响应结果
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class OfflineZoneSummaryVo {

    /**
     * 线下主播数
     */
    private OfflineZoneMetricsDataVo offlinePlayerCnt;

    /**
     * 线下主播数占比
     */
    private OfflineZoneMetricsDataVo offlinePlayerCntRate;

    /**
     * 线下厅收入
     */
    private OfflineZoneMetricsDataVo income;

    /**
     * 受保护主播数
     */
    private OfflineZoneMetricsDataVo protectedPlayerCnt;

    /**
     * 受保护主播数占比
     */
    private OfflineZoneMetricsDataVo protectedPlayerCntRate;

    /**
     * 线下主播收入
     */
    private OfflineZoneMetricsDataVo offlinePlayerIncome;

    /**
     * 线下主播收入占比
     */
    private OfflineZoneMetricsDataVo offlinePlayerIncomeRate;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;
}
