package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/5 15:39
 */
@Data
public class MyRoomVo {

    /**
     * 签约记录ID
     * playerSignId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

    /**
     * 简介
     */
    private String roomIntro;

    /**
     * 厅主
     */
    private UserVo roomUser;

    /**
     * 结算方式
     */
    private String settleType;

    /**
     * 分成比例
     */
    private String settlePercentage;

    /**
     * 家族类型
     */
    private String familyType;

    /**
     * 有效期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signDealLineTime;

    /**
     * 签约时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signStartTime;

    /**
     * 发起时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;


}
