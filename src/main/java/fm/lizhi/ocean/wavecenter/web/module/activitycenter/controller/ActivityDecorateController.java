package fm.lizhi.ocean.wavecenter.web.module.activitycenter.controller;


import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityDecorateService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.convert.ActivityDecorateConvert;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.BatchGetDecorateParamVO;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.DecorateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 装扮
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity/dressUp")
@Slf4j
public class ActivityDecorateController {

    @Autowired
    private ActivityDecorateService activityDecorateService;


    /**
     * 批量获取装扮
     */
    @GetMapping("/batchGetDressUp")
    @VerifyUserToken
    public ResultVO<List<DecorateVO>> batchGetDressUp(BatchGetDecorateParamVO param){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        RequestBatchGetDecorate request = RequestBatchGetDecorate.builder().appId(appId).ids(param.getIds()).type(param.getType()).build();
        Result<List<DecorateBean>> result = activityDecorateService.batchGetDecorateList(request);
        if (RpcResult.isFail(result)){
            log.warn("batch get activity decorate list fail. ids:{}, rCode:{}", request.getIds(), result.rCode());
            return ResultVO.failure(result.rCode(), result.getMessage());
        }
        return ResultVO.success(ActivityDecorateConvert.I.toDecorateVO(result.target()));
    }

}
