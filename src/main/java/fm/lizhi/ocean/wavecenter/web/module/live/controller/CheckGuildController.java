package fm.lizhi.ocean.wavecenter.web.module.live.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveGuildCheckInService;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.file.convert.FileConvert;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.DynamicColTable;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.StatsUser;
import fm.lizhi.ocean.wavecenter.web.module.income.convert.CheckInConvert;
import fm.lizhi.ocean.wavecenter.web.module.live.vo.check.*;
import fm.lizhi.ocean.wavecenter.web.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公会-打卡汇总
 *
 * <AUTHOR>
 * @date 2024/6/6 18:29
 * @deprecated 已迁移至 {@link CheckInTempController}
 */
@Slf4j
@PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
@RestController
@RequestMapping("live/check/guild")
@Deprecated
public class CheckGuildController {

    @Autowired
    private FileExportHandler fileExportHandler;

    @Autowired
    private LiveGuildCheckInService liveGuildCheckInService;


    /**
     * 公会打卡-厅打卡日统计
     *
     * @return
     */
    @VerifyUserToken
    @GetMapping("/room/day/stats")
    public ResultVO<CGCRoomDayStatsRes> roomDayStats(@Validated GuildRoomDayCheckStatsParamVo paramVo) {
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Integer pageNo = paramVo.getPageNo();
        Integer pageSize = paramVo.getPageSize();

        GuildRoomDayCheckStatsReq statsReq = GuildRoomDayCheckStatsReq.builder()
                .appId(appId)
                .endDate(dayEnd)
                .familyId(familyId)
                .startDate(dayStart)
                .pageNo(pageNo)
                .pageSize(pageSize)
                .build();
        Result<LGCSRoomDayStatsRes> result = liveGuildCheckInService.roomDayStats(statsReq);

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomDayStats error rCode={},statsReq={}", result.rCode(), JSONObject.toJSONString(statsReq));
            return ResultVO.failure("查询失败！");
        }
        List<GuildRoomDayStatsRes> roomDayStatsRes = result.target().getList();
        List<GuildRoomDayStatsResVo> guildRoomDayStatsResVos = roomDayStatsRes.stream().map(CheckInConvert.I::convertGuildRoomDayStatsResVo).collect(Collectors.toList());

        CGCRoomDayStatsRes res = new CGCRoomDayStatsRes();
        res.setTotal(result.target().getTotal());
        res.setList(guildRoomDayStatsResVos);
        res.setTimeStats(CheckInConvert.I.timeStatsBeans2Vos(result.target().getTimeStats()));
        res.setTimeStatsSum(CheckInConvert.I.timeStatsSumBean2Vo(result.target().getTimeStatSum()));

        return ResultVO.success(res);
    }


    /**
     * 公会打卡-厅打卡小时统计
     *
     * @return
     */
    @VerifyUserToken
    @GetMapping("/room/hour/stats")
    public ResultVO<CGCRoomHourStatsRes> roomHourStats(@Validated GuildRoomHourCheckStatsParamVo paramVo) {
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        GuildRoomHourCheckStatsReq statsReq = GuildRoomHourCheckStatsReq.builder()
                .appId(appId)
                .endDate(dayEnd)
                .startDate(dayStart)
                .familyId(familyId)
                .build();
        Result<LGCSRoomHourStatsRes> result = liveGuildCheckInService.roomHourStats(statsReq);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomHourStats error rCode={},statsReq={}", result.rCode(), JSONObject.toJSONString(statsReq));
            return ResultVO.failure("查询失败！");
        }

        List<GuildRoomHourStatsRes> guildRoomHourStatsRes = result.target().getList();
        List<GuildRoomHourStatsResVo> guildRoomHourStatsResVos = guildRoomHourStatsRes.stream().map(CheckInConvert.I::convertGuildRoomHourStatsVo).collect(Collectors.toList());
        //时间合计
        CGCRoomHourStatsRes res = new CGCRoomHourStatsRes();
        res.setTotal(guildRoomHourStatsResVos.size());
        res.setList(guildRoomHourStatsResVos);
        res.setTimeStats(CheckInConvert.I.timeStatsBeans2Vos(result.target().getTimeStats()));
        res.setTimeStatsSum(CheckInConvert.I.timeStatsSumBean2Vo(result.target().getTimeStatsSum()));
        return ResultVO.success(res);
    }


    /**
     * 公会打卡-厅打卡-天-汇总
     *
     * @return
     */
    @VerifyUserToken
    @GetMapping("/room/day/sum")
    public ResultVO<GuildRoomDayStatsSummaryVo> roomDaySum(@Validated GuildRoomCheckSummaryParamVo paramVo) {
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        GuildRoomDayCheckStatsReq statsReq = GuildRoomDayCheckStatsReq.builder()
                .startDate(dayStart)
                .endDate(dayEnd)
                .familyId(familyId)
                .appId(appId)
                .build();
        Result<GuildRoomDayStatsSummaryRes> result = liveGuildCheckInService.roomDayStatsSummary(statsReq);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomSum error rCode={},statsReq={}", result.rCode(), JSONObject.toJSONString(statsReq));
            return ResultVO.failure("查询失败！");
        }
        GuildRoomDayStatsSummaryRes summaryRes = result.target();
        GuildRoomDayStatsSummaryVo guildRoomDayStatsSummaryVo = CheckInConvert.I.convertGuildRoomDayStatsSummaryVo(summaryRes);
        return ResultVO.success(guildRoomDayStatsSummaryVo);
    }


    /**
     * 公会打卡-厅打卡小时汇总
     *
     * @return
     */
    @VerifyUserToken
    @GetMapping("/room/hour/sum")
    public ResultVO<GuildRoomHourStatsSummaryVo> roomSum(@Validated GuildRoomCheckSummaryParamVo paramVo) {
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        GuildRoomHourCheckStatsReq statsReq = GuildRoomHourCheckStatsReq.builder()
                .startDate(dayStart)
                .endDate(dayEnd)
                .familyId(familyId)
                .appId(appId)
                .build();
        Result<GuildRoomHourStatsSummaryRes> result = liveGuildCheckInService.roomHourStatsSummary(statsReq);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("roomSum error rCode={},statsReq={}", result.rCode(), JSONObject.toJSONString(statsReq));
            return ResultVO.failure("查询失败！");
        }
        GuildRoomHourStatsSummaryRes summaryRes = result.target();
        GuildRoomHourStatsSummaryVo guildRoomHourStatsSummaryVo = CheckInConvert.I.convertGuildRoomHourStatsSummaryVo(summaryRes);
        return ResultVO.success(guildRoomHourStatsSummaryVo);
    }


    /**
     * 公会-厅-打卡汇总-小时统计-导出
     *
     * @return
     */
    @VerifyUserToken
    @GetMapping("/room/hour/stats/export")
    public ResultVO<Void> roomHourStatsExport(@Validated CGCRoomHourStatsExportParamVo paramVo) {
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.putCol("档期");
        dynamicColTable.putCol("数据类型");

        return fileExportHandler.exportDynamicFile("厅日打卡汇总", dynamicColTable, (sheet, pageNo, pageSize) -> {

            GuildRoomHourCheckStatsReq.GuildRoomHourCheckStatsReqBuilder builder = GuildRoomHourCheckStatsReq.builder()
                    .appId(appId)
                    .familyId(familyId)
                    .startDate(dayStart)
                    .endDate(dayEnd);
            Result<LGCSRoomHourStatsRes> res = liveGuildCheckInService.roomHourStats(builder.build());
            if (RpcResult.isFail(res)) {
                log.warn("roomHourStats rpc fail. appId={}, familyId={}, startDate={}, endDate={}, rCode={}", appId, familyId, dayStart, dayEnd, res.rCode());
                return PageVO.empty();
            }
            List<GuildRoomHourStatsRes> rpcData = res.target().getList();

            //档期开始时间-厅-值
            Table<Date, String, String> incomeTable = HashBasedTable.create();
            Table<Date, String, String> charmTable = HashBasedTable.create();
            Table<Date, String, String> seatOrderTable = HashBasedTable.create();
            Table<Date, String, String> checkPlayerTable = HashBasedTable.create();

            for (GuildRoomHourStatsRes rpcDatum : rpcData) {
                List<GuildRoomHourDetail> hourDetails = rpcDatum.getDetail();
                RoomBean roomBean = rpcDatum.getRoom();
                String room = new StatsUser(roomBean.getName(), roomBean.getBand()).format();
                for (GuildRoomHourDetail hourDetail : hourDetails) {
                    Date time = hourDetail.getTime();
                    incomeTable.put(time, room, hourDetail.getIncome()== null? "0" : String.valueOf(hourDetail.getIncome()));
                    charmTable.put(time, room, hourDetail.getCharm() == null ? "0" : String.valueOf(hourDetail.getCharm()));
                    seatOrderTable.put(time, room, hourDetail.getSeatOrder() == null ? "0" : String.valueOf(hourDetail.getSeatOrder()));
                    checkPlayerTable.put(time, room, hourDetail.getCheckPlayerNumber() == null ? "0" : String.valueOf(hourDetail.getCheckPlayerNumber()));
                }
            }

            List<DynamicColTable.Row<Date>> statsList = new ArrayList<>();
            statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(incomeTable, "收入"));
            statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(charmTable, "魅力值"));
            statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(seatOrderTable, "麦序"));
            statsList.addAll(FileConvert.I.hourStatsTable2DynamicList(checkPlayerTable, "打开主播数"));

            //按照时间字段排序
            statsList.sort(Comparator.comparing(DynamicColTable.Row::getSortRow));
            return PageVO.of(0, statsList);
        });
    }

    /**
     * 公会-厅-打卡汇总-日统计-导出
     *
     * @param paramVo
     * @return
     */
    @VerifyUserToken
    @GetMapping("/room/day/stats/export")
    public ResultVO<Void> roomDayStatsExport(@Validated CGCRoomDayStatsExportParamVo paramVo) {
        Date dayStart = MyDateUtil.getDayStart(paramVo.getStartDate());
        Date dayEnd = MyDateUtil.getDayEnd(paramVo.getEndDate());

        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        DynamicColTable dynamicColTable = new DynamicColTable();
        dynamicColTable.putCol("时间");
        dynamicColTable.putCol("数据类型");
        return fileExportHandler.exportDynamicFile("厅小时打卡汇总", dynamicColTable, (sheet, pageNo, pageSize) -> {

            GuildRoomDayCheckStatsReq req = new GuildRoomDayCheckStatsReq();
            req.setStartDate(dayStart);
            req.setEndDate(dayEnd);
            req.setAppId(appId);
            req.setFamilyId(familyId);
            req.setPageNo(pageNo);
            req.setPageSize(pageSize);
            Result<LGCSRoomDayStatsRes> res = liveGuildCheckInService.roomDayStats(req);
            if (RpcResult.isFail(res)) {
                log.warn("roomDayStats rpc fail. appId={}, familyId={}, startDate={}, endDate={}, rCode={}", appId, familyId, dayStart, dayEnd, res.rCode());
                return PageVO.empty();
            }
            List<GuildRoomDayStatsRes> rpcData = res.target().getList();

            //日期-厅-值
            Table<Date, String, String> incomeTable = HashBasedTable.create();
            Table<Date, String, String> charmTable = HashBasedTable.create();
            Table<Date, String, String> seatOrderTable = HashBasedTable.create();
            Table<Date, String, String> checkPlayerTable = HashBasedTable.create();

            for (GuildRoomDayStatsRes rpcDatum : rpcData) {
                List<GuildRoomDayDetail> dayDetails = rpcDatum.getDetail();
                RoomBean roomBean = rpcDatum.getRoom();
                String room = new StatsUser(roomBean.getName(), roomBean.getBand()).format();
                for (GuildRoomDayDetail dayDetail : dayDetails) {
                    incomeTable.put(dayDetail.getTime(), room, dayDetail.getIncome()== null ? "0" : String.valueOf(dayDetail.getIncome()));
                    charmTable.put(dayDetail.getTime(), room, dayDetail.getCharm() == null ? "0" : String.valueOf(dayDetail.getCharm()));
                    seatOrderTable.put(dayDetail.getTime(), room, dayDetail.getSeatOrder() == null ? "0" : String.valueOf(dayDetail.getSeatOrder()));
                    checkPlayerTable.put(dayDetail.getTime(), room, dayDetail.getCheckPlayerNumber() == null ? "0" : String.valueOf(dayDetail.getCheckPlayerNumber()));
                }
            }

            List<DynamicColTable.Row<Date>> statsList = new ArrayList<>();
            statsList.addAll(FileConvert.I.dayStatsTable2DynamicList(incomeTable, "收入"));
            statsList.addAll(FileConvert.I.dayStatsTable2DynamicList(charmTable, "魅力值"));
            statsList.addAll(FileConvert.I.dayStatsTable2DynamicList(seatOrderTable, "麦序"));
            statsList.addAll(FileConvert.I.dayStatsTable2DynamicList(checkPlayerTable, "打开主播数"));

            //按照时间字段排序
            statsList.sort(Comparator.comparing(DynamicColTable.Row::getSortRow));
            return PageVO.of(res.target().getTotal(), statsList);
        });
    }

}
