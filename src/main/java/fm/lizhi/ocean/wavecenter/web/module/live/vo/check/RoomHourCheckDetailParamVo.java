package fm.lizhi.ocean.wavecenter.web.module.live.vo.check;


import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoomHourCheckDetailParamVo {

    @NotNull(message = "厅不可为空")
    private Long roomId;

    private Long playerId;

    /**
     * 开始时间 yyyy-MM-dd
     */
    @NotBlank(message = "开始时间不可为空")
    private String startDate;

    /**
     * 结束时间 yyyy-MM-dd
     */
    @NotBlank(message = "结束时间不可为空")
    private String endDate;
}
