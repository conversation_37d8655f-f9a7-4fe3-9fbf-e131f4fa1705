package fm.lizhi.ocean.wavecenter.web.module.home.result;


import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.EchelonVo;
import fm.lizhi.ocean.wavecenter.web.module.home.vo.MetricsDataVO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 厅关键数据汇总
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RoomKeyDataSummaryResult {


    /**
     * 厅总收入
     */
    private MetricsDataVO sumIncome;

    /**
     * 考核流水
     */
    private EchelonVo examinationFlow;

    /**
     * 距离下一梯队
     */
    private EchelonVo nextEchelon;

    /**
     * 厅上麦主播数
     */
    private MetricsDataVO signUpGuestPlayerCnt;

    /**
     * 有收入主播数
     */
    private MetricsDataVO incomePlayerCnt;
}
