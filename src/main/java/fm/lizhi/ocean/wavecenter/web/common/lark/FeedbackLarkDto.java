package fm.lizhi.ocean.wavecenter.web.common.lark;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/6/14 20:24
 */
@Data
@Accessors(chain = true)
public class FeedbackLarkDto {

    private Long userId;

    private String band;

    private String nickName;

    private String appName;

    private Integer typeId;

    private String content;

    private Integer star;

    private String contact;

    /**
     * 反馈的文件附件
     */
    private String fileListStr;

    /**
     * web意见反馈  推荐评分
     */
    private String source;

}
