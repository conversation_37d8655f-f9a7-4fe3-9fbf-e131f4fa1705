package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo;

import lombok.Data;

/**
 * 离线区域数据监控-汇总-公会响应结果
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class OfflineZoneFamilySummaryVo {

    /**
     * 基地数
     */
    private OfflineZoneMetricsDataVo basicCnt;

    /**
     * 线下厅数
     */
    private OfflineZoneMetricsDataVo offlineHallCnt;

    /**
     * 线下主播数
     */
    private OfflineZoneMetricsDataVo offlinePlayerCnt;

    /**
     * 线下厅数占比
     */
    private OfflineZoneMetricsDataVo offlineHallCntRate;

    /**
     * 线下主播数占比
     */
    private OfflineZoneMetricsDataVo offlinePlayerCntRate;

    /**
     * 线下厅收入
     */
    private OfflineZoneMetricsDataVo offlineHallIncome;

    /**
     * 线下厅收入占比
     */
    private OfflineZoneMetricsDataVo offlineHallIncomeRate;

    /**
     * 受保护主播数
     */
    private OfflineZoneMetricsDataVo protectedPlayerCnt;

    /**
     * 受保护主播数占比
     */
    private OfflineZoneMetricsDataVo protectedPlayerCntRate;
}
