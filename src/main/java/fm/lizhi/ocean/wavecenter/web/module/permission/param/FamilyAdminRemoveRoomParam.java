package fm.lizhi.ocean.wavecenter.web.module.permission.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/11/25 19:55
 */
@Data
public class FamilyAdminRemoveRoomParam {

    /**
     * 授权配置id
     */
    @NotNull(message = "配置ID不能为空")
    private Long configId;

    /**
     * 授权厅id
     */
    @NotNull(message = "厅id不能为空")
    private Long roomId;

}
