package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/5 16:24
 */
@Data
public class TodoSignPlayerVo {

    /**
     * 合同ID playerSignId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

    /**
     * 主播信息
     */
    private UserVo playerInfo;

    /**
     * 厅主信息
     */
    private UserVo roomInfo;

    /**
     * 类型
     * SIGN=签约，CANCEL=解约
     */
    private String signType;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否本人发起,即管理员
     */
    private boolean isSelfCreate;

    /**
     * 签署有效期（解约的自动解约时间） 签署截止时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signDeadline;

    /**
     * 签约生效开始时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date oldStartTime;

    /**
     * 签约生效结束时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date endTime;

    /**
     * 解约时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date stopTime;

    /**
     * 签约申请发起时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

}
